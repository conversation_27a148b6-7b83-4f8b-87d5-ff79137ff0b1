INCLUDEPATH += $$PWD
DEPENDPATH += $$PWD

contains (CONFIG, ENABLE_STREAMDECK) {
    message("LibGoal-devices: Stream Deck native support enabled... [ YES ] | Dependencies: libhidapi-libusb")
    DEFINES += GOALSPORT_USE_STREAMDECK
    LIBS += -lhidapi-libusb

    SOURCES += \
    $$PWD/include/StreamDeck/streamdeckcontroller.cpp \
    $$PWD/include/StreamDeck/streamdeckdevice.cpp

    HEADERS += \
    $$PWD/include/StreamDeck/streamdeckcontroller.h \
    $$PWD/include/StreamDeck/streamdeckdevice.h
} else {
    message("LibGoal-devices: Stream Deck native support enabled... [ NO ] | Enable with CONFIG += ENABLE_STREAMDECK")
}

contains (CONFIG, ENABLE_XKEYS) {
    message("LibGoal-devices: XKeys enabled... [ YES ] | Dependencies: XKeys SDK piehid")
    DEFINES += GOALSPORT_USE_XKEYS
    LIBS += -lpiehid

    SOURCES += \
    $$PWD/include/XKeys/xkeyspiecontroller.cpp

    HEADERS += \
    $$PWD/include/XKeys/xkeyspiecontroller.h
} else {
    message("LibGoal-devices: XKeys enabled... [ NO ] | Enable with CONFIG += ENABLE_XKEYS")
}

SOURCES +=  \
    $$PWD/include/UdpMsg/udpmessagereader.cpp \
    $$PWD/include/UdpMsg/udpmessagesender.cpp \
    $$PWD/include/VideoSwitcher/ajakumocontroler.cpp \
    $$PWD/include/VideoSwitcher/aspencontroller.cpp \
    $$PWD/include/VideoSwitcher/bmvideohubcontroller.cpp \
    $$PWD/include/VideoSwitcher/bmvideohubswitcher.cpp \
    $$PWD/include/VideoSwitcher/lightwaremxseriescontroller.cpp \
    $$PWD/include/VideoSwitcher/novacontrollerbase.cpp \
    $$PWD/include/VideoSwitcher/novahseriescontroller.cpp \
    $$PWD/include/VideoSwitcher/novamctrl4kcontroller.cpp \
    $$PWD/include/VideoSwitcher/novamctrl660procontroller.cpp \
    $$PWD/include/VideoSwitcher/novaprouhdjrcontroller.cpp \
    $$PWD/include/VideoSwitcher/novavxseries.cpp


HEADERS +=  \
    $$PWD/include/UdpMsg/udpmessagereader.h \
    $$PWD/include/UdpMsg/udpmessagesender.h \
    $$PWD/include/VideoSwitcher/ajakumocontroler.h \
    $$PWD/include/VideoSwitcher/aspencontroller.h \
    $$PWD/include/VideoSwitcher/bmvideohubcontroller.h \
    $$PWD/include/VideoSwitcher/bmvideohubswitcher.h \
    $$PWD/include/VideoSwitcher/lightwaremxseriescontroller.h \
    $$PWD/include/VideoSwitcher/novacontrollerbase.h \
    $$PWD/include/VideoSwitcher/novahseriescontroller.h \
    $$PWD/include/VideoSwitcher/novamctrl4kcontroller.h \
    $$PWD/include/VideoSwitcher/novamctrl660procontroller.h \
    $$PWD/include/VideoSwitcher/novaprouhdjrcontroller.h \
    $$PWD/include/VideoSwitcher/novavxseries.h \
    $$PWD/include/VideoSwitcher/videomatrixswitcherinterface.h
