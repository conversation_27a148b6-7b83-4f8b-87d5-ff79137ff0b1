#include "lightwaremxseriescontroller.h"

LightwareMXseriesController::LightwareMXseriesController(QString deviceAddr, int port, QObject *parent) :
    QObject(parent),
    mDeviceAddr(deviceAddr),
    mTcpPort(port)
{
    connect(&mSocket, &QAbstractSocket::connected, this, [this](){
        qInfo() << "Lightware MX: Device connected. Addr:" << mDeviceAddr << "port:" << mTcpPort;
        processQueuedRequests();  //process requests issued before socket gets connected
    });
    connect(&mSocket, &QAbstractSocket::disconnected, this, [this](){
        qInfo() << "Lightware MX: Device disconnected. Addr:" << mDeviceAddr << "port:" << mTcpPort;
    });
    connect(&mSocket, &QAbstractSocket::readyRead, this, &LightwareMXseriesController::processReplyData);

    mReplyTimer.setSingleShot(true);
    mReplyTimer.setInterval(200);
    connect(&mReplyTimer, &QTimer::timeout, this, &LightwareMXseriesController::replyTimeout);

    mAutoDisconnectTimer.setSingleShot(true);
    mAutoDisconnectTimer.setTimerType(Qt::VeryCoarseTimer);
    mAutoDisconnectTimer.setInterval(3000);
    connect(&mAutoDisconnectTimer, &QTimer::timeout, this, &LightwareMXseriesController::disconnectDevice);
}

LightwareMXseriesController::~LightwareMXseriesController()
{
    disconnectDevice();
}

void LightwareMXseriesController::connectToDevice()
{
    if(mSocket.state() == QTcpSocket::UnconnectedState){
        qInfo() << "Lightware MX: Connecting to the device... Addr:" << mDeviceAddr << "port:" << mTcpPort;
        mSocket.connectToHost(mDeviceAddr, mTcpPort);
    }

    if(mAutoDisconnectEnabled){
        QMetaObject::invokeMethod(&mAutoDisconnectTimer, qOverload<>(&QTimer::start));
    }
}

void LightwareMXseriesController::disconnectDevice()
{
    qInfo() << "Lightware MX: Disconnecting the device. Clear pending requests:" << mReqQueue.count();
    QMetaObject::invokeMethod(&mReplyTimer, &QTimer::stop);
    QMetaObject::invokeMethod(&mAutoDisconnectTimer, &QTimer::stop);
    mSocket.disconnectFromHost();

    //clear all pending requests
    mPendingRequest = NoRequest;
    mReqQueue.clear();
}

void LightwareMXseriesController::changeConnection(int inputNumber, int outpuNumber)
{
    sendRequest(QString("{%1@%2}").arg(inputNumber).arg(outpuNumber).toLatin1(), RequestType::SwitchCommand);
}

void LightwareMXseriesController::setAutoDisconnectModeEnabled(bool enabled)
{
    mAutoDisconnectEnabled = enabled;
    if(mAutoDisconnectEnabled){
        mAutoDisconnectTimer.start();
    } else {
        mAutoDisconnectTimer.stop();
    }
}

void LightwareMXseriesController::sendRequest(const QByteArray &rawReqData, RequestType type)
{
    mReqQueue.enqueue({rawReqData, type});
    processQueuedRequests();

    if(mAutoDisconnectEnabled){
        QMetaObject::invokeMethod(&mAutoDisconnectTimer, qOverload<>(&QTimer::start));
    }
}

void LightwareMXseriesController::processReplyData()
{
    QByteArray receivedData = mSocket.readAll();
    if(!receivedData.isEmpty()){
        QMetaObject::invokeMethod(&mReplyTimer, &QTimer::stop);
        QList<QByteArray> responseList = receivedData.split('\n');
        for(auto r : responseList){
            QByteArray response = r.trimmed();  //remove whitespaces from begining and end
            if(response.startsWith('(') && response.endsWith(')')){
                response = response.mid(1, response.size()-2);  //response is surrounded with brackets (), cut them off
                switch (mPendingRequest) {
                case RequestType::GetDeviceSerial:
                    mDeviceInfo.serialNumber = QString::fromLatin1(response);
                    printDeviceInfo();
                    break;
                case RequestType::GetDeviceType:
                    mDeviceInfo.model = QString::fromLatin1(response);
                    printDeviceInfo();
                    break;
                case RequestType::SwitchCommand:
                    parseSwitchResponse(response);
                    break;
                default:
                    break;
                }
            }
            mPendingRequest = NoRequest;
        }
    }

    processQueuedRequests();
}

void LightwareMXseriesController::replyTimeout()
{
    if(mPendingRequest != NoRequest){
        qWarning() << "Lightware MX: Device reply timed out. Requested command type:" << mPendingRequest << "| Addr:" << mDeviceAddr << "| port:" << mTcpPort;
        mPendingRequest = NoRequest;
    }
    processQueuedRequests();
}

void LightwareMXseriesController::processQueuedRequests()
{
    if(mPendingRequest != NoRequest){
        return;  //still waiting for previous request reply
    }

    if(mReqQueue.isEmpty()){
        return;
    }

    if(mSocket.state() == QTcpSocket::UnconnectedState){
        connectToDevice();  //lazy auto connect
    }

    if(mSocket.isValid() && mSocket.state() == QTcpSocket::ConnectedState){
        const RequestCommand &rc = mReqQueue.head();
        qint64 written = mSocket.write(rc.commandData);
        if(written == rc.commandData.size()){  //TODO - test this
            mPendingRequest = rc.type;
            mReqQueue.dequeue();
        } else if(written < 0){
            //failed to write to the socket - try to reconnect
            qCritical() << "Lightware MX: Failed to write command data to the socket. Trying to reconnect." << "| Addr:" << mDeviceAddr << "| port:" << mTcpPort;
            mSocket.abort();
            mSocket.disconnectFromHost();
        } else {
            qWarning() << "Lightware MX: Failed to write all command data to the socket. Will resend." << written << "of" << rc.commandData.size() << "bytes written." << "| Addr:" << mDeviceAddr << "| port:" << mTcpPort;
        }

        //start reply timer always (even on error) to try send failed command again
        QMetaObject::invokeMethod(&mReplyTimer, qOverload<>(&QTimer::start));
    }
}

void LightwareMXseriesController::printDeviceInfo(bool completeInfoOnly)
{
    if(!completeInfoOnly || (!mDeviceInfo.model.isNull() && !mDeviceInfo.serialNumber.isNull())){
        qInfo() << "Lightware MX: Device Addr:" << mDeviceAddr
                << "| Model:" << (mDeviceInfo.model.isNull() ? "--" : mDeviceInfo.model)
                << "|" << mDeviceInfo.serialNumber;
    }
}

void LightwareMXseriesController::parseSwitchResponse(const QByteArray &response)
{
    QString resStr = QString::fromLatin1(response);
    int outNum, inNum = -1;
    //2-digit ASCII number
    outNum = resStr.mid(resStr.indexOf('O')+1, 2).toInt();
    inNum = resStr.mid(resStr.indexOf('I')+1, 2).toInt();
    if(outNum > 0 && inNum > 0){
        emit connectionChanged(inNum, outNum);
    }
    qInfo() << "Lightware MX: Inputs switched. Device reply:" << resStr << "Parsed as (IN-OUT):" << inNum << "-" << outNum;
}
