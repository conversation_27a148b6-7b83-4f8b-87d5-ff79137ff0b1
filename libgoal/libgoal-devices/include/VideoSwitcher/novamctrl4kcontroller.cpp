#include "novamctrl4kcontroller.h"

namespace  {
    const QString devName = "Nova MCTRL 4K";
    const QByteArray connectCommand{QByteArray::fromHex("55 aa 00 00 fe 00 00 00 00 00 00 00 02 00 00 00 02 00 57 56")};
    const QByteArray connectReply{QByteArray::fromHex("aa 55 00 00 00 fe 00 00 00 00 00 00 02 00 00 00 02 00 03 11 6b 56")};
    const QByteArray changeConnectionCommandSkelet{QByteArray::fromHex("55 aa 00 00 fe ff 00 00 00 00 01 00 23 00 00 02 01 00 00 00 58")};
    const QByteArray replyOKbegining{QByteArray::fromHex("aa 55 00")};
}

NovaMctrl4kController::NovaMctrl4kController(QString devAddr, int portNum) :
    NovaControllerBase(devAddr, portNum, devName)
{}

QByteArray NovaMctrl4kController::getConnectCommand() const
{
    return connectCommand;
}

bool NovaMctrl4kController::checkConnectReply(const QByteArray &receivedData)
{
    return receivedData.startsWith(connectReply);
}

QByteArray NovaMctrl4kController::buildSwitchCommand(int srcNum, [[maybe_unused]] int dstNum) const
{
    QByteArray arrToSend = changeConnectionCommandSkelet;
    switch(srcNum){
    case 1:  //DP
        arrToSend[3] = '\x9d';
        arrToSend[18] = '\x5f';
        arrToSend[19] = '\x75';
        break;
    case 2:  //HDMI
        arrToSend[3] = '\x8a';
        arrToSend[18] = '\x05';
        arrToSend[19] = '\x08';
        break;
    case 3:  //DVI
        arrToSend[3] = '\x3e';
        arrToSend[18] = '\x61';
        arrToSend[19] = '\x18';
        break;
    default:
        arrToSend.clear();
        break;
    }
    return arrToSend;
}

bool NovaMctrl4kController::checkSwitchReply(const QByteArray &receivedData)
{
    bool ok = false;
    if(receivedData.startsWith(replyOKbegining) && receivedData.size() >= 4){
        switch(receivedData[3]){
        case '\x9d':  //OK - DP
        case '\x8a':  //OK - HDMI
        case '\x3e':  //OK - DVI
            ok = true;
            break;
        }
    }
    return ok;
}

