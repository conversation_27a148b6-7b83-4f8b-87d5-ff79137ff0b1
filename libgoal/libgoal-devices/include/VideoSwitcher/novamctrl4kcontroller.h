#ifndef NOVAMCTRL4KCONTROLLER_H
#define NOVAMCTRL4KCONTROLLER_H

#include "novacontrollerbase.h"

/**
 * @brief The NovaMctrl4kController class controls input sources on LED sending card
 * NovaStart MCTRL 4K. It uses TCP/IP connection to device acording to the protocol.
 *
 * Input numbers corresponds to inputs on device as folows:
 * DP:      1
 * HDMI:    2
 * DVI:     3
 *
 * Output number is ignored as the MCTRL 4K device has only one output.
 */

class NovaMctrl4kController : public NovaControllerBase
{
    Q_OBJECT
public:
    explicit NovaMctrl4kController(QString devAddr, int portNum = defaultPort());

    QByteArray getConnectCommand() const;
    bool checkConnectReply(const QByteArray &receivedData);
    QByteArray buildSwitchCommand(int srcNum, int dstNum) const;
    bool checkSwitchReply(const QByteArray &receivedData);
};

#endif // NOVAMCTRL4KCONTROLLER_H
