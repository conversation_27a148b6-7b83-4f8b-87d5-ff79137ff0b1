#ifndef LIGHTWAREMXSERIESCONTROLLER_H
#define LIGHTWAREMXSERIESCONTROLLER_H

#include <QObject>
#include "videomatrixswitcherinterface.h"
#include <QTcpSocket>
#include <QQueue>
#include <QTimer>

class LightwareMXseriesController : public QObject, public VideoMatrixSwitcherInterface
{
    Q_OBJECT
    Q_INTERFACES(VideoMatrixSwitcherInterface)

    struct DeviceInfo {
        QString model;
        QString serialNumber;
    };

    enum RequestType {
        NoRequest = 0,
        UnknownCommand,
        SwitchCommand,
        GetDeviceType,
        GetDeviceSerial
    };

    struct RequestCommand {
        QByteArray commandData;
        RequestType type = UnknownCommand;
    };

public:
    explicit LightwareMXseriesController(QString deviceAddr, int port = defaultPort(), QObject *parent = nullptr);
    ~LightwareMXseriesController();

    static int defaultPort() { return 10001; }

    void changeConnection(int inputNumber, int outpuNumber);
    /**
     * @brief setAutoDisconnectModeEnabled Auto disconnect mode is useful when connecting to the device from more
     * than one computer. <PERSON><PERSON> accepts just one connection.
     * Auto-disconnect mode is enabled by default. Disconnects 3 seconds after last connect or command sent.
     * @param enabled Enable or disable auto disconnect mode.
     */
    void setAutoDisconnectModeEnabled(bool enabled);

public slots:
    void connectToDevice();
    void disconnectDevice();

    /**
     * @brief sendRequest Send raw request to device, see Lightware MX device User manual for reference.
     * @param rawReqData - ASCII payload data, UPPER CASE command letters, command surrounded with curly brackets {}
     * @param type - Type of request, set as pending request as a hint to parse reply from device
     */
    void sendRequest(const QByteArray &rawReqData, RequestType type = RequestType::UnknownCommand);

    void querryDeviceModel() { sendRequest(QString("{I}").toLatin1(), RequestType::GetDeviceType); }
    void querryDeviceSerialNumber()  { sendRequest(QString("{S}").toLatin1(), RequestType::GetDeviceSerial); }

signals:
    void connectionChanged(int inputNumber, int outputNumber);

private slots:
    void processReplyData();
    void replyTimeout();

private:
    void processQueuedRequests();
    void printDeviceInfo(bool completeInfoOnly = true);
    void parseSwitchResponse(const QByteArray &response);

    QString mDeviceAddr;
    int mTcpPort = 0;
    QTcpSocket mSocket;
    DeviceInfo mDeviceInfo;
    RequestType mPendingRequest = NoRequest;  //type of last request sent and is waiting for reply
    QQueue<RequestCommand> mReqQueue;
    QTimer mReplyTimer;
    bool mAutoDisconnectEnabled = true;  //Enabled by default. Disconnects few seconds after commands are sent
    QTimer mAutoDisconnectTimer;
};

#endif // LIGHTWAREMXSERIESCONTROLLER_H
