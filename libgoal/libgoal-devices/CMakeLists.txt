cmake_minimum_required(VERSION 3.10)

project(GSS_libgoal_devices VERSION 0.1 LANGUAGES CXX)
libGoalProject(GSS_libgoal_devices)

option(DEVICES_ENABLE_STREAMDECK "Enable Stream Deck native support" OFF)
option(DEVICES_ENABLE_XKEYS "Enable XKeys support" ON)

find_package(QT NAMES Qt6 Qt5)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Network)

add_library(${PROJECT_NAME} STATIC)

if(NOT WIN32)
    target_sources(${PROJECT_NAME} PRIVATE        
        include/VideoSwitcher/videomatrixswitcherinterface.h
        include/VideoSwitcher/ajakumocontroler.h 
        include/VideoSwitcher/aspencontroller.h  
        include/VideoSwitcher/bmvideohubcontroller.h  
        include/VideoSwitcher/bmvideohubswitcher.h    
        include/VideoSwitcher/novacontrollerbase.h  
        include/VideoSwitcher/novamctrl660procontroller.h   
        include/VideoSwitcher/novaprouhdjrcontroller.h
        include/VideoSwitcher/novamctrl4kcontroller.h
        include/VideoSwitcher/novahseriescontroller.h
        include/VideoSwitcher/novavxseries.h
        include/VideoSwitcher/lightwaremxseriescontroller.h

        include/VideoSwitcher/ajakumocontroler.cpp 
        include/VideoSwitcher/aspencontroller.cpp  
        include/VideoSwitcher/bmvideohubcontroller.cpp 
        include/VideoSwitcher/bmvideohubswitcher.cpp 
        include/VideoSwitcher/novacontrollerbase.cpp
        include/VideoSwitcher/novamctrl660procontroller.cpp 
        include/VideoSwitcher/novaprouhdjrcontroller.cpp 
        include/VideoSwitcher/novamctrl4kcontroller.cpp
        include/VideoSwitcher/novahseriescontroller.cpp
        include/VideoSwitcher/novavxseries.cpp
        include/VideoSwitcher/lightwaremxseriescontroller.cpp
    )
endif()

if (DEVICES_ENABLE_STREAMDECK)
    if(NOT WIN32)
        find_package(PkgConfig REQUIRED)
        pkg_check_modules(hidapi-libusb REQUIRED IMPORTED_TARGET hidapi-libusb)

        target_sources(${PROJECT_NAME} PRIVATE
            include/StreamDeck/streamdeckcontroller.h    
            include/StreamDeck/streamdeckdevice.h

            include/StreamDeck/streamdeckcontroller.cpp  
            include/StreamDeck/streamdeckdevice.cpp
        )

        target_link_libraries(${PROJECT_NAME} PUBLIC
            PkgConfig::hidapi-libusb
            Qt${QT_VERSION_MAJOR}::Gui
        )
    endif()
endif()

if (DEVICES_ENABLE_XKEYS)
    target_sources(${PROJECT_NAME} PRIVATE
        include/XKeys/xkeyspiecontroller.cpp  
        include/XKeys/xkeyspiecontroller.h
    )

    if (WIN32)
        target_include_directories(${PROJECT_NAME} PUBLIC
            ${CMAKE_CURRENT_LIST_DIR}/include/win64includes/piehid-win64/include
        )

        target_link_directories(${PROJECT_NAME} PUBLIC
            ${CMAKE_CURRENT_LIST_DIR}/include/win64includes/piehid-win64/lib
        )

        target_link_libraries(${PROJECT_NAME} PUBLIC
            piehid
        )

    else()
        target_link_libraries(${PROJECT_NAME} PUBLIC
            -lpiehid
        )
    endif()

endif()

target_sources(${PROJECT_NAME} PRIVATE
    include/UdpMsg/udpmessagesender.h
    include/UdpMsg/udpmessagesender.cpp
    include/UdpMsg/udpmessagereader.h
    include/UdpMsg/udpmessagereader.cpp
)

add_library(GSS::libgoal::devices ALIAS ${PROJECT_NAME})

if (NOT ${LIBGOAL_STANDALONE_BUILD})
    set_target_properties(${PROJECT_NAME} PROPERTIES EXCLUDE_FROM_ALL TRUE)
    message(STATUS "removed target ${PROJECT_NAME} from ALL")
endif()

target_include_directories(${PROJECT_NAME} PUBLIC
    ${CMAKE_CURRENT_LIST_DIR}
    ${CMAKE_CURRENT_LIST_DIR}/include
)

target_link_libraries(${PROJECT_NAME} PUBLIC
# External
    Qt${QT_VERSION_MAJOR}::Network
)

