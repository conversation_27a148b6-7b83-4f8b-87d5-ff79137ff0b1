#ifndef AUDIOLOOP_H
#define AUDIOLOOP_H

#include "audiodevicelistener.h"
#include "audioutils.h"
#include "nodes/audiointerfaceinfo.h"

#include <QObject>

#include <pipewire/context.h>
#include <pipewire/core.h>
#include <pipewire/thread-loop.h>

namespace LibGoal::Audio
{

class RegistryListener;
class AudioDeviceListener;

/**
 * @brief The AudioLoop class is responsible for encapsulating PipeWire context and core objects.
 * Each interaction with the audio loop needs to be prefixed with locking of the PipeWire thread loop (done via the AudioLoopLocker RAII object).
 */
class AudioLoop : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Creates a new AudioLoop instance. Make sure pw_init is called before creating this object.
     * The pw_deinit has to be called when exiting the application, AFTER all AudioLoop instances are destroyed.
     * @param parent QObject parent
     */
    explicit AudioLoop(QObject *parent = nullptr);
    ~AudioLoop() override;

    pw_core *getAudioLoopCore() const;
    pw_registry *getAudioLoopRegistry() const;
    Utils::AudioLoopLocker getAudioLoopLocker() const;
    AudioDeviceListener *getDeviceListener() const;

Q_SIGNALS:
    /**
     * @brief Emitted when the default audio output changes
     * @param output the new default audio output
     */
    void defaultOutputChanged(const AudioInterfaceInfo &output);

    /**
     * @brief Emitted when the available audio output devices change
     * @param outputDevices the current audio output devices
     * @param defaultOutput the current default audio output
     */
    void availableOutputDevicesChanged(const QList<AudioInterfaceInfo> &outputDevices, const AudioInterfaceInfo &defaultOutput);

private:
    pw_thread_loop *m_pwThreadLoop = nullptr;
    pw_context *m_pwContext = nullptr;
    pw_core *m_pwCore = nullptr;
    pw_registry *m_registry = nullptr;

    AudioDeviceListener *m_outputDeviceListener = nullptr;
};
} // namespace LibGoal::Audio

#endif // AUDIOLOOP_H
