#include "audiodevicelistener.h"

#include "audioloop.h"
#include "audionodeportmanager.h"
#include "portinfo.h"

#include <pipewire/impl-metadata.h>
#include <pipewire/keys.h>
#include <pipewire/node.h>
#include <pipewire/type.h>

#include <QJsonDocument>
#include <QJsonObject>

LibGoal::Audio::AudioDeviceListener::AudioDeviceListener(AudioLoop *audioLoop, QObject *parent)
    : QObject{parent}
    , m_audioLoop{audioLoop}
    , m_deviceListener{new RegistryListener{this, onGlobalListener, removedListener, audioLoop, this}}
{
}

LibGoal::Audio::AudioDeviceListener::~AudioDeviceListener()
{
    for (auto &hook : m_metadataHooks) {
        spa_hook_remove(&hook);
    }
    m_metadataHooks.clear();

    for (const auto &metadata : m_metadataObjects) {
        pw_proxy_destroy(reinterpret_cast<pw_proxy *>(metadata));
    }
    m_metadataObjects.clear();
}

QMap<uint32_t, LibGoal::Audio::AudioInterfaceInfo> LibGoal::Audio::AudioDeviceListener::getOutputDevices() const
{
    return m_outputDevices;
}

LibGoal::Audio::AudioInterfaceInfo LibGoal::Audio::AudioDeviceListener::getDefaultOutput() const
{
    return m_defaultOutput;
}

QMap<uint32_t, LibGoal::Audio::AudioInterfaceInfo> LibGoal::Audio::AudioDeviceListener::getInputDevices() const
{
    return m_inputDevices;
}

LibGoal::Audio::AudioInterfaceInfo LibGoal::Audio::AudioDeviceListener::getDefaultInput() const
{
    return m_defaultInput;
}

QMap<uint32_t, QList<LibGoal::Audio::PortInfo>> LibGoal::Audio::AudioDeviceListener::getPorts() const
{
    return m_ports;
}

void LibGoal::Audio::AudioDeviceListener::onGlobalListener(void *data, const uint32_t id, const uint32_t permissions, const char *type,
                                                           const uint32_t version, const spa_dict *props)
{
    deviceListener(data, id, permissions, type, version, props);
    metadataListener(data, id, permissions, type, version, props);
    portListener(data, id, permissions, type, version, props);
}

void LibGoal::Audio::AudioDeviceListener::deviceListener(void *audioDeviceListenerPtr, const uint32_t id, [[maybe_unused]] uint32_t permissions,
                                                         [[maybe_unused]] const char *type, [[maybe_unused]] uint32_t version, const spa_dict *props)
{
    if (strcmp(type, PW_TYPE_INTERFACE_Node) != 0) {
        return;
    }

    const char *mediaClass = spa_dict_lookup(props, PW_KEY_MEDIA_CLASS);
    if (mediaClass == nullptr || strcmp(mediaClass, AUDIO_SINK_MEDIA_CLASS) != 0 && strcmp(mediaClass, AUDIO_SOURCE_MEDIA_CLASS) != 0) {
        return;
    }

    const QString name = spa_dict_lookup(props, PW_KEY_NODE_NAME);
    const QString description = spa_dict_lookup(props, PW_KEY_NODE_DESCRIPTION);
    const QString nick = spa_dict_lookup(props, PW_KEY_NODE_NICK);

    AudioInterfaceInfo output;
    output.nodeId = id;
    output.name = name;
    output.description = description;
    output.nick = nick;

    auto *audioDeviceListener = static_cast<AudioDeviceListener *>(audioDeviceListenerPtr);
    if (strcmp(mediaClass, AUDIO_SINK_MEDIA_CLASS) == 0) {
        audioDeviceListener->m_outputDevices[output.nodeId] = output;
        audioDeviceListener->signalOutputDevicesChanged();
        qInfo() << "Audio Output Device added:" << output.toString();
    } else if (strcmp(mediaClass, AUDIO_SOURCE_MEDIA_CLASS) == 0) {
        audioDeviceListener->m_inputDevices[output.nodeId] = output;
        audioDeviceListener->signalInputDevicesChanged();
        qInfo() << "Audio Input Device added:" << output.toString();
    }
}

void LibGoal::Audio::AudioDeviceListener::metadataListener(void *audioDeviceListenerPtr, const uint32_t id, [[maybe_unused]] uint32_t permissions,
                                                           const char *type, const uint32_t version, [[maybe_unused]] const spa_dict *props)
{
    if (strcmp(type, PW_TYPE_INTERFACE_Metadata) != 0) {
        return;
    }

    auto *audioDeviceListener = static_cast<AudioDeviceListener *>(audioDeviceListenerPtr);
    auto *metadata = static_cast<struct pw_metadata *>(pw_registry_bind(
        audioDeviceListener->m_audioLoop->getAudioLoopRegistry(), id, type, version, sizeof(audioDeviceListenerPtr)));

    if (metadata == nullptr) {
        qWarning() << "Failed to bind metadata for id:" << id;
        return;
    }

    if (audioDeviceListener->m_metadataObjects.contains(id)) {
        pw_proxy_destroy(reinterpret_cast<pw_proxy *>(audioDeviceListener->m_metadataObjects[id]));
        audioDeviceListener->m_metadataObjects.remove(id);
    }

    audioDeviceListener->m_metadataObjects[id] = metadata;

    if (audioDeviceListener->m_metadataHooks.contains(id)) {
        spa_hook_remove(&audioDeviceListener->m_metadataHooks[id]);
        audioDeviceListener->m_metadataHooks.remove(id);
    }

    audioDeviceListener->m_metadataHooks[id] = {};

    pw_metadata_add_listener(metadata, &audioDeviceListener->m_metadataHooks[id], &metadata_events, audioDeviceListenerPtr);
}

void LibGoal::Audio::AudioDeviceListener::portListener(void *audioDeviceListenerPtr,
                                                       const uint32_t id,
                                                       [[maybe_unused]] const uint32_t permissions,
                                                       const char *type,
                                                       [[maybe_unused]] const uint32_t version,
                                                       const spa_dict *props)
{
    const PortInfo portInfo = AudioNodePortManager::getRegistryEvenPortInfo(id, permissions, type, version, props);
    if (portInfo.portId == SPA_ID_INVALID) {
        return;
    }

    auto *audioDeviceListener = static_cast<AudioDeviceListener *>(audioDeviceListenerPtr);
    if (audioDeviceListener->m_ports.contains(portInfo.nodeId)) {
        audioDeviceListener->m_ports[portInfo.nodeId].append(portInfo);
    } else {
        audioDeviceListener->m_ports[portInfo.nodeId] = {portInfo};
    }

    Q_EMIT audioDeviceListener->portAdded(portInfo);
}

void LibGoal::Audio::AudioDeviceListener::removedListener(void *audioDeviceListenerPtr, const uint32_t id)
{
    auto *audioDeviceListener = static_cast<AudioDeviceListener *>(audioDeviceListenerPtr);
    if (audioDeviceListener->m_outputDevices.contains(id)) {
        const auto output = audioDeviceListener->m_outputDevices[id];
        audioDeviceListener->m_outputDevices.remove(id);
        audioDeviceListener->m_ports.remove(id);

        audioDeviceListener->signalOutputDevicesChanged();
        qInfo() << "Audio Output Device removed:" << output.toString();
    } else if (audioDeviceListener->m_inputDevices.contains(id)) {
        const auto input = audioDeviceListener->m_inputDevices[id];
        audioDeviceListener->m_inputDevices.remove(id);
        audioDeviceListener->m_ports.remove(id);

        audioDeviceListener->signalInputDevicesChanged();
        qInfo() << "Audio Input Device removed:" << input.toString();
    }

    QMap<uint32_t, QList<PortInfo>> newPorts;
    for (auto it = audioDeviceListener->m_ports.begin(); it != audioDeviceListener->m_ports.end(); ++it) {
        QList<PortInfo> ports;
        for (const auto &port : it.value()) {
            if (port.portId == id) {
                Q_EMIT audioDeviceListener->portRemoved(port);
                continue;
            }

            ports.append(port);
        }

        if (!ports.isEmpty()) {
            newPorts[it.key()] = ports;
        }
    }

    audioDeviceListener->m_ports = newPorts;
}

int LibGoal::Audio::AudioDeviceListener::metadataPropertyCallback(void *audioDeviceListenerPtr, [[maybe_unused]] uint32_t subject, const char *key,
                                                                  [[maybe_unused]] const char *type, const char *value)
{
    if (key == nullptr || value == nullptr) {
        return -1;
    }

    if (strcmp(key, DEFAULT_AUDIO_SINK_KEY) != 0 && strcmp(key, DEFAULT_AUDIO_SOURCE_KEY) != 0) {
        return 0;
    }

    auto *audioDeviceListener = static_cast<AudioDeviceListener *>(audioDeviceListenerPtr);
    audioDeviceListener->parseDefaultAudioDevice(key, value);
    return 0;
}

void LibGoal::Audio::AudioDeviceListener::parseDefaultAudioDevice(const char *key, const char *jsonString)
{
    const QByteArray jsonByteArray{jsonString};
    QJsonParseError error;
    const QJsonDocument jsonDocument = QJsonDocument::fromJson(jsonByteArray, &error);

    if (error.error != QJsonParseError::NoError) {
        qWarning() << "Failed to parse default audio sink JSON:" << jsonString << "error:" << error.errorString();
        return;
    }

    if (!jsonDocument.isObject()) {
        qWarning() << "Default audio sink JSON is not an object:" << jsonString;
        return;
    }

    const QJsonObject jsonObject = jsonDocument.object();
    const QString name = jsonObject.value(NAME_KEY).toString();

    if (strcmp(key, DEFAULT_AUDIO_SINK_KEY) == 0) {
        for (const auto &output : m_outputDevices) {
            if (output.name != name) {
                continue;
            }

            m_defaultOutput = output;
            Q_EMIT defaultOutputChanged(m_defaultOutput);
            qInfo() << "Default audio sink set to:" << m_defaultOutput.toString();
            break;
        }
    }

    if (strcmp(key, DEFAULT_AUDIO_SOURCE_KEY) == 0) {
        for (const auto &input : m_inputDevices) {
            if (input.name != name) {
                continue;
            }

            m_defaultInput = input;
            Q_EMIT defaultInputChanged(m_defaultInput);
            qInfo() << "Default audio source set to:" << m_defaultInput.toString();
            break;
        }
    }
}

void LibGoal::Audio::AudioDeviceListener::signalOutputDevicesChanged()
{
    QList<AudioInterfaceInfo> outputDevices;

    for (const auto &output : m_outputDevices) {
        outputDevices.append(output);
    }

    Q_EMIT outputDevicesChanged(outputDevices, m_defaultOutput);
}

void LibGoal::Audio::AudioDeviceListener::signalInputDevicesChanged()
{
    QList<AudioInterfaceInfo> inputDevices;

    for (const auto &input : m_inputDevices) {
        inputDevices.append(input);
    }

    Q_EMIT inputDevicesChanged(inputDevices, m_defaultInput);
}
