#ifndef AUDIOOUTILS_H
#define AUDIOOUTILS_H

#include <QJsonDocument>
#include <QList>
#include <QString>
#include <QtMultimedia/QAudioFormat>

#include <pipewire/properties.h>
#include <pipewire/thread-loop.h>
#include <spa/param/audio/raw.h>
#include <spa/pod/pod.h>

namespace LibGoal::Audio
{
class AudioNodeBase;

namespace Utils
{

static constexpr auto POD_DATA_BUILDER_BUFFER_SIZE = 1024;

static constexpr float DEFAULT_PIPEWIRE_VOLUME = 1.0f;
static constexpr float MIN_PIPEWIRE_VOLUME = 0.0f;
static constexpr float MAX_PIPEWIRE_VOLUME = 10.0f;

enum class AudioPropagationMode : int {
    Inherit,  // apply parent audio settings only
    Absolute, // ignore parent audio settings
    Relative, // apply parent audio settings on top of its own
};

float decibelsToIecScale(float decibels);
float iecScaleToDecibels(float iecScale);

float volumePercentageToCubicFloat(int volumePercentage);
int cubicFloatToVolumePercentage(float cubicFloat);

float volumePercentageToVolumeLinear(int volumePercentage);
int volumeLinearToVolumePercentage(float volumeLinear);

float decibelsToVolumeLinear(float decibels);
float volumeLinearToDecibels(float volumeLinear);

float volumePercentageToDecibels(int volumePercentage);
int decibelsToVolumePercentage(float decibels);

struct AudioSettings {
    int volume{cubicFloatToVolumePercentage(DEFAULT_PIPEWIRE_VOLUME)};
    bool muted{false};
};

struct CompoundAudioSettings {
    AudioSettings parentSettings;
    AudioSettings instanceSettings;
    AudioPropagationMode propagationMode{AudioPropagationMode::Inherit};
};

AudioSettings determineAudioSettings(const CompoundAudioSettings &settings);

/**
 * @brief The AudioLoopLocker class encapsulates locking of the PipeWire thread loop using RAII.
 */
class AudioLoopLocker
{
public:
    explicit AudioLoopLocker(pw_thread_loop *pwThreadLoop);

    ~AudioLoopLocker();

    void unlock() const;

private:
    pw_thread_loop *m_pwThreadLoop = nullptr;
};

/**
 * @brief The AudioProperties class encapsulates the PipeWire properties object. Does not take ownership of the properties by default.
 */
class AudioProperties
{
public:
    explicit AudioProperties(pw_properties *pwProperties);

    /**
     * Creates a new PipeWire properties object with the default media type, category, role, app name. Sets the target object if not empty.
     * @param audioNode the audio node to set create the properties for
     * @param properties a list of key-value pairs to set. Could be additional or could override the default values.
     */
    explicit AudioProperties(const AudioNodeBase *audioNode, const QList<QPair<QString, QString>> &properties = {});
    explicit AudioProperties(const QList<QPair<QString, QString>> &properties = {});

    ~AudioProperties();

    pw_properties *getProperties() const;

    /**
     * Sets whether the properties object should be cleaned up on destruction (as pw_stream & pw_filter take ownership of the properties).
     */
    void setCleanup(bool cleanup);

private:
    pw_properties *m_pwProperties = nullptr;
    bool m_cleanup = false;
};

/**
 * @brief The AudioFormatInfo struct is a struct for storing audio format information - used for forming PipeWire audio parameters.
 * The default values adhere to the libgoal libavdecoder conversion.
 */
struct AudioFormatInfo {
    static QAudioFormat::SampleFormat toAudioFormat(spa_audio_format format);
    static QAudioFormat::ChannelConfig toAudioChannelConfig(uint32_t channels);
    static int getSampleSizeInBytes(spa_audio_format format);
    QAudioFormat toAudioFormat() const;

    spa_audio_format format{SPA_AUDIO_FORMAT_S16};
    uint32_t flags{0};
    uint32_t rate{48000};
    uint32_t channels{2};

    int buffersCount{8};
    int blocksCount{1};
    int bufferSize{8192};
    int sampleSize{getSampleSizeInBytes(format)};        /// Size of one sample
    int stride{static_cast<int>(sampleSize * channels)}; /// Stride (size of one frame)
    int align{16};                                       /// Alignment of the buffer
};

using AudioAmplitudes = QPair<QList<QList<float>>, QList<float>>;
AudioAmplitudes calculateAudioDataAmplitudes(const QByteArray &audioData, const AudioFormatInfo &audioFormatInfo);

/**
 * @brief The AudioParamsFactory class is a factory for creating PipeWire parameters stored as POD objects.
 * Each method returns a pointer to a newly allocated object (under the assumption that it gets destroyed by the caller e.g. pw_stream_destroy).
 */
class AudioParamsFactory
{
public:
    AudioParamsFactory() = delete;

    /**
     * @brief Create a PipeWire audio params array from the given format info.
     * @param formatInfo the audio format info
     * @return a pair of the created spa_pod object and the number of elements in the array
     */
    static QList<const spa_pod *> fromFormatInfo(const AudioFormatInfo &formatInfo);

    static QList<const spa_pod *> defaultParams();

    /**
     * @brief Create a PipeWire audio params array from the given JSON document and specified type.
     * @param json the JSON document
     * @param type the type of the parameters
     * @return a list of the created spa_pod objects
     */
    static QList<const spa_pod *> fromJson(const QJsonDocument &json, uint32_t type);
};
} // namespace Utils
} // namespace LibGoal::Audio

#endif // AUDIOOUTILS_H
