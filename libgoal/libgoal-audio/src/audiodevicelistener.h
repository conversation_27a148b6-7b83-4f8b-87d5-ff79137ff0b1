#ifndef AUDIOOUTPUTDEVICELISTENER_H
#define AUDIOOUTPUTDEVICELISTENER_H

#include "nodes/audiointerfaceinfo.h"
#include "portinfo.h"
#include "registrylistener.h"

#include <QMap>
#include <QObject>

#include <pipewire/extensions/metadata.h>

namespace LibGoal::Audio
{
/**
 * @brief The AudioDeviceListener class listens for audio devices and the default audio device for both input and output.
 * It stores the audio device information and emits when changes occur.
 */
class AudioDeviceListener : public QObject
{
    Q_OBJECT

public:
    explicit AudioDeviceListener(AudioLoop *audioLoop, QObject *parent = nullptr);
    ~AudioDeviceListener() override;

    QMap<uint32_t, AudioInterfaceInfo> getOutputDevices() const;
    AudioInterfaceInfo getDefaultOutput() const;

    QMap<uint32_t, AudioInterfaceInfo> getInputDevices() const;
    AudioInterfaceInfo getDefaultInput() const;

    QMap<uint32_t, QList<PortInfo>> getPorts() const;

    constexpr static auto AUDIO_SINK_MEDIA_CLASS = "Audio/Sink";
    constexpr static auto DEFAULT_AUDIO_SINK_KEY = "default.audio.sink";

    constexpr static auto AUDIO_SOURCE_MEDIA_CLASS = "Audio/Source";
    constexpr static auto DEFAULT_AUDIO_SOURCE_KEY = "default.audio.source";

    constexpr static auto NAME_KEY = "name";

Q_SIGNALS:
    /**
     * @brief Emitted when the default audio output changes
     * @param output the new default audio output
     */
    void defaultOutputChanged(const AudioInterfaceInfo &output);

    /**
     * @brief Emitted when the audio output devices change
     * @param outputDevices the current audio output devices
     * @param defaultOutput the current default audio output
     */
    void outputDevicesChanged(const QList<AudioInterfaceInfo> &outputDevices, const AudioInterfaceInfo &defaultOutput);

    /**
     * @brief Emitted when the default audio input changes
     * @param input the new default audio input
     */
    void defaultInputChanged(const AudioInterfaceInfo &input);

    /**
     * @brief Emitted when the audio input devices change
     * @param inputDevices the current audio input devices
     * @param defaultInput the current default audio input
     */
    void inputDevicesChanged(const QList<AudioInterfaceInfo> &inputDevices, const AudioInterfaceInfo &defaultInput);

    void portAdded(const PortInfo &port);
    void portRemoved(const PortInfo &port);

private:
    AudioLoop *m_audioLoop = nullptr;
    RegistryListener *m_deviceListener = nullptr;

    QMap<uint32_t, AudioInterfaceInfo> m_outputDevices;
    QMap<uint32_t, AudioInterfaceInfo> m_inputDevices;
    QMap<uint32_t, QList<PortInfo>> m_ports;
    QMap<uint32_t, spa_hook> m_metadataHooks;
    QMap<uint32_t, pw_metadata *> m_metadataObjects;

    AudioInterfaceInfo m_defaultOutput;
    AudioInterfaceInfo m_defaultInput;

    static void onGlobalListener(void *data, uint32_t id, uint32_t permissions, const char *type, uint32_t version, const spa_dict *props);
    static void deviceListener(void *audioDeviceListenerPtr, uint32_t id, uint32_t permissions, const char *type, uint32_t version, const spa_dict *props);
    static void metadataListener(void *audioDeviceListenerPtr, uint32_t id, uint32_t permissions, const char *type, uint32_t version, const spa_dict *props);
    static void portListener(void *audioDeviceListenerPtr, uint32_t id, uint32_t permissions, const char *type, uint32_t version, const spa_dict *props);

    static void removedListener(void *audioDeviceListenerPtr, uint32_t id);

    static int metadataPropertyCallback(void *audioDeviceListenerPtr, uint32_t subject, const char *key, const char *type, const char *value);

    static constexpr pw_metadata_events metadata_events = {
        .version = PW_VERSION_METADATA_EVENTS,
        .property = metadataPropertyCallback};

    void parseDefaultAudioDevice(const char *key, const char *jsonString);
    void signalOutputDevicesChanged();
    void signalInputDevicesChanged();
};
} // namespace LibGoal::Audio

#endif // AUDIOOUTPUTDEVICELISTENER_H
