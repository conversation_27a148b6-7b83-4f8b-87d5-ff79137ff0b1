#include "audioloop.h"

#include <pipewire/keys.h>
#include <pipewire/node.h>

LibGoal::Audio::AudioLoop::AudioLoop(QObject *parent)
    : QObject{parent}
{
    m_pwThreadLoop = pw_thread_loop_new("pipewire-thread-loop", nullptr);
    if (m_pwThreadLoop == nullptr) {
        qFatal() << "failed to create PipeWire loop";
        return;
    }

    Utils::AudioLoopLocker locker = getAudioLoopLocker();

    m_pwContext = pw_context_new(pw_thread_loop_get_loop(m_pwThreadLoop), nullptr, 0);
    if (m_pwContext == nullptr) {
        qFatal() << "failed to create PipeWire context";
        return;
    }

    m_pwCore = pw_context_connect(m_pwContext, nullptr, 0);
    if (m_pwCore == nullptr) {
        qFatal() << "failed to connect to PipeWire core";
        return;
    }

    m_registry = pw_core_get_registry(m_pwCore, PW_VERSION_REGISTRY, 0);
    if (m_registry == nullptr) {
        qFatal() << "failed to get PipeWire registry";
        return;
    }

    m_outputDeviceListener = new AudioDeviceListener{this, this};
    QObject::connect(m_outputDeviceListener, &AudioDeviceListener::defaultOutputChanged, this, &AudioLoop::defaultOutputChanged);
    QObject::connect(m_outputDeviceListener, &AudioDeviceListener::outputDevicesChanged, this, &AudioLoop::availableOutputDevicesChanged);

    pw_thread_loop_start(m_pwThreadLoop);
}

LibGoal::Audio::AudioLoop::~AudioLoop()
{
    if (m_pwThreadLoop == nullptr) {
        return;
    }

    pw_thread_loop_stop(m_pwThreadLoop);

    if (m_pwCore != nullptr) {
        pw_core_disconnect(m_pwCore);
    }

    if (m_pwContext != nullptr) {
        pw_context_destroy(m_pwContext);
    }

    if (m_registry != nullptr) {
        pw_proxy_destroy(reinterpret_cast<pw_proxy *>(m_registry));
    }

    pw_thread_loop_destroy(m_pwThreadLoop);
}

pw_core *LibGoal::Audio::AudioLoop::getAudioLoopCore() const
{
    return m_pwCore;
}

pw_registry *LibGoal::Audio::AudioLoop::getAudioLoopRegistry() const
{
    return m_registry;
}

LibGoal::Audio::Utils::AudioLoopLocker LibGoal::Audio::AudioLoop::getAudioLoopLocker() const
{
    return Utils::AudioLoopLocker{m_pwThreadLoop};
}

LibGoal::Audio::AudioDeviceListener *LibGoal::Audio::AudioLoop::getDeviceListener() const
{
    return m_outputDeviceListener;
}
