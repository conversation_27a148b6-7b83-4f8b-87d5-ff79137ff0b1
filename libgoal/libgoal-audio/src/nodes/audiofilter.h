#ifndef AUDIOFILTER_H
#define AUDIOFILTER_H

#include "audioloop.h"
#include "audionodebase.h"

#include <pipewire/filter.h>

namespace LibGoal::Audio
{
using FilterProcessHandler = std::function<void(const QList<float *> &inputBuffers, const QList<float *> &outputBuffers, uint32_t sampleCount)>;

/**
 * @brief The AudioFilter class is responsible for applying the provided filter to the incoming audio data.
 */
class AudioFilter : public AudioNodeBase
{
public:
    /**
     * @brief Creates a new AudioFilter instance.
     * @param onProcessHandler the handler to be called when the incoming audio data is to be processed
     * @param settings the settings of the audio node
     * @param audioLoop the audio loop instance
     * @param parent QObject parent
     */
    explicit AudioFilter(FilterProcessHandler onProcessHandler, const AudioNodeSettings &settings,
                         AudioLoop *audioLoop, QObject *parent = nullptr);
    ~AudioFilter() override;

    void setNodeName(const QString &nodeName) override;
    void setSuppressOnProcessWarnings(bool suppressOnProcessWarnings);

private:
    pw_filter *m_filter = nullptr;
    spa_hook m_filterListener{};
    pw_filter_events m_filterEvents{};
    bool m_suppressOnProcessWarnings{false};

    FilterProcessHandler m_onProcessHandler;
    void filterWrapper(const spa_io_position *position) const;
    void processAmplitudesFeedback(const QList<float *> &outputBuffers, uint32_t sampleCount) const;

    QList<void *> m_inputPortsHandles;
    QList<void *> m_outputPortsHandles;

    static void onProcess(void *data, spa_io_position *position);
    static void onStateChanged(void *data, pw_filter_state old, pw_filter_state state, const char *error);
    static AudioNodeSettings createAudioFilterSettings(const AudioNodeSettings &settings);
};
} // namespace LibGoal::Audio

#endif // AUDIOFILTER_H
