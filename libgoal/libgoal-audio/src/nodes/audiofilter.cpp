#include "audiofilter.h"

#include <utility>

#include <pipewire/keys.h>

void LibGoal::Audio::AudioFilter::filterWrapper([[maybe_unused]] const spa_io_position *position) const
{
    if (m_filter == nullptr || !isConnected() || !getPortManager()->arePortsConnected()) {
        return;
    }

    const auto n_samples = static_cast<uint32_t>(position->clock.duration);
    QList<float *> inputBuffers;
    QList<float *> outputBuffers;

    if (!m_suppressOnProcessWarnings && n_samples > std::numeric_limits<size_t>::max() / sizeof(float)) {
        qWarning() << "sampleCount * sizeof(float) results in overflow on node:" << getNodeName() << "node id:" << getNodeId();
        return;
    }

    const auto settings = getAudioSettings();
    if (!m_suppressOnProcessWarnings && m_inputPortsHandles.size() != m_outputPortsHandles.size()) {
        qWarning() << "Input and output ports count mismatch on node:" << getNodeName() << "node id:" << getNodeId();
        return;
    }

    for (const auto inputPortsHandle : m_inputPortsHandles) {
        auto *in = static_cast<float *>(pw_filter_get_dsp_buffer(inputPortsHandle, n_samples));
        inputBuffers.append(in);
    }

    for (const auto outputPortsHandle : m_outputPortsHandles) {
        auto *out = static_cast<float *>(pw_filter_get_dsp_buffer(outputPortsHandle, n_samples));
        outputBuffers.append(out);
    }

    m_onProcessHandler(inputBuffers, outputBuffers, n_samples);
    processAmplitudesFeedback(outputBuffers, n_samples);
}

void LibGoal::Audio::AudioFilter::processAmplitudesFeedback(const QList<float *> &outputBuffers, const uint32_t sampleCount) const
{
    QList<QList<float>> amplitudeLists;
    QList<float> peakAmplitudes;

    for (const auto &outputBuffer : outputBuffers) {
        QList<float> amplitudes;
        float peakAmplitude = -std::numeric_limits<float>::infinity();

        if (outputBuffer != nullptr) {
            // find the peak amplitude
            for (uint32_t i = 0; i < sampleCount; i++) {
                amplitudes.append(std::abs(outputBuffer[i]));
                peakAmplitude = std::max(peakAmplitude, Utils::volumeLinearToDecibels(std::abs(outputBuffer[i])));
            }
        }

        amplitudeLists.append(amplitudes);
        peakAmplitudes.append(peakAmplitude);
    }

    Q_EMIT amplitudesFeedback(amplitudeLists, peakAmplitudes);
}

void LibGoal::Audio::AudioFilter::onProcess(void *data, spa_io_position *position)
{
    const auto *const filter = static_cast<AudioFilter *>(data);
    filter->filterWrapper(position);
}

void LibGoal::Audio::AudioFilter::onStateChanged(void *data, [[maybe_unused]] pw_filter_state old, const pw_filter_state state, const char *error)
{
    auto *const filter = static_cast<AudioFilter *>(data);
    switch (state) {
    case PW_FILTER_STATE_ERROR:
        qWarning() << "Filter" << filter->getNodeName() << "error:" << error;
        break;
    case PW_FILTER_STATE_PAUSED:
        [[fallthrough]];
    case PW_FILTER_STATE_STREAMING:
        if (filter->getNodeId() == SPA_ID_INVALID) {
            filter->setNodeId(pw_filter_get_node_id(filter->m_filter));
        }

        if (!filter->isConnected()) {
            filter->setConnected(true);
        }
        break;
    case PW_FILTER_STATE_UNCONNECTED:
        filter->setConnected(false);
        break;
    default:
        break;
    }
}

LibGoal::Audio::AudioNodeSettings LibGoal::Audio::AudioFilter::createAudioFilterSettings(const AudioNodeSettings &settings)
{
    AudioNodeSettings filterSettings = settings;
    filterSettings.hasInputPorts = true;
    return filterSettings;
}

LibGoal::Audio::AudioFilter::AudioFilter(FilterProcessHandler onProcessHandler, const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent)
    : AudioNodeBase{createAudioFilterSettings(settings), audioLoop, parent}
    , m_onProcessHandler{std::move(onProcessHandler)}
{
    Utils::AudioLoopLocker locker = audioLoop->getAudioLoopLocker();

    QList<QPair<QString, QString>> propertiesList = {{PW_KEY_MEDIA_CLASS, "Filter"}};
    propertiesList.append(settings.additionalProperties);
    const Utils::AudioProperties properties{this, propertiesList};

    m_filterEvents.version = PW_VERSION_FILTER_EVENTS;
    m_filterEvents.process = onProcess;
    m_filterEvents.state_changed = onStateChanged;

    m_filter = pw_filter_new(audioLoop->getAudioLoopCore(), "", properties.getProperties());

    if (m_filter == nullptr) {
        qWarning() << "Failed to create filter" << getNodeName();
        return;
    }

    pw_filter_add_listener(m_filter, &m_filterListener, &m_filterEvents, this);

    for (uint32_t i = 0; i < getAudioFormatInfo().channels; i++) {
        auto *const outputPort = static_cast<pw_port *>(pw_filter_add_port(
            m_filter,
            PW_DIRECTION_OUTPUT,
            PW_FILTER_PORT_FLAG_MAP_BUFFERS,
            0,
            pw_properties_new(
                PW_KEY_PORT_NAME, QString("output_%1").arg(i).toLatin1().constData(),
                PW_KEY_FORMAT_DSP, "32 bit float mono audio",
                nullptr),
            nullptr, 0));

        m_outputPortsHandles.append(outputPort); // add even on failure, to preserve port order
        if (outputPort == nullptr) {
            qWarning() << "Failed to create output port:" << i << "on node:" << getNodeName();
        }

        auto *const inputPort = static_cast<pw_port *>(pw_filter_add_port(
            m_filter,
            PW_DIRECTION_INPUT,
            PW_FILTER_PORT_FLAG_MAP_BUFFERS,
            0,
            pw_properties_new(
                PW_KEY_PORT_NAME, QString("input_%1").arg(i).toLatin1().constData(),
                PW_KEY_FORMAT_DSP, "32 bit float mono audio",
                nullptr),
            nullptr, 0));

        m_inputPortsHandles.append(inputPort);
        if (inputPort == nullptr) {
            qWarning() << "Failed to create input port:" << i << "on node:" << getNodeName();
        }
    }

    if (pw_filter_connect(m_filter, PW_FILTER_FLAG_RT_PROCESS, nullptr, 0) != 0) {
        qWarning() << "Failed to connect filter" << getNodeName();
    }
}

LibGoal::Audio::AudioFilter::~AudioFilter()
{
    const auto *audioLoop = getAudioLoop();
    if (audioLoop == nullptr) {
        return;
    }

    Utils::AudioLoopLocker loopLocker = audioLoop->getAudioLoopLocker();
    spa_hook_remove(&m_filterListener);

    if (m_filter != nullptr) {
        pw_filter_destroy(m_filter);
    }
}

void LibGoal::Audio::AudioFilter::setNodeName(const QString &nodeName)
{
    AudioNodeBase::setNodeName(nodeName);

    Utils::AudioProperties properties{{
        {PW_KEY_NODE_NAME, nodeName},
        {PW_KEY_NODE_DESCRIPTION, nodeName},
        {PW_KEY_NODE_NICK, nodeName},
    }};
    properties.setCleanup(true);

    Utils::AudioLoopLocker loopLocker = getAudioLoop()->getAudioLoopLocker();
    pw_filter_update_properties(m_filter, nullptr, &properties.getProperties()->dict);
}

void LibGoal::Audio::AudioFilter::setSuppressOnProcessWarnings(const bool suppressOnProcessWarnings)
{
    m_suppressOnProcessWarnings = suppressOnProcessWarnings;
}
