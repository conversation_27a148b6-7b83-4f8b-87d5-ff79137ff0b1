#ifndef AUDIOOUTPUTINFO_H
#define AUDIOOUTPUTINFO_H

#include <QString>

#include <pipewire/core.h>

namespace LibGoal::Audio
{
struct AudioInterfaceInfo {
    uint32_t nodeId{SPA_ID_INVALID};
    QString name;
    QString description;
    QString nick;

    QString toString() const
    {
        return QString("Id: %1, Name: %2, Description: %3").arg(nodeId).arg(name).arg(description);
    }
};
} // namespace LibGoal::Audio

#endif // AUDIOOUTPUTINFO_H
