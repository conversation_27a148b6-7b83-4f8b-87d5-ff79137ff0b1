#ifndef AUDIONODEBASE_H
#define AUDIONODEBASE_H

#include "audioloop.h"
#include "audionodelinkmanager.h"
#include "audionodeportmanager.h"
#include "audioutils.h"

#include <QMutex>

namespace LibGoal::Audio
{
/**
 * @brief The AudioNodeBase class is the base class for all audio nodes. Allows for a hierarchical structure of audio nodes.
 */
class AudioNodeBase : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Creates a new AudioNodeBase instance.
     * @param settings the settings of the audio node
     * @param audioLoop the audio loop instance
     * @param parent QObject parent
     */
    explicit AudioNodeBase(const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent = nullptr);
    ~AudioNodeBase() override;

    bool isConnected() const;
    bool isLinked(uint32_t nodeId) const;
    bool arePortsConnected() const;
    bool isConnectedToAnOutputDevice() const;

    /**
     * @return true if the node is linked both from input and output
     */
    bool isLinkedEndToEnd() const;

    uint32_t getNodeId() const;
    QString getNodeName() const;
    Utils::AudioFormatInfo getAudioFormatInfo() const;
    AudioNodeSettings getAudioSettings() const;

    AudioLoop *getAudioLoop() const;
    QString toString() const;

    virtual void setNodeName(const QString &nodeName);

    void connectToOutputDevice(const AudioInterfaceInfo &outputInfo, const QSet<InputOutputPortsLinkMapping> &portsMappings = {}) const;
    void connectToDefaultOutputDevice(const QSet<InputOutputPortsLinkMapping> &portsMappings = {});
    void disconnectOutputDevice();

    void connectToInputDevice(const AudioInterfaceInfo &inputInfo, const QSet<InputOutputPortsLinkMapping> &portsMappings = {}) const;
    void connectToDefaultInputDevice(const QSet<InputOutputPortsLinkMapping> &portsMappings = {});
    void disconnectInputDevice();

    AudioNodePortManager *getPortManager() const;
    AudioNodeLinkManager *getLinkManager() const;

Q_SIGNALS:
    void nodeConnected(AudioNodeBase *sender);
    void portsConnected(AudioNodeBase *sender);
    void linked(AudioNodeBase *sender);

    /**
     * Emitted on each successful onProcess callback
     * @param amplitudeLists amplitude lists for each output port, normalized to 0..1 relative to max possible amplitude
     * @param peakAmplitudes peak amplitudes for each output port, normalized to 0..1 relative to max possible amplitude
     */
    void amplitudesFeedback(const QList<QList<float>> &amplitudeLists, const QList<float> &peakAmplitudes) const;

protected Q_SLOTS:
    void setConnected(bool connected);
    void setOutputDevice(const AudioInterfaceInfo &outputDevice, const QSet<InputOutputPortsLinkMapping> &portsMappings = {}) const;
    void setInputDevice(const AudioInterfaceInfo &inputDevice, const QSet<InputOutputPortsLinkMapping> &portsMappings = {}) const;
    void setNodeId(uint32_t nodeId);
    void reconnectToOutputDeviceCandidate(const QList<AudioInterfaceInfo> &availableOutputDevices) const;

private:
    AudioNodeSettings m_settings;

    // state
    bool m_connected{false};
    bool m_useDefaultOutputDevice{false};
    bool m_useDefaultInputDevice{false};

    uint32_t m_nodeId{SPA_ID_INVALID};

    AudioLoop *m_audioLoop = nullptr;

    AudioNodePortManager *m_portManager = nullptr;
    AudioNodeLinkManager *m_linkManager = nullptr;
};
} // namespace LibGoal::Audio

#endif // AUDIONODEBASE_H
