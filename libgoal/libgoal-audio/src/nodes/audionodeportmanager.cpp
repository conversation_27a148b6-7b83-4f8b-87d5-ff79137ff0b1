#include "audionodeportmanager.h"

#include "audionodebase.h"

#include <pipewire/keys.h>
#include <pipewire/port.h>
#include <pipewire/type.h>

void LibGoal::Audio::AudioNodePortManager::registryListener(void *portManagerPtr, const uint32_t id, const uint32_t permissions,
                                                            const char *type, const uint32_t version, const spa_dict *props)
{
    auto *portManager = static_cast<AudioNodePortManager *>(portManagerPtr);
    portManager->portConnectionListener(id, permissions, type, version, props);
}

void LibGoal::Audio::AudioNodePortManager::registryRemovedListener(void *portManagerPtr, const uint32_t id)
{
    auto *portManager = static_cast<AudioNodePortManager *>(portManagerPtr);
    portManager->portRemovedListener(id);
}

void LibGoal::Audio::AudioNodePortManager::portConnectionListener([[maybe_unused]] const uint32_t id, [[maybe_unused]] const uint32_t permissions,
                                                                  const char *type, [[maybe_unused]] const uint32_t version, const spa_dict *props)
{
    const PortInfo portInfo = getRegistryEvenPortInfo(id, permissions, type, version, props);
    if (portInfo.nodeId != m_audioNode->getNodeId()) {
        return;
    }

    registerPortInfo(portInfo);
}

void LibGoal::Audio::AudioNodePortManager::portRemovedListener(const uint32_t id)
{
    for (int i = 0; i < m_inputPortsInfos.size(); i++) {
        if (m_inputPortsInfos.at(i).portId == id) {
            m_inputPortsInfos.removeAt(i);
            return;
        }
    }

    for (int i = 0; i < m_outputPortsInfos.size(); i++) {
        if (m_outputPortsInfos.at(i).portId == id) {
            m_outputPortsInfos.removeAt(i);
            return;
        }
    }
}

void LibGoal::Audio::AudioNodePortManager::registerPortInfo(const PortInfo &portInfo)
{
    if (arePortsConnected()) {
        qWarning() << "All ports are already connected for node" << m_audioNode->toString();
        return;
    }

    bool portRegistered = false;
    switch (portInfo.direction) {
    case AudioNodePortDirection::In:
        for (int i = 0; i < m_inputPortsInfos.size(); i++) {
            if (m_inputPortsInfos.at(i).portId > portInfo.portId) {
                m_inputPortsInfos.insert(i, portInfo);
                portRegistered = true;
                break;
            }
        }
        if (!portRegistered) {
            m_inputPortsInfos.append(portInfo);
        }
        break;
    case AudioNodePortDirection::Out:
        for (int i = 0; i < m_outputPortsInfos.size(); i++) {
            if (m_outputPortsInfos.at(i).portId > portInfo.portId) {
                m_outputPortsInfos.insert(i, portInfo);
                portRegistered = true;
                break;
            }
        }
        if (!portRegistered) {
            m_outputPortsInfos.append(portInfo);
        }
        break;
    default:
        qWarning() << "Unknown port direction for node" << m_audioNode->toString();
    }

    if (arePortsConnected()) {
        Q_EMIT portsConnected();
    }
}

LibGoal::Audio::AudioNodePortManager::AudioNodePortManager(const AudioNodeSettings &settings, AudioNodeBase *audioNode)
    : QObject{audioNode}
    , m_settings{settings}
    , m_registryListener{new RegistryListener{this, registryListener, registryRemovedListener, audioNode->getAudioLoop(), this}}
    , m_audioNode{audioNode}
{
}

bool LibGoal::Audio::AudioNodePortManager::arePortsConnected() const
{
    return (!m_settings.hasInputPorts || m_inputPortsInfos.size() == m_settings.audioFormatInfo.channels) && m_outputPortsInfos.size() == m_settings.audioFormatInfo.channels;
}

QList<LibGoal::Audio::PortInfo> LibGoal::Audio::AudioNodePortManager::getInputPortsInfos() const
{
    return m_inputPortsInfos;
}

QList<LibGoal::Audio::PortInfo> LibGoal::Audio::AudioNodePortManager::getOutputPortsInfos() const
{
    return m_outputPortsInfos;
}

LibGoal::Audio::PortInfo LibGoal::Audio::AudioNodePortManager::getRegistryEvenPortInfo(const uint32_t id, [[maybe_unused]] uint32_t permissions,
                                                                                       const char *type, [[maybe_unused]] uint32_t version, const spa_dict *props)
{
    if (strcmp(type, PW_TYPE_INTERFACE_Port) != 0) {
        return {};
    }

    const char *portDirection = spa_dict_lookup(props, PW_KEY_PORT_DIRECTION);
    const char *audioChannel = spa_dict_lookup(props, PW_KEY_AUDIO_CHANNEL);
    const char *nodeIdStr = spa_dict_lookup(props, PW_KEY_NODE_ID);
    const char *portIndexStr = spa_dict_lookup(props, PW_KEY_PORT_ID);

    bool ok = false;
    const uint32_t portIndex = QString(portIndexStr).toUInt(&ok);
    if (!ok) {
        return {};
    }

    const uint32_t nodeId = QString(nodeIdStr).toUInt(&ok);
    if (!ok) {
        return {};
    }

    PortInfo portInfo;
    portInfo.portId = id;
    portInfo.nodeId = nodeId;
    portInfo.index = portIndex;
    portInfo.direction = portDirection == nullptr ? AudioNodePortDirection::Unknown : PortInfo::getDirection(portDirection);
    portInfo.channel = audioChannel ? QString(audioChannel) : QString();

    return portInfo;
}
