#ifndef AUDIONODE_H
#define AUDIONODE_H

#include "audiofilter.h"

#include <QObject>

namespace LibGoal::Audio
{
/**
 * @brief The ScreenAudioNode class is responsible for routing all input audio as a single output
 */
class AudioNode : public AudioFilter
{
public:
    /**
     * @brief Creates a new AudioNode instance.
     * @param settings the settings of the audio node
     * @param audioLoop the audio loop instance
     * @param parent QObject parent
     */
    explicit AudioNode(const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent = nullptr);

private:
    static void passThroughFilter(const QList<float *> &inputBuffers, const QList<float *> &outputBuffers, uint32_t sampleCount);
};
} // namespace LibGoal::Audio

#endif // AUDIONODE_H
