#ifndef AUDIONODESETTINGS_H
#define AUDIONODESETTINGS_H

#include "audioutils.h"
#include "portinfo.h"

namespace LibGoal::Audio
{
struct AudioNodeSettings {
    QString nodeName;
    Utils::AudioFormatInfo audioFormatInfo{}; // the format info of the audio - determines the amount of ports and their properties
    Utils::AudioSettings audioSettings{};     // audio player specific

    QList<QPair<QString, QString>> additionalProperties{}; // additional metadata for the node
    QList<QPair<AudioNodeBase *, QSet<InputOutputPortsLinkMapping>>> parentNodesSetups{};

    bool hasInputPorts{true}; // for detecting ports connection, set automatically for internal use
};
} // namespace LibGoal::Audio

#endif // AUDIONODESETTINGS_H
