#include "audiofilterpluginbase.h"

#include <QDebug>

#include <dlfcn.h>
#include <utility>

using LV2_Descriptor_Function = const LV2_Descriptor *(*)(uint32_t index);

constexpr static auto Lv2DescriptorDynamicFunction = "lv2_descriptor";
constexpr static auto ENABLED_PORT_SYMBOL = "enabled";

LibGoal::Audio::AudioFilterPluginBase::AudioFilterPluginBase(QString pluginUri, const AudioNodeSettings &settings,
                                                             AudioLoop *audioLoop, QObject *parent)
    : AudioFilter([this](const QList<float *> &inputBuffers, const QList<float *> &outputBuffers, const uint32_t nSamples) {
        processAudio(inputBuffers, outputBuffers, nSamples);
    },
                  settings, audioLoop, parent)
    , m_pluginUri(std::move(pluginUri))
{
    m_world = lilv_world_new();
    if (!m_world) {
        qWarning() << "Failed to create LV2 world";
        return;
    }

    lilv_world_load_all(m_world);
}

LibGoal::Audio::AudioFilterPluginBase::~AudioFilterPluginBase()
{
    if (m_instance != nullptr && m_descriptor != nullptr) {
        if (m_descriptor->deactivate) {
            m_descriptor->deactivate(m_instance);
        }
        if (m_descriptor->cleanup) {
            m_descriptor->cleanup(m_instance);
        }
        m_instance = nullptr;
    }

    m_descriptor = nullptr;

    if (m_libraryHandle != nullptr) {
        dlclose(m_libraryHandle);
    }

    if (m_world != nullptr) {
        lilv_world_free(m_world);
    }
}

bool LibGoal::Audio::AudioFilterPluginBase::initializePlugin()
{
    if (!loadPlugin()) {
        return false;
    }

    if (!instantiatePlugin()) {
        return false;
    }

    connectControlPorts();

    // Activate the plugin
    if (m_descriptor != nullptr && m_descriptor->activate != nullptr) {
        m_descriptor->activate(m_instance);
    }

    return true;
}

void LibGoal::Audio::AudioFilterPluginBase::connectPluginPorts(const QList<float *> &inputPortsBuffers,
                                                               const QList<float *> &outputPortsBuffers, const uint32_t nSamples)
{
    for (int i = 0; i < m_audioInputPorts.size() && i < inputPortsBuffers.size(); ++i) {
        if (inputPortsBuffers.at(i) != nullptr) {
            m_descriptor->connect_port(m_instance, m_audioInputPorts.at(i), inputPortsBuffers.at(i));
        } else {
            m_dummyInputBuffers[i].resize(nSamples * sizeof(float));
            memset(m_dummyInputBuffers[i].data(), 0, nSamples * sizeof(float));
            m_descriptor->connect_port(m_instance, m_audioInputPorts.at(i), m_dummyInputBuffers[i].data());
        }
    }

    for (int i = 0; i < m_audioOutputPorts.size() && i < outputPortsBuffers.size(); ++i) {
        if (outputPortsBuffers.at(i) != nullptr) {
            m_descriptor->connect_port(m_instance, m_audioOutputPorts.at(i), outputPortsBuffers.at(i));
        } else {
            m_dummyOutputBuffers[i].resize(nSamples * sizeof(float));
            m_descriptor->connect_port(m_instance, m_audioOutputPorts.at(i), m_dummyOutputBuffers[i].data());
        }
    }
}

bool LibGoal::Audio::AudioFilterPluginBase::loadPlugin()
{
    if (m_world == nullptr) {
        qWarning() << "LV2 world not initialized";
        return false;
    }

    LilvNode *uri = lilv_new_uri(m_world, m_pluginUri.toUtf8().constData());
    if (uri == nullptr) {
        qWarning() << "Failed to create URI for plugin:" << m_pluginUri;
        return false;
    }

    m_plugin = lilv_plugins_get_by_uri(lilv_world_get_all_plugins(m_world), uri);
    lilv_node_free(uri);

    if (m_plugin == nullptr) {
        qWarning() << "Failed to find plugin:" << m_pluginUri;
        return false;
    }

    const uint32_t portsCount = lilv_plugin_get_num_ports(m_plugin);

    for (uint32_t i = 0; i < portsCount; ++i) {
        const LilvPort *port = lilv_plugin_get_port_by_index(m_plugin, i);

        // Audio ports
        if (lilv_port_is_a(m_plugin, port, lilv_new_uri(m_world, LV2_CORE__AudioPort))) {
            if (lilv_port_is_a(m_plugin, port, lilv_new_uri(m_world, LV2_CORE__InputPort))) {
                m_audioInputPorts.append(i);
                m_dummyInputBuffers.append(QByteArray());
            } else if (lilv_port_is_a(m_plugin, port, lilv_new_uri(m_world, LV2_CORE__OutputPort))) {
                m_audioOutputPorts.append(i);
                m_dummyOutputBuffers.append(QByteArray());
            }

            continue;
        }

        // Control ports
        if (lilv_port_is_a(m_plugin, port, lilv_new_uri(m_world, LV2_CORE__ControlPort))) {
            const LilvNode *symbol = lilv_port_get_symbol(m_plugin, port);
            QString symbolString = lilv_node_as_string(symbol);
            m_controlPortIndices[symbolString] = i;

            // Initialize with default value
            float defaultValue = 0.0f;

            LilvNode *defaultValueNode = nullptr;
            LilvNode *minValueNode = nullptr;
            LilvNode *maxValueNode = nullptr;

            lilv_port_get_range(m_plugin, port, &defaultValueNode, &minValueNode, &maxValueNode);

            if (defaultValueNode != nullptr) {
                defaultValue = lilv_node_as_float(defaultValueNode);
                lilv_node_free(defaultValueNode);
            }

            float minValue = defaultValue;
            float maxValue = defaultValue;

            if (minValueNode != nullptr) {
                minValue = lilv_node_as_float(minValueNode);
                lilv_node_free(minValueNode);
            }

            if (maxValueNode != nullptr) {
                maxValue = lilv_node_as_float(maxValueNode);
                lilv_node_free(maxValueNode);
            }

            m_controlValues[symbolString] = defaultValue;
            m_controlMinValues[symbolString] = minValue;
            m_controlMaxValues[symbolString] = maxValue;
            lilv_node_free(const_cast<LilvNode *>(symbol));
        }
    }

    return true;
}

bool LibGoal::Audio::AudioFilterPluginBase::instantiatePlugin()
{
    if (m_plugin == nullptr) {
        qWarning() << "Plugin not loaded";
        return false;
    }

    const LilvNode *libraryUri = lilv_plugin_get_library_uri(m_plugin);
    if (!libraryUri) {
        qWarning() << "Failed to get library URI for plugin:" << m_pluginUri;
        return false;
    }

    char *libraryPath = lilv_file_uri_parse(lilv_node_as_uri(libraryUri), nullptr);
    lilv_node_free(const_cast<LilvNode *>(libraryUri));

    if (libraryPath == nullptr) {
        qWarning() << "Failed to parse library path for plugin:" << m_pluginUri;
        return false;
    }

    void *libraryHandle = dlopen(libraryPath, RTLD_NOW);
    lilv_free(libraryPath);

    if (libraryHandle == nullptr) {
        qWarning() << "Failed to open plugin library:" << dlerror();
        return false;
    }

    const auto descriptorFunction =
        reinterpret_cast<LV2_Descriptor_Function>(dlsym(libraryHandle, Lv2DescriptorDynamicFunction));

    if (descriptorFunction == nullptr) {
        qWarning() << "Failed to find lv2_descriptor function:" << dlerror();
        dlclose(libraryHandle);
        return false;
    }

    // Find the right descriptor by URI
    const char *plugin_uri = lilv_node_as_uri(lilv_plugin_get_uri(m_plugin));
    for (uint32_t i = 0;; ++i) {
        const LV2_Descriptor *desc = descriptorFunction(i);
        if (desc == nullptr) {
            break; // End of descriptors
        }

        if (strcmp(desc->URI, plugin_uri) == 0) {
            m_descriptor = desc;
            break;
        }
    }

    if (m_descriptor == nullptr) {
        qWarning() << "Failed to find matching descriptor for plugin:" << m_pluginUri;
        dlclose(libraryHandle);
        return false;
    }

    m_libraryHandle = libraryHandle;

    const double sampleRate = getAudioSettings().audioFormatInfo.rate;
    m_instance = m_descriptor->instantiate(m_descriptor, sampleRate, nullptr, nullptr);

    if (m_instance == nullptr) {
        qWarning() << "Failed to instantiate plugin:" << m_pluginUri;
        return false;
    }

    return true;
}

void LibGoal::Audio::AudioFilterPluginBase::connectControlPorts()
{
    for (const auto &controlPort : m_controlPortIndices.keys()) {
        const uint32_t controlPortIndex = m_controlPortIndices.value(controlPort, UINT32_MAX);
        if (controlPortIndex != UINT32_MAX && m_controlValues.contains(controlPort)) {
            m_descriptor->connect_port(m_instance, controlPortIndex, &m_controlValues[controlPort]);
        }
    }
}

void LibGoal::Audio::AudioFilterPluginBase::processAudio(const QList<float *> &inputBuffers, const QList<float *> &outputBuffers, const uint32_t nSamples)
{
    if (isPluginLoaded()) { // Run the plugin
        connectPluginPorts(inputBuffers, outputBuffers, nSamples);
        m_descriptor->run(m_instance, nSamples);
        return;
    }

    // If plugin isn't ready, just apply a pass-through filter
    for (int i = 0; i < inputBuffers.size() && i < outputBuffers.size(); ++i) {
        if (inputBuffers[i] == nullptr || outputBuffers[i] == nullptr) {
            continue;
        }

        memcpy(outputBuffers[i], inputBuffers[i], nSamples * sizeof(float));
    }
}

float LibGoal::Audio::AudioFilterPluginBase::getParameterValue(const QString &parameterUri) const
{
    return m_controlValues.value(parameterUri, 0.0f);
}

float LibGoal::Audio::AudioFilterPluginBase::getParameterMinimumValue(const QString &parameterUri) const
{
    return m_controlMinValues.value(parameterUri, 0.0f);
}

float LibGoal::Audio::AudioFilterPluginBase::getParameterMaximumValue(const QString &parameterUri) const
{
    return m_controlMaxValues.value(parameterUri, 0.0f);
}

void LibGoal::Audio::AudioFilterPluginBase::setParameterValue(const QString &parameterUri, const float value)
{
    if (m_controlValues.contains(parameterUri) && m_controlMinValues.contains(parameterUri) && m_controlMaxValues.contains(parameterUri)) {
        m_controlValues[parameterUri] = std::clamp(value, m_controlMinValues[parameterUri], m_controlMaxValues[parameterUri]);
    }
}

void LibGoal::Audio::AudioFilterPluginBase::setEnabled(const bool enabled)
{
    setParameterValue(ENABLED_PORT_SYMBOL, enabled ? 1.0f : 0.0f);
}

bool LibGoal::Audio::AudioFilterPluginBase::isPluginLoaded() const
{
    return m_instance != nullptr && m_descriptor != nullptr && m_descriptor->run != nullptr;
}

QStringList LibGoal::Audio::AudioFilterPluginBase::getAvailableParameterUris() const
{
    return m_controlPortIndices.keys();
}
