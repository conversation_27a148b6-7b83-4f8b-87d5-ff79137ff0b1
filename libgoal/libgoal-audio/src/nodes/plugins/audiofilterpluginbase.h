#ifndef AUDIOFILTERPLUGINBASE_H
#define AUDIOFILTERPLUGINBASE_H

#include "audiofilter.h"

#include <QMap>
#include <QString>

#include <lilv-0/lilv/lilv.h>
#include <lv2/core/lv2.h>

namespace LibGoal::Audio
{
/**
 * @brief The AudioFilterPluginBase class is the base class for all filters utilizing LV2 plugins.
 * In order to implement a new filter, the derived class has to provide the URI of the plugin and call the initializePlugin method.
 * Then based on the specific plugin, the derived class can set the parameters of the plugin using the setParameterValue method.
 */
class AudioFilterPluginBase : public AudioFilter
{
    Q_OBJECT

protected:
    /**
     * @brief Initialize the LV2 plugin. This method has to be called by the derived class.
     * @return true if the plugin was successfully initialized, false otherwise
     */
    bool initializePlugin();

public:
    /**
     * @brief Creates a new AudioFilterPluginBase instance.
     * @param pluginUri the URI of the LV2 plugin
     * @param settings the settings of the audio node
     * @param audioLoop the audio loop instance
     * @param parent QObject parent
     */
    AudioFilterPluginBase(QString pluginUri, const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent = nullptr);
    ~AudioFilterPluginBase() override;

    float getParameterValue(const QString &parameterUri) const;
    float getParameterMinimumValue(const QString &parameterUri) const;
    float getParameterMaximumValue(const QString &parameterUri) const;

    void setParameterValue(const QString &parameterUri, float value);
    void setEnabled(bool enabled);

    bool isPluginLoaded() const;
    QStringList getAvailableParameterUris() const;

private:
    QString m_pluginUri;
    LilvWorld *m_world = nullptr;
    const LilvPlugin *m_plugin = nullptr;
    LV2_Handle m_instance = nullptr;
    const LV2_Descriptor *m_descriptor = nullptr;

    QList<uint32_t> m_audioInputPorts;
    QList<uint32_t> m_audioOutputPorts;
    void *m_libraryHandle = nullptr;

    QMap<QString, uint32_t> m_controlPortIndices;
    QMap<QString, float> m_controlValues;
    QMap<QString, float> m_controlMinValues;
    QMap<QString, float> m_controlMaxValues;

    QList<QByteArray> m_dummyInputBuffers;
    QList<QByteArray> m_dummyOutputBuffers;

    bool loadPlugin();
    bool instantiatePlugin();

    void processAudio(const QList<float *> &inputBuffers, const QList<float *> &outputBuffers, uint32_t nSamples);
    void connectPluginPorts(const QList<float *> &inputPortsBuffers, const QList<float *> &outputPortsBuffers, uint32_t nSamples);
    void connectControlPorts();
};
} // namespace LibGoal::Audio

#endif // AUDIOFILTERPLUGINBASE_H
