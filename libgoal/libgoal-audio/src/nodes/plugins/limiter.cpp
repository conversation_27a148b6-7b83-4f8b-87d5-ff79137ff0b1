#include "limiter.h"

constexpr static auto THRESHOLD_PORT_SYMBOL = "th";
constexpr static auto ATTACK_PORT_SYMBOL = "at";
constexpr static auto RELEASE_PORT_SYMBOL = "rt";
constexpr static auto KNEE_PORT_SYMBOL = "akn";
constexpr static auto PLUGIN_URI = "http://lsp-plug.in/plugins/lv2/mb_limiter_stereo";

LibGoal::Audio::Limiter::Limiter(const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent)
    : AudioFilterPluginBase(PLUGIN_URI, settings, audioLoop, parent)
{
    if (!initializePlugin()) {
        qWarning() << "Failed to initialize limiter plugin";
    }
}

float LibGoal::Audio::Limiter::getThreshold() const
{
    return getParameterValue(THRESHOLD_PORT_SYMBOL);
}

float LibGoal::Audio::Limiter::getAttack() const
{
    return getParameterValue(ATTACK_PORT_SYMBOL);
}

float LibGoal::Audio::Limiter::getRelease() const
{
    return getParameterValue(RELEASE_PORT_SYMBOL);
}

float LibGoal::Audio::Limiter::getKnee() const
{
    return getParameterValue(KNEE_PORT_SYMBOL);
}

void LibGoal::Audio::Limiter::setThreshold(const float threshold)
{
    setParameterValue(THRESHOLD_PORT_SYMBOL, threshold);
}

void LibGoal::Audio::Limiter::setAttack(const float attack)
{
    setParameterValue(ATTACK_PORT_SYMBOL, attack);
}

void LibGoal::Audio::Limiter::setRelease(const float release)
{
    setParameterValue(RELEASE_PORT_SYMBOL, release);
}

void LibGoal::Audio::Limiter::setKnee(const float knee)
{
    setParameterValue(KNEE_PORT_SYMBOL, knee);
}
