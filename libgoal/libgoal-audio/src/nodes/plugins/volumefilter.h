#ifndef VOLUMEFILTER_H
#define VOLUMEFILTER_H

#include "audiofilterpluginbase.h"

namespace LibGoal::Audio
{
/**
 * @brief The VolumeFilter class is a filter that adjusts the volume of the incoming audio data.
 */
class VolumeFilter : public AudioFilterPluginBase
{
    Q_OBJECT

    // have to keep this state additionally as mute has to be implemented via setting the volume to minimum
    float m_volumeLinear = 1.0f;
    bool m_muted = false;

public:
    /**
     * @brief Creates a new VolumeFilter instance.
     * @param settings the settings of the audio node
     * @param audioLoop the audio loop instance
     * @param parent QObject parent
     */
    explicit VolumeFilter(const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent = nullptr);

    float getVolumeDecibels() const;
    int getVolumePercentage() const;
    bool isMuted() const;
    float getBalance() const;

    void setVolumeDecibels(float volumeDecibels);
    void setVolumePercentage(int volumePercentage);
    void setMuted(bool muted);
    void setBalance(float balance);
};
} // namespace LibGoal::Audio

#endif // VOLUMEFILTER_H
