#ifndef LIMITER_H
#define LIMITER_H

#include "audiofilterpluginbase.h"

namespace LibGoal::Audio
{
/**
 * @brief The Limiter class is a filter that limits the volume of the incoming audio data.
 */
class Limiter : public AudioFilterPluginBase
{
    Q_OBJECT

public:
    /**
     * @brief Creates a new Limiter instance.
     * @param settings the settings of the audio node
     * @param audioLoop the audio loop instance
     * @param parent QObject parent
     */
    explicit Limiter(const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent = nullptr);

    float getThreshold() const;
    float getAttack() const;
    float getRelease() const;
    float getKnee() const;

    void setThreshold(float threshold);
    void setAttack(float attack);
    void setRelease(float release);
    void setKnee(float knee);
};
} // namespace LibGoal::Audio

#endif //LIMITER_H
