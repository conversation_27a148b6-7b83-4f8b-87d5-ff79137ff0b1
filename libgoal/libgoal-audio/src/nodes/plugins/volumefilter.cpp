#include "volumefilter.h"

constexpr static auto TOTAL_VOLUME_PORT_SYMBOL = "g_in";
constexpr static auto BALANCE_PORT_SYMBOL = "bal";
constexpr static auto PLUGIN_URI = "http://lsp-plug.in/plugins/lv2/para_equalizer_x16_stereo";

LibGoal::Audio::VolumeFilter::VolumeFilter(const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent)
    : AudioFilterPluginBase(PLUGIN_URI, settings, audioLoop, parent)
{
    if (!initializePlugin()) {
        qWarning() << "Failed to initialize volume filter plugin";
    }

    m_volumeLinear = getParameterValue(TOTAL_VOLUME_PORT_SYMBOL);
}

float LibGoal::Audio::VolumeFilter::getVolumeDecibels() const
{
    return Utils::volumeLinearToDecibels(m_volumeLinear);
}

int LibGoal::Audio::VolumeFilter::getVolumePercentage() const
{
    return Utils::volumeLinearToVolumePercentage(m_volumeLinear);
}

bool LibGoal::Audio::VolumeFilter::isMuted() const
{
    return m_muted;
}

float LibGoal::Audio::VolumeFilter::getBalance() const
{
    return getParameterValue(BALANCE_PORT_SYMBOL);
}

void LibGoal::Audio::VolumeFilter::setVolumeDecibels(const float volumeDecibels)
{
    m_volumeLinear = Utils::decibelsToVolumeLinear(volumeDecibels);
    if (!m_muted) {
        setParameterValue(TOTAL_VOLUME_PORT_SYMBOL, m_volumeLinear);
    }
}

void LibGoal::Audio::VolumeFilter::setVolumePercentage(const int volumePercentage)
{
    setVolumeDecibels(Utils::volumePercentageToDecibels(volumePercentage));
}

void LibGoal::Audio::VolumeFilter::setMuted(const bool muted)
{
    m_muted = muted;
    setParameterValue(TOTAL_VOLUME_PORT_SYMBOL, muted ? getParameterMinimumValue(TOTAL_VOLUME_PORT_SYMBOL) : m_volumeLinear);
}

void LibGoal::Audio::VolumeFilter::setBalance(const float balance)
{
    setParameterValue(BALANCE_PORT_SYMBOL, balance);
}
