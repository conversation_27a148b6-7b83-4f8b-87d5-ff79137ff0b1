#include "audionodelinkmanager.h"

#include "audionodebase.h"

#include <QRandomGenerator>

#include <pipewire/keys.h>
#include <pipewire/link.h>
#include <pipewire/type.h>

static constexpr auto LINK_RETRY_INTERVAL = std::chrono::milliseconds{100};
static constexpr auto MAX_LINK_RETRIES{3};
static constexpr auto INITIAL_RETRY_DELAY_MS{300};

void LibGoal::Audio::AudioNodeLinkManager::registryListener(void *linkManagerPtr, const uint32_t id, const uint32_t permissions,
                                                            const char *type, const uint32_t version, const spa_dict *props)
{
    auto *linkManager = static_cast<AudioNodeLinkManager *>(linkManagerPtr);
    linkManager->linkConnectionListener(id, permissions, type, version, props);
}

void LibGoal::Audio::AudioNodeLinkManager::registryRemovedListener(void *linkManagerPtr, const uint32_t id)
{
    auto *linkManager = static_cast<AudioNodeLinkManager *>(linkManagerPtr);
    linkManager->linkRemovedListener(id);
}

void LibGoal::Audio::AudioNodeLinkManager::linkConnectionListener(const uint32_t id, [[maybe_unused]] uint32_t permissions, const char *type,
                                                                  [[maybe_unused]] uint32_t version, const spa_dict *props)
{
    if (strcmp(type, PW_TYPE_INTERFACE_Link) != 0) {
        return;
    }

    const char *inputNodeId = spa_dict_lookup(props, PW_KEY_LINK_INPUT_NODE);
    const char *outputNodeId = spa_dict_lookup(props, PW_KEY_LINK_OUTPUT_NODE);

    if (inputNodeId == nullptr || outputNodeId == nullptr) {
        return;
    }

    bool inputNodeIdOk = false;
    bool outputNodeIdOk = false;
    const uint32_t inputNodeIdInt = QString{inputNodeId}.toUInt(&inputNodeIdOk);
    const uint32_t outputNodeIdInt = QString{outputNodeId}.toUInt(&outputNodeIdOk);

    if (!inputNodeIdOk || !outputNodeIdOk) {
        return;
    }

    if (outputNodeIdInt == m_audioNode->getNodeId()) { // our node is the output node
        for (auto &parentNodePortInfo : m_parentNodeConnectionStates) {
            if (parentNodePortInfo.node->getNodeId() == inputNodeIdInt) {
                parentNodePortInfo.linkIds.insert(id);
                break;
            }
        }

        if (m_outputDevice.nodeId == inputNodeIdInt) {
            m_outputDeviceLinkIds.insert(id);
        }

        m_allParentNodeLinkIds.insert(id);
    } else if (inputNodeIdInt == m_audioNode->getNodeId()) { // our node is the input node
        if (m_inputDevice.nodeId == outputNodeIdInt) {
            m_inputDeviceLinkIds.insert(id);
        }

        m_allChildNodeLinkIds.insert(id);
    }
}

void LibGoal::Audio::AudioNodeLinkManager::linkRemovedListener(const uint32_t id)
{
    for (auto &parentNodeConnectionInfo : m_parentNodeConnectionStates) {
        parentNodeConnectionInfo.linkIds.remove(id);
    }

    m_outputDeviceLinkIds.remove(id);
    m_inputDeviceLinkIds.remove(id);
    m_allChildNodeLinkIds.remove(id);
    m_allParentNodeLinkIds.remove(id);
}

void LibGoal::Audio::AudioNodeLinkManager::resetNodeLinkingRetries(const uint32_t linkedNodeId)
{
    m_linkRetryAttempts.remove(linkedNodeId);
    auto *retryTimer = m_linkRetryTimers.take(linkedNodeId);
    if (retryTimer != nullptr) {
        retryTimer->stop();
        retryTimer->deleteLater();
    }
}

void LibGoal::Audio::AudioNodeLinkManager::addChildNode(AudioNodeBase *childNode)
{
    for (const auto *existingChildNode : m_childNodes) {
        if (existingChildNode == childNode) {
            return;
        }
    }

    m_childNodes.append(childNode);
}

void LibGoal::Audio::AudioNodeLinkManager::removeChildNode(const AudioNodeBase *childNode)
{
    for (int i = 0; i < m_childNodes.size(); i++) {
        if (m_childNodes.at(i) == childNode) {
            m_childNodes.removeAt(i);
            break;
        }
    }
}

void LibGoal::Audio::AudioNodeLinkManager::initializeConnections()
{
    for (const auto &parentNodeConnectionState : m_parentNodeConnectionStates) {
        if (parentNodeConnectionState.node == nullptr) {
            continue;
        }

        attemptLinkingWithParent(parentNodeConnectionState.node, parentNodeConnectionState.portsMappings);
    }

    attemptLinkingWithOutputDevice(getOutputDevicePortMappings());
    attemptLinkingWithInputDevice(getInputDevicePortMappings());
}

void LibGoal::Audio::AudioNodeLinkManager::attemptLinkingWithParent(const AudioNodeBase *parentNode, const QSet<InputOutputPortsLinkMapping> &portsMappings)
{
    // check connections of the parent node
    if (parentNode == nullptr || !parentNode->isConnected() ||
        !parentNode->getPortManager()->arePortsConnected() || parentNode->getNodeId() == SPA_ID_INVALID) {
        return;
    }

    attemptLinking(parentNode->getNodeId(), portsMappings, true);
}

void LibGoal::Audio::AudioNodeLinkManager::attemptLinkingWithOutputDevice(const QSet<InputOutputPortsLinkMapping> &portsMappings)
{
    const uint32_t outputDeviceNodeId = m_outputDevice.nodeId;
    if (outputDeviceNodeId == SPA_ID_INVALID) {
        return;
    }

    attemptLinking(outputDeviceNodeId, portsMappings, true);
}

void LibGoal::Audio::AudioNodeLinkManager::attemptLinkingWithInputDevice(const QSet<InputOutputPortsLinkMapping> &portsMappings)
{
    const uint32_t inputDeviceNodeId = m_inputDevice.nodeId;
    if (inputDeviceNodeId == SPA_ID_INVALID) {
        return;
    }

    attemptLinking(inputDeviceNodeId, portsMappings, false);
}

void LibGoal::Audio::AudioNodeLinkManager::attemptLinking(uint32_t linkedNodeId, const QSet<InputOutputPortsLinkMapping> &portsMappings, const bool isLinkedNodeInput)
{
    if (m_audioNode->getNodeId() == SPA_ID_INVALID || !m_audioNode->isConnected() || !m_audioNode->getPortManager()->arePortsConnected()) {
        resetNodeLinkingRetries(linkedNodeId);
        return;
    }

    // Check if we've exceeded retry attempts for this node
    if (m_linkRetryAttempts.value(linkedNodeId, 0) >= MAX_LINK_RETRIES) {
        qWarning() << "Maximum retry attempts reached for linking" << m_audioNode->toString() << "to" << linkedNodeId;
        resetNodeLinkingRetries(linkedNodeId);
        return;
    }

    // If already linked, verify the links actually exist in the graph
    if (m_audioNode->isLinked(linkedNodeId)) {
        verifyLinks(linkedNodeId, portsMappings, isLinkedNodeInput);
        return;
    }

    bool success = true;
    pw_properties *props = nullptr;

    Utils::AudioLoopLocker locker = m_audioNode->getAudioLoop()->getAudioLoopLocker();
    const QList<PortInfo> ownNodePorts = isLinkedNodeInput ? m_audioNode->getPortManager()->getOutputPortsInfos() : m_audioNode->getPortManager()->getInputPortsInfos();

    if (isLinkedNodeInput) {
        props = pw_properties_new(PW_KEY_LINK_OUTPUT_NODE, QString::number(m_audioNode->getNodeId()).toLatin1().constData(),
                                  PW_KEY_LINK_INPUT_NODE, QString::number(linkedNodeId).toLatin1().constData(), nullptr);
    } else {
        props = pw_properties_new(PW_KEY_LINK_INPUT_NODE, QString::number(m_audioNode->getNodeId()).toLatin1().constData(),
                                  PW_KEY_LINK_OUTPUT_NODE, QString::number(linkedNodeId).toLatin1().constData(), nullptr);
    }

    if (props == nullptr) {
        qWarning() << "Failed to create properties for linking" << m_audioNode->toString() << "and" << linkedNodeId;
        scheduleRetry(linkedNodeId, portsMappings, isLinkedNodeInput);
        return;
    }

    for (const auto &portMapping : portsMappings) {
        pw_properties_setf(props, PW_KEY_LINK_INPUT_PORT, "%d", portMapping.first);
        pw_properties_setf(props, PW_KEY_LINK_OUTPUT_PORT, "%d", portMapping.second);

        const void *link = pw_core_create_object(m_audioNode->getAudioLoop()->getAudioLoopCore(),
                                                 "link-factory",
                                                 PW_TYPE_INTERFACE_Link,
                                                 PW_VERSION_LINK,
                                                 &props->dict, 0);

        if (link == nullptr) {
            qWarning() << "Failed to create link between" << m_audioNode->toString() << "and" << linkedNodeId
                       << "on input port" << portMapping.first << "and output port" << portMapping.second;
            success = false;
            break;
        }
    }

    if (success) {
        // Schedule verification to ensure links are actually created
        QTimer::singleShot(LINK_RETRY_INTERVAL, this,
                           [this, linkedNodeId, portsMappings, isLinkedNodeInput] {
                               verifyLinks(linkedNodeId, portsMappings, isLinkedNodeInput);
                           });
    } else {
        scheduleRetry(linkedNodeId, portsMappings, isLinkedNodeInput);
    }

    pw_properties_free(props);
}

void LibGoal::Audio::AudioNodeLinkManager::scheduleRetry(uint32_t linkedNodeId, const QSet<InputOutputPortsLinkMapping> &portsMappings, bool isLinkedNodeInput)
{
    const auto *audioLoop = m_audioNode->getAudioLoop();
    if (audioLoop == nullptr) {
        return;
    }
    const int currentAttempt = m_linkRetryAttempts.value(linkedNodeId, 0);
    resetNodeLinkingRetries(linkedNodeId);
    m_linkRetryAttempts[linkedNodeId] = currentAttempt + 1;

    // Calculate exponential backoff with jitter
    auto delay = static_cast<int>(INITIAL_RETRY_DELAY_MS * std::pow(2, currentAttempt));
    const int jitter = QRandomGenerator::global()->bounded(delay / 4);
    delay += jitter;

    // Create new timer for retry
    auto *timer = new QTimer(this);
    m_linkRetryTimers[linkedNodeId] = timer;
    timer->setSingleShot(true);

    QObject::connect(timer, &QTimer::timeout, this, [this, linkedNodeId, portsMappings, isLinkedNodeInput] {
        // Only attempt linking if this node ID is still relevant
        if (linkedNodeId == m_outputDevice.nodeId || linkedNodeId == m_inputDevice.nodeId ||
            std::ranges::any_of(m_parentNodeConnectionStates,
                                [linkedNodeId](const ParentNodeConnectionState &state) {
                                    return state.node->getNodeId() == linkedNodeId;
                                })) {
            attemptLinking(linkedNodeId, portsMappings, isLinkedNodeInput);
            return;
        }

        resetNodeLinkingRetries(linkedNodeId);
    });

    timer->start(delay);
}

void LibGoal::Audio::AudioNodeLinkManager::verifyLinks(const uint32_t linkedNodeId, const QSet<InputOutputPortsLinkMapping> &portsMappings, const bool isLinkedNodeInput)
{
    if (m_audioNode->isLinked(linkedNodeId)) {
        resetNodeLinkingRetries(linkedNodeId);
        Q_EMIT linked();
        return;
    }

    qWarning() << "Links not verified for" << m_audioNode->toString() << "to" << linkedNodeId
               << "- scheduling retry";

    scheduleRetry(linkedNodeId, portsMappings, isLinkedNodeInput);
}

LibGoal::Audio::AudioNodeLinkManager::AudioNodeLinkManager(const AudioNodeSettings &settings, AudioNodeBase *audioNode)
    : QObject{audioNode}
    , m_settings{settings}
    , m_registryListener{new RegistryListener{this, registryListener, registryRemovedListener, audioNode->getAudioLoop(), this}}
    , m_audioNode{audioNode}
{
}

LibGoal::Audio::AudioNodeLinkManager::~AudioNodeLinkManager()
{
    for (const auto &parentNodeConnectionState : m_parentNodeConnectionStates) {
        resetNodeLinkingRetries(parentNodeConnectionState.node->getNodeId());
    }

    resetNodeLinkingRetries(m_outputDevice.nodeId);
    resetNodeLinkingRetries(m_inputDevice.nodeId);
}

QList<LibGoal::Audio::ParentNodeConnectionState> LibGoal::Audio::AudioNodeLinkManager::getParentNodeConnectionStates() const
{
    return m_parentNodeConnectionStates;
}

QSet<uint32_t> LibGoal::Audio::AudioNodeLinkManager::getOutputDeviceLinkIds() const
{
    return m_outputDeviceLinkIds;
}

LibGoal::Audio::AudioInterfaceInfo LibGoal::Audio::AudioNodeLinkManager::getOutputDevice() const
{
    return m_outputDevice;
}

QSet<uint32_t> LibGoal::Audio::AudioNodeLinkManager::getInputDeviceLinkIds() const
{
    return m_inputDeviceLinkIds;
}

LibGoal::Audio::AudioInterfaceInfo LibGoal::Audio::AudioNodeLinkManager::getInputDevice() const
{
    return m_inputDevice;
}

QSet<uint32_t> LibGoal::Audio::AudioNodeLinkManager::getAllChildNodeLinkIds() const
{
    return m_allChildNodeLinkIds;
}

QSet<uint32_t> LibGoal::Audio::AudioNodeLinkManager::getAllParentNodeLinkIds() const
{
    return m_allParentNodeLinkIds;
}

void LibGoal::Audio::AudioNodeLinkManager::setOutputDevice(const AudioInterfaceInfo &outputDevice, QSet<InputOutputPortsLinkMapping> portsMappings)
{
    if (outputDevice.nodeId != SPA_ID_INVALID && m_outputDevice.nodeId == outputDevice.nodeId && m_audioNode->isLinked(outputDevice.nodeId)) {
        return;
    }

    auto *registry = m_audioNode->getAudioLoop()->getAudioLoopRegistry();
    if (registry == nullptr) {
        return;
    }

    // Clean up old device connections
    for (const uint32_t linkId : m_outputDeviceLinkIds) {
        Utils::AudioLoopLocker locker = m_audioNode->getAudioLoop()->getAudioLoopLocker();
        pw_registry_destroy(registry, linkId);
    }

    resetNodeLinkingRetries(m_outputDevice.nodeId);

    m_outputDeviceLinkIds.clear();
    m_outputDevice = outputDevice;

    if (portsMappings.isEmpty()) {
        for (uint32_t i = 0; i < m_settings.audioFormatInfo.channels; i++) {
            portsMappings.insert({i, i});
        }
    }

    m_outputDevicePortMappings = portsMappings;
    attemptLinkingWithOutputDevice(portsMappings);
}

void LibGoal::Audio::AudioNodeLinkManager::setInputDevice(const AudioInterfaceInfo &inputDevice, QSet<InputOutputPortsLinkMapping> portsMappings)
{
    if (inputDevice.nodeId != SPA_ID_INVALID && m_inputDevice.nodeId == inputDevice.nodeId && m_audioNode->isLinked(inputDevice.nodeId)) {
        return;
    }

    auto *registry = m_audioNode->getAudioLoop()->getAudioLoopRegistry();
    if (registry == nullptr) {
        return;
    }

    // Clean up old device connections
    for (const uint32_t linkId : m_inputDeviceLinkIds) {
        Utils::AudioLoopLocker locker = m_audioNode->getAudioLoop()->getAudioLoopLocker();
        pw_registry_destroy(registry, linkId);
    }

    m_inputDeviceLinkIds.clear();
    m_inputDevice = inputDevice;

    if (portsMappings.isEmpty()) {
        for (uint32_t i = 0; i < m_settings.audioFormatInfo.channels; i++) {
            portsMappings.insert({i, i});
        }
    }

    m_inputDevicePortMappings = portsMappings;
    attemptLinkingWithInputDevice(portsMappings);
}

void LibGoal::Audio::AudioNodeLinkManager::addParentNode(AudioNodeBase *parentNode, QSet<InputOutputPortsLinkMapping> portsMappings)
{
    for (const auto &parentNodeConnectionState : m_parentNodeConnectionStates) {
        if (parentNodeConnectionState.node == parentNode) {
            return;
        }
    }

    if (portsMappings.isEmpty()) {
        for (uint32_t i = 0; i < m_settings.audioFormatInfo.channels && i < parentNode->getAudioFormatInfo().channels; i++) {
            portsMappings.insert({i, i});
        }
    }

    ParentNodeConnectionState parentNodeConnectionState;
    parentNodeConnectionState.node = parentNode;
    parentNodeConnectionState.portsMappings = portsMappings;
    parentNodeConnectionState.connections << QObject::connect(parentNode, &AudioNodeBase::nodeConnected, this, [this, portsMappings](const AudioNodeBase *node) {
        attemptLinkingWithParent(node, portsMappings);
    });
    parentNodeConnectionState.connections << QObject::connect(parentNode, &AudioNodeBase::portsConnected, this, [this, portsMappings](const AudioNodeBase *node) {
        attemptLinkingWithParent(node, portsMappings);
    });

    m_parentNodeConnectionStates.append(parentNodeConnectionState);
    attemptLinkingWithParent(parentNode, portsMappings);
}

void LibGoal::Audio::AudioNodeLinkManager::removeParentNode(const AudioNodeBase *parentNode)
{
    for (int i = 0; i < m_parentNodeConnectionStates.size(); i++) {
        const ParentNodeConnectionState &parentNodeConnectionState = m_parentNodeConnectionStates[i];
        if (parentNodeConnectionState.node != parentNode) {
            continue;
        }

        for (const auto &connection : parentNodeConnectionState.connections) {
            QObject::disconnect(connection);
        }

        Utils::AudioLoopLocker locker = m_audioNode->getAudioLoop()->getAudioLoopLocker();
        auto *registry = m_audioNode->getAudioLoop()->getAudioLoopRegistry();
        if (registry != nullptr) {
            for (const uint32_t linkId : parentNodeConnectionState.linkIds) {
                pw_registry_destroy(registry, linkId);
            }
        }

        m_parentNodeConnectionStates.removeAt(i);
        resetNodeLinkingRetries(parentNode->getNodeId());
    }
}

QList<LibGoal::Audio::AudioNodeBase *> LibGoal::Audio::AudioNodeLinkManager::getChildNodes() const
{
    return m_childNodes;
}

QList<LibGoal::Audio::AudioNodeBase *> LibGoal::Audio::AudioNodeLinkManager::getParentNodes() const
{
    QList<AudioNodeBase *> parentNodes;
    for (const auto &parentNodeConnectionState : m_parentNodeConnectionStates) {
        parentNodes.append(parentNodeConnectionState.node);
    }
    return parentNodes;
}

QSet<LibGoal::Audio::InputOutputPortsLinkMapping> LibGoal::Audio::AudioNodeLinkManager::getOutputDevicePortMappings() const
{
    return m_outputDevicePortMappings;
}

QSet<LibGoal::Audio::InputOutputPortsLinkMapping> LibGoal::Audio::AudioNodeLinkManager::getInputDevicePortMappings() const
{
    return m_inputDevicePortMappings;
}
