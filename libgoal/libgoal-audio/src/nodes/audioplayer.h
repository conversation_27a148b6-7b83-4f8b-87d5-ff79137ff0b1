#ifndef AUDIOPLAYER_H
#define AUDIOPLAYER_H

#include "audionodebase.h"

#include <QMutex>
#include <QObject>

#include <pipewire/stream.h>

namespace LibGoal::Audio
{
/**
 * @brief The AudioPlayer class is responsible for playing audio of a single source as a single stream
 */
class AudioPlayer : public AudioNodeBase
{
    Q_OBJECT

public:
    /**
     * @brief Creates a new AudioPlayer instance.
     * @param settings the settings of the audio node
     * @param audioLoop the audio loop instance
     * @param parent QObject parent
     */
    explicit AudioPlayer(const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent = nullptr);

    ~AudioPlayer() override;

    /**
     * @brief plays the audio data on the audio on the next onProcess callback
     * It is recommended to clear the audio buffer before starting to play new media.
     * @param audioData the audio data to be played
     */
    void playAudio(const QByteArray &audioData);
    void clearAudioBuffer();

    bool isMuted() const;
    int getVolumePercentage() const;
    float getVolumeDecibels() const;

public Q_SLOTS:
    bool setMuted(bool muted);
    bool setVolumePercentage(int volumePercentage);
    bool setVolumeDecibels(float volumeDecibels);

    void setNodeName(const QString &nodeName) override;

private:
    Utils::AudioProperties m_properties;
    pw_stream *m_stream = nullptr;
    spa_hook m_streamListener{};
    pw_stream_events m_streamEvents{};
    bool m_muted{false};
    int m_volume{100}; // percentage

    QByteArray m_audioBuffer;
    QMutex m_bufferMutex;
    const qsizetype m_maxBufferSize{1024L * 1024 * 8}; // 8MB

    void processAudio();
    static void onProcess(void *audioPlayerPtr);
    static void onStateChanged(void *data, pw_stream_state old, pw_stream_state state, const char *error);
    static AudioNodeSettings createAudioPlayerSettings(const AudioNodeSettings &settings);
};
} // namespace LibGoal::Audio

#endif // AUDIOPLAYER_H
