#include "audioplayer.h"

#include <QFile>
#include <QMutexLocker>
#include <pipewire/keys.h>
#include <pipewire/node.h>
#include <pipewire/type.h>
#include <spa/debug/types.h>
#include <spa/param/audio/format-utils.h>

void LibGoal::Audio::AudioPlayer::processAudio()
{
    if (m_stream == nullptr || !isConnected() || !getPortManager()->arePortsConnected()) {
        return;
    }

    // Get the next available buffer
    pw_buffer *buffer = pw_stream_dequeue_buffer(m_stream);
    if (buffer == nullptr) {
        return;
    }

    const spa_buffer *spaBuf = buffer->buffer;
    if (spaBuf == nullptr || spaBuf->n_datas == 0 || spaBuf->datas[0].data == nullptr) {
        pw_stream_queue_buffer(m_stream, buffer); // Return the buffer
        return;
    }

    void *bufferData = spaBuf->datas[0].data;
    const qsizetype maxBufferSize = spaBuf->datas[0].maxsize;
    const auto audioFormatInfo = getAudioFormatInfo();

    spaBuf->datas[0].chunk->offset = 0;
    spaBuf->datas[0].chunk->stride = audioFormatInfo.stride;

    // Try to get data from the queue
    QByteArray audioData;
    {
        QMutexLocker locker(&m_bufferMutex);
        if (!m_audioBuffer.isEmpty()) {
            audioData = m_audioBuffer.left(maxBufferSize);
            m_audioBuffer.remove(0, audioData.size());
        }
    }

    if (audioData.isEmpty()) { // No data to play, send silence
        memset(bufferData, 0, maxBufferSize);
        spaBuf->datas[0].chunk->size = 0;
    } else {
        memcpy(bufferData, audioData.constData(), audioData.size());
        spaBuf->datas[0].chunk->size = static_cast<uint32_t>(audioData.size());
    }

    pw_stream_queue_buffer(m_stream, buffer);

    const auto [channelAmplitudes, maxAmplitudes] =
        calculateAudioDataAmplitudes(audioData, audioFormatInfo);
    Q_EMIT amplitudesFeedback(channelAmplitudes, maxAmplitudes);
}

void LibGoal::Audio::AudioPlayer::onProcess(void *audioPlayerPtr)
{
    auto *player = static_cast<AudioPlayer *>(audioPlayerPtr);
    player->processAudio();
}

void LibGoal::Audio::AudioPlayer::onStateChanged(void *data, [[maybe_unused]] pw_stream_state old, const pw_stream_state state, const char *error)
{
    auto *const player = static_cast<AudioPlayer *>(data);
    switch (state) {
    case PW_STREAM_STATE_ERROR:
        qWarning() << "AudioPlayer" << player->getNodeName() << "error:" << error;
        break;
    case PW_STREAM_STATE_PAUSED:
        [[fallthrough]];
    case PW_STREAM_STATE_STREAMING:
        if (player->getNodeId() == SPA_ID_INVALID) {
            player->setNodeId(pw_stream_get_node_id(player->m_stream));
        }

        player->setConnected(true);
        player->setMuted(player->isMuted());
        player->setVolumePercentage(player->getVolumePercentage());
        break;
    case PW_STREAM_STATE_UNCONNECTED:
        player->setConnected(false);
        break;
    default:
        break;
    }

    player->clearAudioBuffer();
}

LibGoal::Audio::AudioNodeSettings LibGoal::Audio::AudioPlayer::createAudioPlayerSettings(const AudioNodeSettings &settings)
{
    AudioNodeSettings playerSettings = settings;
    playerSettings.hasInputPorts = false;
    return playerSettings;
}

LibGoal::Audio::AudioPlayer::AudioPlayer(const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent)
    : AudioNodeBase{createAudioPlayerSettings(settings), audioLoop, parent}
    , m_properties{this, settings.additionalProperties}
    , m_muted{settings.audioSettings.muted}
    , m_volume{settings.audioSettings.volume}
{
    if (getAudioLoop() == nullptr) {
        qCritical() << toString() << "failed to create audio player: audio loop is null";
        return;
    }

    if (m_properties.getProperties() == nullptr) {
        qCritical() << toString() << "failed to create audio properties";
        return;
    }

    m_streamEvents.version = PW_VERSION_STREAM_EVENTS;
    m_streamEvents.state_changed = &onStateChanged;
    m_streamEvents.process = &onProcess;

    const auto loopLocker = getAudioLoop()->getAudioLoopLocker();
    m_stream = pw_stream_new(getAudioLoop()->getAudioLoopCore(), "", m_properties.getProperties());

    if (m_stream == nullptr) {
        qCritical() << toString() << "failed to create audio stream";
        return;
    }

    pw_stream_add_listener(m_stream, &m_streamListener, &m_streamEvents, this);
    QList<const spa_pod *> params = Utils::AudioParamsFactory::fromFormatInfo(getAudioFormatInfo());

    if (pw_stream_connect(m_stream,
                          PW_DIRECTION_OUTPUT,
                          PW_ID_ANY,
                          static_cast<pw_stream_flags>(PW_STREAM_FLAG_MAP_BUFFERS |
                                                       PW_STREAM_FLAG_RT_PROCESS),
                          params.data(), static_cast<int>(params.size())) != 0) {
        qCritical() << toString() << "failed to connect audio stream";
        return;
    }
}

LibGoal::Audio::AudioPlayer::~AudioPlayer()
{
    const auto *audioLoop = getAudioLoop();
    if (audioLoop == nullptr) {
        return;
    }

    Utils::AudioLoopLocker loopLocker = audioLoop->getAudioLoopLocker();
    spa_hook_remove(&m_streamListener);

    if (m_stream != nullptr) {
        pw_stream_destroy(m_stream);
    }
}

void LibGoal::Audio::AudioPlayer::playAudio(const QByteArray &audioData)
{
    if (m_stream == nullptr || audioData.isEmpty() || !isConnected() || !getPortManager()->arePortsConnected()) {
        return;
    }

    {
        QMutexLocker locker(&m_bufferMutex);
        const qsizetype maxBytesToAppend = m_maxBufferSize - m_audioBuffer.size();
        m_audioBuffer.append(audioData.left(maxBytesToAppend));
    }

    if (pw_stream_is_driving(m_stream)) {
        pw_stream_trigger_process(m_stream);
    }
}

void LibGoal::Audio::AudioPlayer::clearAudioBuffer()
{
    QMutexLocker locker(&m_bufferMutex);
    m_audioBuffer.clear();
}

bool LibGoal::Audio::AudioPlayer::isMuted() const
{
    return m_muted;
}

int LibGoal::Audio::AudioPlayer::getVolumePercentage() const
{
    return m_volume;
}

float LibGoal::Audio::AudioPlayer::getVolumeDecibels() const
{
    return Utils::volumePercentageToDecibels(m_volume);
}

bool LibGoal::Audio::AudioPlayer::setMuted(const bool muted)
{
    uint8_t buffer[Utils::POD_DATA_BUILDER_BUFFER_SIZE];
    auto builder = SPA_POD_BUILDER_INIT(buffer, sizeof(buffer));

    const auto *param = static_cast<const spa_pod *>(spa_pod_builder_add_object(&builder,
                                                                                SPA_TYPE_OBJECT_Props, SPA_PARAM_Props,
                                                                                SPA_PROP_mute, SPA_POD_Bool(muted)));

    if (param == nullptr) {
        return false;
    }

    Utils::AudioLoopLocker loopLocker = getAudioLoop()->getAudioLoopLocker();
    const bool success = pw_stream_set_param(m_stream, SPA_PARAM_Props, param) == 0;
    if (success) {
        m_muted = muted;
    }

    return success;
}

bool LibGoal::Audio::AudioPlayer::setVolumePercentage(const int volumePercentage)
{
    uint8_t buffer[Utils::POD_DATA_BUILDER_BUFFER_SIZE];
    auto builder = SPA_POD_BUILDER_INIT(buffer, sizeof(buffer));

    const float volume = std::clamp(Utils::volumePercentageToCubicFloat(volumePercentage), Utils::MIN_PIPEWIRE_VOLUME, Utils::MAX_PIPEWIRE_VOLUME);
    const uint32_t channelCount = getAudioFormatInfo().channels;

    std::vector volumes(channelCount, volume);
    const auto *param = static_cast<const spa_pod *>(
        spa_pod_builder_add_object(&builder,
                                   SPA_TYPE_OBJECT_Props,
                                   SPA_PARAM_Props,
                                   SPA_PROP_channelVolumes,
                                   SPA_POD_Array(sizeof(float), SPA_TYPE_Float, channelCount, volumes.data())));

    if (param == nullptr) {
        return false;
    }

    Utils::AudioLoopLocker loopLocker = getAudioLoop()->getAudioLoopLocker();
    const bool success = pw_stream_set_param(m_stream, SPA_PARAM_Props, param) == 0;
    if (success) {
        m_volume = volumePercentage;
    }

    return success;
}

bool LibGoal::Audio::AudioPlayer::setVolumeDecibels(const float volumeDecibels)
{
    return setVolumePercentage(Utils::decibelsToVolumePercentage(volumeDecibels));
}

void LibGoal::Audio::AudioPlayer::setNodeName(const QString &nodeName)
{
    AudioNodeBase::setNodeName(nodeName);

    Utils::AudioProperties properties{{
        {PW_KEY_NODE_NAME, nodeName},
        {PW_KEY_NODE_DESCRIPTION, nodeName},
        {PW_KEY_NODE_NICK, nodeName},
    }};
    properties.setCleanup(true);

    Utils::AudioLoopLocker loopLocker = getAudioLoop()->getAudioLoopLocker();
    pw_stream_update_properties(m_stream, &properties.getProperties()->dict);
}
