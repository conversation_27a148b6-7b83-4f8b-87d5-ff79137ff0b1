#include "audionodebase.h"

#include <QRandomGenerator>
#include <pipewire/keys.h>
#include <pipewire/link.h>
#include <pipewire/port.h>

LibGoal::Audio::AudioNodeBase::AudioNodeBase(const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent)
    : QObject{parent}
    , m_settings{settings}
    , m_audioLoop{audioLoop}
    , m_portManager{new AudioNodePortManager{settings, this}}
    , m_linkManager{new AudioNodeLinkManager{settings, this}}
{
    for (const auto &parentNodeSetup : m_settings.parentNodesSetups) {
        getLinkManager()->addParentNode(parentNodeSetup.first, parentNodeSetup.second);
        parentNodeSetup.first->getLinkManager()->addChildNode(this);
    }

    QObject::connect(audioLoop, &AudioLoop::availableOutputDevicesChanged, this, &AudioNodeBase::reconnectToOutputDeviceCandidate);

    QObject::connect(audioLoop, &AudioLoop::defaultOutputChanged, this, [this](const AudioInterfaceInfo &outputInfo) {
        if (m_useDefaultOutputDevice) {
            setOutputDevice(outputInfo);
        }
    });

    QObject::connect(m_portManager, &AudioNodePortManager::portsConnected, this, [this] {
        Q_EMIT portsConnected(this);
        getLinkManager()->initializeConnections();
    });

    QObject::connect(getLinkManager(), &AudioNodeLinkManager::linked, this, [this] {
        Q_EMIT linked(this);
    });
}

LibGoal::Audio::AudioNodeBase::~AudioNodeBase()
{
    const auto *audioLoop = getAudioLoop();
    if (audioLoop == nullptr) {
        return;
    }

    for (auto const *childNode : getLinkManager()->getChildNodes()) {
        childNode->getLinkManager()->removeParentNode(this);
    }

    for (auto const *parentNode : getLinkManager()->getParentNodes()) {
        parentNode->getLinkManager()->removeChildNode(this);
    }
}

bool LibGoal::Audio::AudioNodeBase::isConnected() const
{
    return m_connected;
}

bool LibGoal::Audio::AudioNodeBase::isLinked(const uint32_t nodeId) const
{
    for (const auto &parentNodeConnectionState : getLinkManager()->getParentNodeConnectionStates()) {
        if (parentNodeConnectionState.node->getNodeId() == nodeId) {
            return parentNodeConnectionState.linkIds.size() >= parentNodeConnectionState.portsMappings.size();
        }
    }

    const bool isLinkedAsOutputDevice = getLinkManager()->getOutputDevice().nodeId == nodeId && getLinkManager()->getOutputDeviceLinkIds().size() >= getLinkManager()->getOutputDevicePortMappings().size();
    const bool isLinkedAsInputDevice = getLinkManager()->getInputDevice().nodeId == nodeId && getLinkManager()->getInputDeviceLinkIds().size() >= getLinkManager()->getInputDevicePortMappings().size();

    return isLinkedAsOutputDevice || isLinkedAsInputDevice;
}

bool LibGoal::Audio::AudioNodeBase::arePortsConnected() const
{
    return m_portManager->arePortsConnected();
}

bool LibGoal::Audio::AudioNodeBase::isConnectedToAnOutputDevice() const
{
    return getLinkManager()->getOutputDevice().nodeId != SPA_ID_INVALID;
}

bool LibGoal::Audio::AudioNodeBase::isLinkedEndToEnd() const
{
    const bool isLinkedFromInput = getPortManager()->getInputPortsInfos().isEmpty() || !getLinkManager()->getAllChildNodeLinkIds().isEmpty();
    const bool isLinkedFromOutput = getPortManager()->getOutputPortsInfos().isEmpty() || !getLinkManager()->getAllParentNodeLinkIds().isEmpty();

    return isLinkedFromInput && isLinkedFromOutput;
}

uint32_t LibGoal::Audio::AudioNodeBase::getNodeId() const
{
    return m_nodeId;
}

void LibGoal::Audio::AudioNodeBase::setConnected(const bool connected)
{
    if (m_connected == connected) {
        return;
    }

    m_connected = connected;
    if (connected) {
        Q_EMIT nodeConnected(this);
    }
}

void LibGoal::Audio::AudioNodeBase::setOutputDevice(const AudioInterfaceInfo &outputDevice, const QSet<InputOutputPortsLinkMapping> &portsMappings) const
{
    getLinkManager()->setOutputDevice(outputDevice, portsMappings);
}

void LibGoal::Audio::AudioNodeBase::setInputDevice(const AudioInterfaceInfo &inputDevice, const QSet<InputOutputPortsLinkMapping> &portsMappings) const
{
    getLinkManager()->setInputDevice(inputDevice, portsMappings);
}

void LibGoal::Audio::AudioNodeBase::setNodeId(const uint32_t nodeId)
{
    if (m_nodeId == nodeId) {
        return;
    }

    m_nodeId = nodeId;
}

void LibGoal::Audio::AudioNodeBase::reconnectToOutputDeviceCandidate(const QList<AudioInterfaceInfo> &availableOutputDevices) const
{
    for (const auto &outputDevice : availableOutputDevices) {
        if (outputDevice.name != getLinkManager()->getOutputDevice().name) {
            continue;
        }

        setOutputDevice(outputDevice);
        return;
    }
}

QString LibGoal::Audio::AudioNodeBase::getNodeName() const
{
    return m_settings.nodeName;
}

LibGoal::Audio::Utils::AudioFormatInfo LibGoal::Audio::AudioNodeBase::getAudioFormatInfo() const
{
    return m_settings.audioFormatInfo;
}

LibGoal::Audio::AudioNodeSettings LibGoal::Audio::AudioNodeBase::getAudioSettings() const
{
    return m_settings;
}

LibGoal::Audio::AudioLoop *LibGoal::Audio::AudioNodeBase::getAudioLoop() const
{
    return m_audioLoop;
}

QString LibGoal::Audio::AudioNodeBase::toString() const
{
    return QString{"%1 (id: %2)"}.arg(m_settings.nodeName).arg(m_nodeId);
}

void LibGoal::Audio::AudioNodeBase::setNodeName(const QString &nodeName)
{
    m_settings.nodeName = nodeName;
}

void LibGoal::Audio::AudioNodeBase::connectToOutputDevice(const AudioInterfaceInfo &outputInfo, const QSet<InputOutputPortsLinkMapping> &portsMappings) const
{
    for (const auto &output : getAudioLoop()->getDeviceListener()->getOutputDevices()) {
        if (output.name != outputInfo.name) {
            continue;
        }

        setOutputDevice(output, portsMappings);
        return;
    }

    // output device not found, set it anyway and reconnect when it becomes available
    AudioInterfaceInfo outputCandidate = outputInfo;
    outputCandidate.nodeId = SPA_ID_INVALID;
    setOutputDevice(outputCandidate, portsMappings);
}

void LibGoal::Audio::AudioNodeBase::connectToDefaultOutputDevice(const QSet<InputOutputPortsLinkMapping> &portsMappings)
{
    m_useDefaultOutputDevice = true;
    setOutputDevice(m_audioLoop->getDeviceListener()->getDefaultOutput(), portsMappings);
}

void LibGoal::Audio::AudioNodeBase::disconnectOutputDevice()
{
    m_useDefaultOutputDevice = false;
    setOutputDevice({});
}

void LibGoal::Audio::AudioNodeBase::connectToInputDevice(const AudioInterfaceInfo &inputInfo, const QSet<InputOutputPortsLinkMapping> &portsMappings) const
{
    for (const auto &input : getAudioLoop()->getDeviceListener()->getInputDevices()) {
        if (input.name != inputInfo.name) {
            continue;
        }

        setInputDevice(input, portsMappings);
        return;
    }

    // input device not found, set it anyway and reconnect when it becomes available
    AudioInterfaceInfo inputCandidate = inputInfo;
    inputCandidate.nodeId = SPA_ID_INVALID;
    setInputDevice(inputCandidate, portsMappings);
}

void LibGoal::Audio::AudioNodeBase::disconnectInputDevice()
{
    m_useDefaultInputDevice = false;
    setInputDevice({});
}

void LibGoal::Audio::AudioNodeBase::connectToDefaultInputDevice(const QSet<InputOutputPortsLinkMapping> &portsMappings)
{
    m_useDefaultInputDevice = true;
    setInputDevice(m_audioLoop->getDeviceListener()->getDefaultInput(), portsMappings);
}

LibGoal::Audio::AudioNodePortManager *LibGoal::Audio::AudioNodeBase::getPortManager() const
{
    return m_portManager;
}

LibGoal::Audio::AudioNodeLinkManager *LibGoal::Audio::AudioNodeBase::getLinkManager() const
{
    return m_linkManager;
}
