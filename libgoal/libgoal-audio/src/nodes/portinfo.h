#ifndef PORTINFO_H
#define PORTINFO_H

#include <QString>
#include <spa/utils/defs.h>

namespace LibGoal::Audio
{
using InputOutputPortsLinkMapping = QPair<uint32_t, uint32_t>;

static constexpr QLatin1StringView PORT_OUT_DIRECTION_KEY{"out"};
static constexpr QLatin1StringView PORT_IN_DIRECTION_KEY{"in"};

enum class AudioNodePortDirection {
    In,
    Out,
    Unknown
};

/**
 * @brief The PortInfo struct is a struct for storing port information (used for AudioNodeBase ports).
 */
struct PortInfo {
    uint32_t portId{SPA_ID_INVALID};
    uint32_t nodeId{SPA_ID_INVALID};
    uint32_t index{SPA_ID_INVALID};
    AudioNodePortDirection direction{AudioNodePortDirection::Unknown};
    QString channel;

    PortInfo() = default;

    static AudioNodePortDirection getDirection(const QString &direction);
    QString toString() const;
};
} // namespace LibGoal::Audio

#endif // PORTINFO_H
