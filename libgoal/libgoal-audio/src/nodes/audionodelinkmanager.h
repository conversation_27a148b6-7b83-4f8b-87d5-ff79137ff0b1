#ifndef AUDIONODELINKMANAGER_H
#define AUDIONODELINKMANAGER_H

#include "audiointerfaceinfo.h"
#include "audionodesettings.h"
#include "portinfo.h"
#include "registrylistener.h"

#include <QObject>
#include <QTimer>

namespace LibGoal::Audio
{

struct ParentNodeConnectionState {
    AudioNodeBase *node{nullptr};

    // in no particular order
    QList<QMetaObject::Connection> connections;
    QSet<uint32_t> linkIds;
    QSet<InputOutputPortsLinkMapping> portsMappings;
};

/**
 * @brief The AudioLinkManager class is responsible for managing the links between an audio node and its parent nodes.
 */
class AudioNodeLinkManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief AudioLinkManager constructor. Creates its own registry listener.
     * @param settings The audio node settings.
     * @param audioNode The parent audio node.
     */
    explicit AudioNodeLinkManager(const AudioNodeSettings &settings, AudioNodeBase *audioNode);
    ~AudioNodeLinkManager() override;

    QList<ParentNodeConnectionState> getParentNodeConnectionStates() const;
    QSet<uint32_t> getOutputDeviceLinkIds() const;
    AudioInterfaceInfo getOutputDevice() const;

    QSet<uint32_t> getInputDeviceLinkIds() const;
    AudioInterfaceInfo getInputDevice() const;

    QSet<uint32_t> getAllChildNodeLinkIds() const;
    QSet<uint32_t> getAllParentNodeLinkIds() const;

    void setOutputDevice(const AudioInterfaceInfo &outputDevice, QSet<InputOutputPortsLinkMapping> portsMappings = {});
    void setInputDevice(const AudioInterfaceInfo &inputDevice, QSet<InputOutputPortsLinkMapping> portsMappings = {});

    void addParentNode(AudioNodeBase *parentNode, QSet<InputOutputPortsLinkMapping> portsMappings = {});
    void removeParentNode(const AudioNodeBase *parentNode);

    void addChildNode(AudioNodeBase *childNode);
    void removeChildNode(const AudioNodeBase *childNode);

    void initializeConnections();

    QList<AudioNodeBase *> getChildNodes() const;
    QList<AudioNodeBase *> getParentNodes() const;

    QSet<InputOutputPortsLinkMapping> getOutputDevicePortMappings() const;
    QSet<InputOutputPortsLinkMapping> getInputDevicePortMappings() const;

Q_SIGNALS:
    void linked();

private Q_SLOTS:
    void attemptLinkingWithParent(const AudioNodeBase *parentNode, const QSet<InputOutputPortsLinkMapping> &portsMappings);
    void attemptLinkingWithOutputDevice(const QSet<InputOutputPortsLinkMapping> &portsMappings);
    void attemptLinkingWithInputDevice(const QSet<InputOutputPortsLinkMapping> &portsMappings);
    void attemptLinking(uint32_t linkedNodeId, const QSet<InputOutputPortsLinkMapping> &portsMappings, bool isLinkedNodeInput);
    void scheduleRetry(uint32_t linkedNodeId, const QSet<InputOutputPortsLinkMapping> &portsMappings, bool isLinkedNodeInput);
    void verifyLinks(uint32_t linkedNodeId, const QSet<InputOutputPortsLinkMapping> &portsMappings, bool isLinkedNodeInput);

private:
    AudioNodeSettings m_settings;

    QList<ParentNodeConnectionState> m_parentNodeConnectionStates;
    QList<AudioNodeBase *> m_childNodes;

    QSet<uint32_t> m_outputDeviceLinkIds;
    QSet<uint32_t> m_inputDeviceLinkIds;

    // captures ALL PipeWire links, not just the ones properly assigned
    QSet<uint32_t> m_allChildNodeLinkIds;
    QSet<uint32_t> m_allParentNodeLinkIds;

    QHash<uint32_t, int> m_linkRetryAttempts;
    QHash<uint32_t, QTimer *> m_linkRetryTimers;

    RegistryListener *m_registryListener = nullptr;
    AudioNodeBase *m_audioNode = nullptr;

    AudioInterfaceInfo m_outputDevice{};
    QSet<InputOutputPortsLinkMapping> m_outputDevicePortMappings;
    AudioInterfaceInfo m_inputDevice{};
    QSet<InputOutputPortsLinkMapping> m_inputDevicePortMappings;

    static void registryListener(void *linkManagerPtr, uint32_t id, uint32_t permissions, const char *type, uint32_t version, const spa_dict *props);
    static void registryRemovedListener(void *linkManagerPtr, uint32_t id);

    void linkConnectionListener(uint32_t id, uint32_t permissions, const char *type, uint32_t version, const spa_dict *props);
    void linkRemovedListener(uint32_t id);

    void resetNodeLinkingRetries(uint32_t linkedNodeId);
};
} // namespace LibGoal::Audio

#endif // AUDIONODELINKMANAGER_H
