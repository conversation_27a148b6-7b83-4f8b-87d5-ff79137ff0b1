#include "virtualaudiosink.h"

#include <pipewire/keys.h>
#include <spa/param/audio/raw-utils.h>
#include <utility>

static constexpr auto VIRTUAL_AUDIO_SINK_MEDIA_CATEGORY = "Sink";

void LibGoal::Audio::VirtualAudioSink::processAudio() const
{
    pw_buffer *buffer = pw_stream_dequeue_buffer(m_stream);
    if (buffer == nullptr) {
        return;
    }

    const spa_buffer *spaBuf = buffer->buffer;
    if (spaBuf == nullptr) {
        qCritical() << toString() << "Invalid buffer!";
        return;
    }

    QByteArray audioData;
    for (uint32_t i = 0; i < spaBuf->n_datas; ++i) {
        const auto &currentDataSegment = spaBuf->datas[i];
        if (currentDataSegment.data == nullptr || currentDataSegment.chunk == nullptr) {
            qCritical() << toString() << "Invalid data or chunk at index" << i;
            continue;
        }

        const uint32_t bufferSize = currentDataSegment.chunk->size;
        currentDataSegment.chunk->offset = 0;
        currentDataSegment.chunk->size = bufferSize;
        currentDataSegment.chunk->stride = getAudioFormatInfo().stride;

        audioData.append(static_cast<char *>(currentDataSegment.data), bufferSize);
    }

    if (!audioData.isEmpty()) {
        m_onProcessHandler(audioData);
        const auto [channelAmplitudes, maxAmplitudes] = Utils::calculateAudioDataAmplitudes(audioData, getAudioFormatInfo());
        Q_EMIT amplitudesFeedback(channelAmplitudes, maxAmplitudes);
    }

    pw_stream_queue_buffer(m_stream, buffer);
}

void LibGoal::Audio::VirtualAudioSink::onStateChanged(void *data, [[maybe_unused]] pw_stream_state old, const pw_stream_state state, const char *error)
{
    auto *const device = static_cast<VirtualAudioSink *>(data);
    switch (state) {
    case PW_STREAM_STATE_ERROR:
        qWarning() << "VirtualOutputDevice" << device->getNodeName() << "error:" << error;
        break;
    case PW_STREAM_STATE_PAUSED:
        [[fallthrough]];
    case PW_STREAM_STATE_STREAMING:
        if (device->getNodeId() == SPA_ID_INVALID) {
            device->setNodeId(pw_stream_get_node_id(device->m_stream));
        }
        device->setConnected(true);
        break;
    case PW_STREAM_STATE_UNCONNECTED:
        device->setConnected(false);
        break;
    default:
        break;
    }
}

void LibGoal::Audio::VirtualAudioSink::onProcess(void *data)
{
    const auto *device = static_cast<VirtualAudioSink *>(data);
    device->processAudio();
}

QList<QPair<QString, QString>> LibGoal::Audio::VirtualAudioSink::setupAdditionalProperties(const AudioNodeSettings &settings)
{
    auto properties = settings.additionalProperties;

    properties.append({{PW_KEY_MEDIA_CLASS, AudioDeviceListener::AUDIO_SINK_MEDIA_CLASS}});
    properties.append({{PW_KEY_MEDIA_CATEGORY, VIRTUAL_AUDIO_SINK_MEDIA_CATEGORY}});
    properties.append({{PW_KEY_NODE_PASSIVE, "true"}});
    properties.append({{PW_KEY_NODE_ALWAYS_PROCESS, "true"}});
    properties.append({{PW_KEY_NODE_VIRTUAL, "true"}});

    return properties;
}

LibGoal::Audio::AudioNodeSettings LibGoal::Audio::VirtualAudioSink::createVirtualAudioSinkSettings(const AudioNodeSettings &settings)
{
    AudioNodeSettings virtualAudioSinkSettings = settings;
    virtualAudioSinkSettings.hasInputPorts = true;
    return virtualAudioSinkSettings;
}

LibGoal::Audio::VirtualAudioSink::VirtualAudioSink(AudioSinkProcessHandler audioProcessHandler, const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent)
    : AudioNodeBase{createVirtualAudioSinkSettings(settings), audioLoop, parent}
    , m_properties{this, setupAdditionalProperties(settings)}
    , m_onProcessHandler{std::move(audioProcessHandler)}
{
    if (getAudioLoop() == nullptr) {
        qCritical() << toString() << "failed to create virtual output device: audio loop is null";
        return;
    }

    if (m_properties.getProperties() == nullptr) {
        qCritical() << toString() << "failed to create audio properties";
        return;
    }

    m_streamEvents.version = PW_VERSION_STREAM_EVENTS;
    m_streamEvents.state_changed = &onStateChanged;
    m_streamEvents.process = &onProcess;

    const auto loopLocker = getAudioLoop()->getAudioLoopLocker();
    m_stream = pw_stream_new(getAudioLoop()->getAudioLoopCore(), "", m_properties.getProperties());

    if (m_stream == nullptr) {
        qCritical() << toString() << "failed to create audio stream";
        return;
    }

    pw_stream_add_listener(m_stream, &m_streamListener, &m_streamEvents, this);
    QList<const spa_pod *> params = Utils::AudioParamsFactory::fromFormatInfo(getAudioFormatInfo());

    if (pw_stream_connect(m_stream,
                          PW_DIRECTION_INPUT,
                          PW_ID_ANY,
                          static_cast<pw_stream_flags>(PW_STREAM_FLAG_MAP_BUFFERS |
                                                       PW_STREAM_FLAG_RT_PROCESS),
                          params.data(), static_cast<int>(params.size())) != 0) {
        qCritical() << toString() << "failed to connect audio stream";
        return;
    }
}

LibGoal::Audio::VirtualAudioSink::~VirtualAudioSink()
{
    const auto *audioLoop = getAudioLoop();
    if (audioLoop == nullptr) {
        return;
    }

    Utils::AudioLoopLocker loopLocker = audioLoop->getAudioLoopLocker();
    spa_hook_remove(&m_streamListener);

    if (m_stream != nullptr) {
        pw_stream_destroy(m_stream);
    }
}
