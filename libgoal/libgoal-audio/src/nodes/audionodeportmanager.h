#ifndef AUDIONODEPORTMANAGER_H
#define AUDIONODEPORTMANAGER_H

#include "audionodesettings.h"
#include "portinfo.h"
#include "registrylistener.h"

#include <QObject>

namespace LibGoal::Audio
{
/**
 * @brief The AudioNodePortManager class is responsible for managing the ports of an audio node.
 */
class AudioNodePortManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief AudioNodePortManager constructor. Creates its own registry listener.
     * @param settings The audio node settings.
     * @param audioNode The parent object.
     */
    explicit AudioNodePortManager(const AudioNodeSettings &settings, AudioNodeBase *audioNode);

    bool arePortsConnected() const;

    QList<PortInfo> getInputPortsInfos() const;
    QList<PortInfo> getOutputPortsInfos() const;

    static PortInfo getRegistryEvenPortInfo(uint32_t id, uint32_t permissions, const char *type, uint32_t version, const spa_dict *props);

Q_SIGNALS:
    void portsConnected();

private:
    QList<PortInfo> m_inputPortsInfos;
    QList<PortInfo> m_outputPortsInfos;
    AudioNodeSettings m_settings;

    RegistryListener *m_registryListener = nullptr;
    AudioNodeBase *m_audioNode = nullptr;

    static void registryListener(void *portManagerPtr, uint32_t id, uint32_t permissions, const char *type, uint32_t version, const spa_dict *props);
    static void registryRemovedListener(void *portManagerPtr, uint32_t id);

    void portConnectionListener(uint32_t id, uint32_t permissions, const char *type, uint32_t version, const spa_dict *props);
    void portRemovedListener(uint32_t id);
    void registerPortInfo(const PortInfo &portInfo);
};
} // namespace LibGoal::Audio

#endif // AUDIONODEPORTMANAGER_H
