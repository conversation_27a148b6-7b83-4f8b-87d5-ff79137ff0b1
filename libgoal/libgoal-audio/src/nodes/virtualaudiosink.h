#ifndef VIRTUALAUDIOSINK_H
#define VIRTUALAUDIOSINK_H

#include "audioloop.h"
#include "audionodebase.h"

#include <QObject>

#include <pipewire/stream.h>

namespace LibGoal::Audio
{
using AudioSinkProcessHandler = std::function<void(const QByteArray &audioDataBuffer)>;

/**
 * @brief The VirtualAudioSink class is responsible for creating a virtual audio output device.
 */
class VirtualAudioSink : public AudioNodeBase
{
    Q_OBJECT

public:
    /**
     * @brief Creates a new VirtualAudioSink instance.
     * @param audioProcessHandler the handler to be called when the incoming audio data is to be processed
     * @param settings the settings of the audio sink
     * @param audioLoop the audio loop instance
     * @param parent QObject parent
     */
    explicit VirtualAudioSink(AudioSinkProcessHandler audioProcessHandler, const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent = nullptr);
    ~VirtualAudioSink() override;

private:
    Utils::AudioProperties m_properties;
    pw_stream *m_stream = nullptr;
    spa_hook m_streamListener{};
    pw_stream_events m_streamEvents{};
    AudioSinkProcessHandler m_onProcessHandler;

    void processAudio() const;
    static void onStateChanged(void *data, pw_stream_state old, pw_stream_state state, const char *error);
    static void onProcess(void *data);
    static QList<QPair<QString, QString>> setupAdditionalProperties(const AudioNodeSettings &settings);
    static AudioNodeSettings createVirtualAudioSinkSettings(const AudioNodeSettings &settings);
};
} // namespace LibGoal::Audio

#endif // VIRTUALAUDIOSINK_H
