#include "portinfo.h"

LibGoal::Audio::AudioNodePortDirection LibGoal::Audio::PortInfo::getDirection(const QString &direction)
{
    if (direction == PORT_OUT_DIRECTION_KEY) {
        return AudioNodePortDirection::Out;
    }

    if (direction == PORT_IN_DIRECTION_KEY) {
        return AudioNodePortDirection::In;
    }

    return AudioNodePortDirection::Unknown;
}

QString LibGoal::Audio::PortInfo::toString() const
{
    return QString("PortId: %1, NodeId: %2, Index: %3, Direction: %4, Channel: %5")
        .arg(portId)
        .arg(nodeId)
        .arg(index)
        .arg(static_cast<int>(direction))
        .arg(channel);
}
