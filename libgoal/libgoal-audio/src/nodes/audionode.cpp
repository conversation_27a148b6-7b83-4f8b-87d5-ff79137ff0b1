#include "audionode.h"

void LibGoal::Audio::AudioNode::passThroughFilter(const QList<float *> &inputBuffers,
                                                  const QList<float *> &outputBuffers, const uint32_t sampleCount)
{
    for (int i = 0; i < inputBuffers.size(); i++) {
        const float *const in = inputBuffers.at(i);
        float *const out = outputBuffers.at(i);

        if (in == nullptr || out == nullptr) {
            continue;
        }

        memcpy(out, in, sampleCount * sizeof(float));
    }
}

LibGoal::Audio::AudioNode::AudioNode(const AudioNodeSettings &settings, AudioLoop *audioLoop, QObject *parent)
    : AudioFilter{passThroughFilter, settings, audioLoop, parent}
{
}
