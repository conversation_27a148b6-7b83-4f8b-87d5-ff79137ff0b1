#include "audioutils.h"

#include "nodes/audionodebase.h"

#include <QCoreApplication>
#include <QJsonDocument>

#include <pipewire/keys.h>
#include <pipewire/node.h>
#include <spa/param/audio/raw-utils.h>
#include <spa/utils/json-pod.h>

static constexpr auto DEFAULT_PW_MEDIA_TYPE = "Audio";
static constexpr auto DEFAULT_PW_MEDIA_CATEGORY = "Playback";
static constexpr auto DEFAULT_PW_MEDIA_ROLE = "DSP";

float LibGoal::Audio::Utils::decibelsToIecScale(const float decibels)
{
    float fScale = 1.0f;

    if (decibels < -70.0f) {
        fScale = 0.0f;
    } else if (decibels < -60.0f) {
        fScale = (decibels + 70.0f) * 0.0025f;
    } else if (decibels < -50.0f) {
        fScale = (decibels + 60.0f) * 0.005f + 0.025f;
    } else if (decibels < -40.0) {
        fScale = (decibels + 50.0f) * 0.0075f + 0.075f;
    } else if (decibels < -30.0f) {
        fScale = (decibels + 40.0f) * 0.015f + 0.15f;
    } else if (decibels < -20.0f) {
        fScale = (decibels + 30.0f) * 0.02f + 0.3f;
    } else if (decibels < -0.001f || decibels > 0.001f) { /* if (dB < 0.0f) */
        fScale = (decibels + 20.0f) * 0.025f + 0.5f;
    }

    return fScale;
}

float LibGoal::Audio::Utils::iecScaleToDecibels(const float iecScale)
{
    float dB = 0.0f;

    if (iecScale < 0.025f) { // IEC_Scale(-60.0f)
        dB = iecScale / 0.0025f - 70.0f;
    } else if (iecScale < 0.075f) { // IEC_Scale(-50.0f)
        dB = (iecScale - 0.025f) / 0.005f - 60.0f;
    } else if (iecScale < 0.15f) { // IEC_Scale(-40.0f)
        dB = (iecScale - 0.075f) / 0.0075f - 50.0f;
    } else if (iecScale < 0.3f) { // IEC_Scale(-30.0f)
        dB = (iecScale - 0.15f) / 0.015f - 40.0f;
    } else if (iecScale < 0.5f) { // IEC_Scale(-20.0f)
        dB = (iecScale - 0.3f) / 0.02f - 30.0f;
    } else { // IEC_Scale(0.0f))
        dB = (iecScale - 0.5f) / 0.025f - 20.0f;
    }

    return dB > -0.001f && dB < 0.001f ? 0.0f : dB;
}

float LibGoal::Audio::Utils::volumePercentageToCubicFloat(const int volumePercentage)
{
    return std::pow(static_cast<float>(volumePercentage) / 100.0f, 3.0f);
}

int LibGoal::Audio::Utils::cubicFloatToVolumePercentage(const float cubicFloat)
{
    return static_cast<int>(std::cbrt(cubicFloat) * 100.0f);
}

float LibGoal::Audio::Utils::volumePercentageToVolumeLinear(const int volumePercentage)
{
    return static_cast<float>(volumePercentage) / 100.0f;
}

int LibGoal::Audio::Utils::volumeLinearToVolumePercentage(const float volumeLinear)
{
    return static_cast<int>(volumeLinear * 100.0f);
}

float LibGoal::Audio::Utils::decibelsToVolumeLinear(const float decibels)
{
    return std::pow(10.0f, decibels / 20.0f);
}

float LibGoal::Audio::Utils::volumeLinearToDecibels(const float volumeLinear)
{
    return 20.0f * std::log10(volumeLinear);
}

float LibGoal::Audio::Utils::volumePercentageToDecibels(const int volumePercentage)
{
    return volumeLinearToDecibels(volumePercentageToVolumeLinear(volumePercentage));
}

int LibGoal::Audio::Utils::decibelsToVolumePercentage(const float decibels)
{
    return volumeLinearToVolumePercentage(decibelsToVolumeLinear(decibels));
}

LibGoal::Audio::Utils::AudioSettings LibGoal::Audio::Utils::determineAudioSettings(const CompoundAudioSettings &settings)
{
    switch (settings.propagationMode) {
    case AudioPropagationMode::Absolute:
        return settings.instanceSettings;
    case AudioPropagationMode::Relative: {
        AudioSettings newSettings;
        newSettings.volume = settings.parentSettings.volume * settings.instanceSettings.volume / 100;
        newSettings.muted = settings.parentSettings.muted || settings.instanceSettings.muted;
        return newSettings;
    }
    case AudioPropagationMode::Inherit:
        [[fallthrough]];
    default:
        return settings.parentSettings;
    }
}

LibGoal::Audio::Utils::AudioLoopLocker::AudioLoopLocker(pw_thread_loop *pwThreadLoop)
    : m_pwThreadLoop{pwThreadLoop}
{
    pw_thread_loop_lock(m_pwThreadLoop);
}

LibGoal::Audio::Utils::AudioLoopLocker::~AudioLoopLocker()
{
    unlock();
}

void LibGoal::Audio::Utils::AudioLoopLocker::unlock() const
{
    pw_thread_loop_unlock(m_pwThreadLoop);
}

LibGoal::Audio::Utils::AudioProperties::AudioProperties(pw_properties *pwProperties)
    : m_pwProperties{pwProperties}
{
}

LibGoal::Audio::Utils::AudioProperties::AudioProperties(const AudioNodeBase *audioNode, const QList<QPair<QString, QString>> &properties)
{
    const QByteArray nodeName = audioNode->getNodeName().toLatin1();
    m_pwProperties = pw_properties_new(PW_KEY_MEDIA_TYPE, DEFAULT_PW_MEDIA_TYPE, PW_KEY_MEDIA_CATEGORY, DEFAULT_PW_MEDIA_CATEGORY,
                                       PW_KEY_MEDIA_ROLE, DEFAULT_PW_MEDIA_ROLE, PW_KEY_APP_NAME, QCoreApplication::applicationName().toLatin1().constData(),
                                       PW_KEY_NODE_NAME, nodeName.constData(), PW_KEY_NODE_NICK, nodeName.constData(),
                                       PW_KEY_NODE_DESCRIPTION, nodeName.constData(), nullptr);

    for (const auto &[key, value] : properties) {
        pw_properties_set(m_pwProperties, key.toLatin1().constData(), value.toLatin1().constData());
    }
}

LibGoal::Audio::Utils::AudioProperties::AudioProperties(const QList<QPair<QString, QString>> &properties)
{
    m_pwProperties = pw_properties_new(nullptr, nullptr); // 2 nullptrs to fit the sentinels

    for (const auto &[key, value] : properties) {
        pw_properties_set(m_pwProperties, key.toLatin1().constData(), value.toLatin1().constData());
    }
}

LibGoal::Audio::Utils::AudioProperties::~AudioProperties()
{
    if (m_cleanup && m_pwProperties != nullptr) {
        auto *properties = m_pwProperties;
        m_pwProperties = nullptr;
        pw_properties_free(properties);
    }
}

pw_properties *LibGoal::Audio::Utils::AudioProperties::getProperties() const
{
    return m_pwProperties;
}

void LibGoal::Audio::Utils::AudioProperties::setCleanup(const bool cleanup)
{
    m_cleanup = cleanup;
}

QAudioFormat::SampleFormat LibGoal::Audio::Utils::AudioFormatInfo::toAudioFormat(const spa_audio_format format)
{
    switch (format) {
    case SPA_AUDIO_FORMAT_S8:
        [[fallthrough]];
    case SPA_AUDIO_FORMAT_U8:
        return QAudioFormat::UInt8;
    case SPA_AUDIO_FORMAT_S16:
        [[fallthrough]];
    case SPA_AUDIO_FORMAT_U16:
        return QAudioFormat::Int16;
    case SPA_AUDIO_FORMAT_S32:
        [[fallthrough]];
    case SPA_AUDIO_FORMAT_U32:
        return QAudioFormat::Int32;
    case SPA_AUDIO_FORMAT_F32:
        [[fallthrough]];
    case SPA_AUDIO_FORMAT_F64:
        return QAudioFormat::Float;
    default:
        return QAudioFormat::Unknown;
    }
}

QAudioFormat::ChannelConfig LibGoal::Audio::Utils::AudioFormatInfo::toAudioChannelConfig(const uint32_t channels)
{
    switch (channels) {
    case 1:
        return QAudioFormat::ChannelConfigMono;
    case 2:
        return QAudioFormat::ChannelConfigStereo;
    case 3:
        return QAudioFormat::ChannelConfig3Dot0;
    case 4:
        return QAudioFormat::ChannelConfig3Dot1;
    case 5:
        return QAudioFormat::ChannelConfigSurround5Dot0;
    case 6:
        return QAudioFormat::ChannelConfigSurround5Dot1;
    case 7:
        return QAudioFormat::ChannelConfigSurround7Dot0;
    case 8:
        return QAudioFormat::ChannelConfigSurround7Dot1;
    default:
        return QAudioFormat::ChannelConfigUnknown;
    }
}

int LibGoal::Audio::Utils::AudioFormatInfo::getSampleSizeInBytes(const spa_audio_format format)
{
    QAudioFormat audioFormat;
    audioFormat.setSampleFormat(toAudioFormat(format));

    return audioFormat.bytesPerSample();
}

QAudioFormat LibGoal::Audio::Utils::AudioFormatInfo::toAudioFormat() const
{
    QAudioFormat audioFormat;
    audioFormat.setChannelConfig(toAudioChannelConfig(channels));
    audioFormat.setSampleFormat(toAudioFormat(format));
    audioFormat.setSampleRate(rate);
    audioFormat.setChannelCount(channels);

    return audioFormat;
}

LibGoal::Audio::Utils::AudioAmplitudes LibGoal::Audio::Utils::calculateAudioDataAmplitudes(const QByteArray &audioData, const AudioFormatInfo &audioFormatInfo)
{
    QList<QList<float>> channelsAmplitudes;
    QList<float> peakAmplitudes;

    const uint32_t sampleSize = audioFormatInfo.sampleSize;
    const uint32_t channels = audioFormatInfo.channels;

    if (sampleSize <= 0 || channels == 0) {
        return {};
    }

    const uint32_t totalSamples = static_cast<uint32_t>(audioData.size()) / sampleSize;
    const uint32_t samplesPerChannel = totalSamples / channels;

    for (uint32_t channel = 0; channel < channels; ++channel) {
        QList<float> amplitudes;
        float peakAmplitude = -std::numeric_limits<float>::infinity();

        for (uint32_t sampleIndex = 0; sampleIndex < samplesPerChannel; ++sampleIndex) {
            const uint32_t index = (sampleIndex * channels + channel) * sampleSize;

            float amplitude = 0.0f;
            switch (audioFormatInfo.format) {
            case SPA_AUDIO_FORMAT_S16: {
                const auto *const samplePtr = reinterpret_cast<const int16_t *>(audioData.constData() + index);
                amplitude = static_cast<float>(*samplePtr) / 32768.0f; // Normalize 16-bit signed int
                break;
            }
            case SPA_AUDIO_FORMAT_U16: {
                const auto *const samplePtr = reinterpret_cast<const uint16_t *>(audioData.constData() + index);
                amplitude = (static_cast<float>(*samplePtr) - 32768.0f) / 32768.0f; // Normalize 16-bit unsigned int
                break;
            }
            case SPA_AUDIO_FORMAT_F32: {
                const auto *samplePtr = reinterpret_cast<const float *>(audioData.constData() + index);
                amplitude = *samplePtr;
                break;
            }
            case SPA_AUDIO_FORMAT_S8: {
                const auto *samplePtr = reinterpret_cast<const int8_t *>(audioData.constData() + index);
                amplitude = static_cast<float>(*samplePtr) / 128.0f; // Normalize 8-bit signed int
                break;
            }
            case SPA_AUDIO_FORMAT_U8: {
                const auto *samplePtr = reinterpret_cast<const uint8_t *>(audioData.constData() + index);
                amplitude = (static_cast<float>(*samplePtr) - 128.0f) / 128.0f; // Normalize 8-bit unsigned int
                break;
            }
            default:
                qWarning() << "Unsupported audio format:" << audioFormatInfo.format;
                return {};
            }

            amplitudes.append(std::abs(amplitude));
            peakAmplitude = std::max(peakAmplitude, volumeLinearToDecibels(std::abs(amplitude)));
        }

        channelsAmplitudes.append(amplitudes);
        peakAmplitudes.append(peakAmplitude);
    }

    return {channelsAmplitudes, peakAmplitudes};
}

QList<const spa_pod *> LibGoal::Audio::Utils::AudioParamsFactory::fromFormatInfo(const AudioFormatInfo &formatInfo)
{
    QList<const spa_pod *> params;
    uint8_t buffer[POD_DATA_BUILDER_BUFFER_SIZE];
    auto builder = SPA_POD_BUILDER_INIT(buffer, sizeof(buffer));
    auto audioInfo = SPA_AUDIO_INFO_RAW_INIT(formatInfo.format, formatInfo.flags, formatInfo.rate, formatInfo.channels);

    params.append(spa_format_audio_raw_build(&builder, SPA_PARAM_EnumFormat, &audioInfo));

    params.append(static_cast<const spa_pod *>(spa_pod_builder_add_object(&builder,
                                                                          SPA_TYPE_OBJECT_ParamBuffers, SPA_PARAM_Buffers,
                                                                          SPA_PARAM_BUFFERS_buffers, SPA_POD_Int(formatInfo.buffersCount),
                                                                          SPA_PARAM_BUFFERS_blocks, SPA_POD_Int(formatInfo.blocksCount),
                                                                          SPA_PARAM_BUFFERS_size, SPA_POD_Int(formatInfo.bufferSize),
                                                                          SPA_PARAM_BUFFERS_stride, SPA_POD_Int(formatInfo.stride),
                                                                          SPA_PARAM_BUFFERS_align, SPA_POD_Int(formatInfo.align))));

    return params;
}

QList<const spa_pod *> LibGoal::Audio::Utils::AudioParamsFactory::defaultParams()
{
    return fromFormatInfo({});
}

QList<const spa_pod *> LibGoal::Audio::Utils::AudioParamsFactory::fromJson(const QJsonDocument &json, uint32_t type)
{
    uint8_t buffer[POD_DATA_BUILDER_BUFFER_SIZE];
    auto builder = SPA_POD_BUILDER_INIT(buffer, sizeof(buffer));
    const QByteArray jsonData = json.toJson();

    const spa_type_info *typeInfo = spa_debug_type_find(spa_type_param, type);
    if (typeInfo == nullptr) {
        return {};
    }

    if (spa_json_to_pod(&builder, 0, typeInfo, jsonData.constData(), static_cast<int>(jsonData.length())) < 0) {
        return {};
    }

    const spa_pod *pod = spa_pod_builder_deref(&builder, 0);
    if (pod == nullptr) {
        return {};
    }

    return {pod};
}
