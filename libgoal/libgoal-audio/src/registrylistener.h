#ifndef REGISTRYLISTENER_H
#define REGISTRYLISTENER_H

#include "audioutils.h"

#include <QObject>

#include <pipewire/core.h>
#include <spa/utils/hook.h>

namespace LibGoal::Audio
{
using RegistryCallback = void (*)(void *data, uint32_t id, uint32_t permissions, const char *type, uint32_t version, const spa_dict *props);
using RegistryRemovedCallback = void (*)(void *data, uint32_t id);

class AudioLoop;

/**
 * @brief The RegistryListener class is responsible for listening to PipeWire registry events
 * and calling the provided callback function. Listens to the events generated immediately after the creation of the object.
 */
class RegistryListener : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Creates a new RegistryListener instance, starts listening to the PipeWire registry events straight away.
     * @param callbackData the data to be passed to the callback function
     * @param registryCallback the callback function to be called when a registry event is received
     * @param audioLoop the AudioLoop instance
     * @param parent QObject parent
     */
    explicit RegistryListener(void *callbackData, Registry<PERSON>allback registryCallback, AudioLoop *audioLoop, QObject *parent = nullptr);

    /**
     * @brief Creates a new RegistryListener instance, starts listening to the PipeWire registry events straight away.
     * @param callbackData the data to be passed to the callback function
     * @param registryCallback the callback function to be called when a registry event is received
     * @param registryRemovedCallback the callback function to be called when a registry object is removed
     * @param audioLoop the AudioLoop instance
     * @param parent QObject parent
     */
    explicit RegistryListener(void *callbackData, RegistryCallback registryCallback, RegistryRemovedCallback registryRemovedCallback,
                              AudioLoop *audioLoop, QObject *parent = nullptr);

    ~RegistryListener() override;

private:
    spa_hook m_registryListener{};
    pw_registry_events m_registryEvents{};

    AudioLoop *m_audioLoop = nullptr;
    void *m_callbackData = nullptr;
};
} // namespace LibGoal::Audio

#endif // REGISTRYLISTENER_H
