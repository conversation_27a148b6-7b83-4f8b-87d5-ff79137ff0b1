#include "registrylistener.h"
#include "audioloop.h"

LibGoal::Audio::RegistryListener::RegistryListener(void *callbackData, const RegistryCallback registryCallback, AudioLoop *audioLoop, QObject *parent)
    : QObject{parent}
    , m_registryEvents{.version = PW_VERSION_CORE_EVENTS, .global = registryCallback, .global_remove = nullptr}
    , m_audioLoop{audioLoop}
    , m_callbackData{callbackData}
{
    Utils::AudioLoopLocker locker = m_audioLoop->getAudioLoopLocker();
    pw_registry_add_listener(m_audioLoop->getAudioLoopRegistry(), &m_registryListener, &m_registryEvents, m_callbackData);
}

LibGoal::Audio::RegistryListener::RegistryListener(void *callbackData, const RegistryCallback registryCallback, const RegistryRemovedCallback registryRemovedCallback,
                                                   AudioLoop *audioLoop, QObject *parent)
    : QObject{parent}
    , m_registryEvents{.version = PW_VERSION_CORE_EVENTS, .global = registryCallback, .global_remove = registryRemovedCallback}
    , m_audioLoop{audioLoop}
    , m_callbackData{callbackData}
{
    Utils::AudioLoopLocker locker = m_audioLoop->getAudioLoopLocker();
    pw_registry_add_listener(m_audioLoop->getAudioLoopRegistry(), &m_registryListener, &m_registryEvents, m_callbackData);
}

LibGoal::Audio::RegistryListener::~RegistryListener()
{
    spa_hook_remove(&m_registryListener);
}
