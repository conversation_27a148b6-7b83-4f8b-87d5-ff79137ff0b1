cmake_minimum_required(VERSION 3.19)

if(NOT WIN32 AND NOT APPLE)

find_package(Qt6 REQUIRED COMPONENTS Core Multimedia)

find_package(PkgConfig REQUIRED)
pkg_check_modules(LV2 REQUIRED IMPORTED_TARGET lv2)
pkg_check_modules(LILV REQUIRED IMPORTED_TARGET lilv-0)
pkg_check_modules(PipeWire REQUIRED IMPORTED_TARGET libpipewire-0.3)

project(GSS_libgoal_audio VERSION 0.1 LANGUAGES CXX)
libGoalProject(GSS_libgoal_audio)

add_library(${PROJECT_NAME} STATIC
        src/audioloop.h
        src/audiodevicelistener.h
        src/audioutils.h
        src/registrylistener.h
        src/nodes/audiofilter.h
        src/nodes/audionode.h
        src/nodes/audionodebase.h
        src/nodes/audionodelinkmanager.h
        src/nodes/audionodeportmanager.h
        src/nodes/audionodesettings.h
        src/nodes/audiointerfaceinfo.h
        src/nodes/audioplayer.h
        src/nodes/portinfo.h
        src/nodes/virtualaudiosink.h
        src/nodes/plugins/audiofilterpluginbase.h
        src/nodes/plugins/volumefilter.h
        src/nodes/plugins/limiter.h

        src/audioloop.cpp
        src/audiodevicelistener.cpp
        src/audioutils.cpp
        src/registrylistener.cpp
        src/nodes/audiofilter.cpp
        src/nodes/audionode.cpp
        src/nodes/audionodebase.cpp
        src/nodes/audionodelinkmanager.cpp
        src/nodes/audionodeportmanager.cpp
        src/nodes/audioplayer.cpp
        src/nodes/portinfo.cpp
        src/nodes/virtualaudiosink.cpp
        src/nodes/plugins/audiofilterpluginbase.cpp
        src/nodes/plugins/volumefilter.cpp
        src/nodes/plugins/limiter.cpp
)

add_library(GSS::libgoal::audio ALIAS ${PROJECT_NAME})

if (NOT ${LIBGOAL_STANDALONE_BUILD})
    set_target_properties(${PROJECT_NAME} PROPERTIES EXCLUDE_FROM_ALL TRUE)
    message(STATUS "removed target ${PROJECT_NAME} from ALL")
endif()

target_include_directories(${PROJECT_NAME} PUBLIC
        ${CMAKE_CURRENT_LIST_DIR}/src
        ${CMAKE_CURRENT_LIST_DIR}/src/nodes
        ${CMAKE_CURRENT_LIST_DIR}/src/nodes/plugins
)

target_link_libraries(${PROJECT_NAME} PUBLIC
        Qt6::Core
        Qt6::Multimedia
        PkgConfig::PipeWire
        PkgConfig::LV2
        PkgConfig::LILV
)

endif()
