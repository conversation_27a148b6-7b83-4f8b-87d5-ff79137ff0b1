#include "subscriber.h"

LibGoal::Mqtt::SubscriberCallback::SubscriberCallback(MqttBase *parent)
    : CallbackBase(parent)
{
}

void LibGoal::Mqtt::SubscriberCallback::message_arrived(const mqtt::const_message_ptr msg)
{
    Q_EMIT messageArrived(QByteArray::fromStdString(msg->get_payload_str()));
}

LibGoal::Mqtt::Subscriber::Subscriber(const QString &clientId, const MqttBase::ServerSettings &serverSettings,
                                      const QStringList &topicParts, const MqttBase::QualityOfService qos, QObject *parent)
    : MqttBase(clientId, serverSettings, topicParts, parent)
    , m_qos(qos)
{
    auto *callback = new SubscriberCallback(this);

    QObject::connect(callback, &CallbackBase::connectionEstablished, this, &Subscriber::setupSubscription);
    QObject::connect(callback, &SubscriberCallback::messageArrived, this, &Subscriber::messageReceived);

    m_callback = callback;
    m_client->set_callback(*m_callback);

    try {
        m_client->connect(*m_connectOptions);
    } catch (const mqtt::exception &e) {
        qWarning() << "subscriber" << getTopic() << "failed to connect:" << e.what();
    }
}

LibGoal::Mqtt::Subscriber::Subscriber(const MqttBase::ServerSettings &serverSettings, const QStringList &topicParts,
                                      const MqttBase::QualityOfService qos, QObject *parent)
    : Subscriber(generateClientId(topicParts), serverSettings, topicParts, qos, parent)
{
}

LibGoal::Mqtt::Subscriber::Subscriber(const QString &clientId, const MqttBase::ServerSettings &serverSettings,
                                      const QString &topic, MqttBase::QualityOfService qos, QObject *parent)
    : Subscriber(clientId, serverSettings, QStringList{topic}, qos, parent)
{
}

LibGoal::Mqtt::Subscriber::Subscriber(const MqttBase::ServerSettings &serverSettings, const QString &topic, const MqttBase::QualityOfService qos, QObject *parent)
    : Subscriber(generateClientId(topic), serverSettings, QStringList{topic}, qos, parent)
{
}

LibGoal::Mqtt::Subscriber::Subscriber(const QString &clientId, const MqttBase::ServerSettings &serverSettings,
                                      const QStringList &topicParts, QObject *parent)
    : Subscriber(clientId, serverSettings, topicParts, MqttBase::QualityOfService::ExactlyOnce, parent)
{
}

LibGoal::Mqtt::Subscriber::Subscriber(const MqttBase::ServerSettings &serverSettings, const QStringList &topicParts, QObject *parent)
    : Subscriber(generateClientId(topicParts), serverSettings, topicParts, parent)
{
}

LibGoal::Mqtt::Subscriber::Subscriber(const QString &clientId, const MqttBase::ServerSettings &serverSettings, const QString &topic, QObject *parent)
    : Subscriber(clientId, serverSettings, QStringList{topic}, parent)
{
}

LibGoal::Mqtt::Subscriber::Subscriber(const MqttBase::ServerSettings &serverSettings, const QString &topic, QObject *parent)
    : Subscriber(generateClientId(topic), serverSettings, QStringList{topic}, parent)
{
}

void LibGoal::Mqtt::Subscriber::setupSubscription() const
{
    m_client->subscribe(getTopic(), m_qos);
}
