#include "mqttbase.h"

#include <QDebug>
#include <QUuid>

static constexpr auto CONNECTION_TIMEOUT_S = 10;
static constexpr auto KEEPALIVE_INTERVAL_S = 3;

static constexpr auto DISCONNECT_CLEANUP_WAIT_TIMEOUT_MS = 1000;
static constexpr auto DISCONNECT_WAIT_TIMEOUT_MS = 2000;

static constexpr auto MAX_INFLIGHT_MESSAGES = 2048;
static constexpr auto MAX_BUFFERED_MESSAGES = 1024;

static constexpr auto MIN_RECONNECT_INTERVAL_S = 1;
static constexpr auto MAX_RECONNECT_INTERVAL_S = 5;

LibGoal::Mqtt::CallbackBase::CallbackBase(MqttBase *parent)
    : QObject(parent)
{
    if (parent == nullptr) {
        return;
    }

    QObject::connect(this, &CallbackBase::connectionEstablished, parent, &MqttBase::connectionEstablished);
    QObject::connect(this, &CallbackBase::connectionLost, parent, &MqttBase::connectionLost);
}

void LibGoal::Mqtt::CallbackBase::connection_lost(const std::string &cause)
{
    qDebug() << "connection lost" + (cause.empty() ? "" : "cause: " + cause);
    Q_EMIT connectionLost(QString::fromStdString(cause));
}

void LibGoal::Mqtt::CallbackBase::connected(const std::string &cause)
{
    Q_EMIT connectionEstablished(QString::fromStdString(cause));
}

bool LibGoal::Mqtt::MqttBase::disconnect() const
{
    try {
        m_client->disable_callbacks();
        m_client->stop_consuming();

        if (!isConnected()) {
            return true;
        }

        if (!m_client->disconnect(DISCONNECT_CLEANUP_WAIT_TIMEOUT_MS)->wait_for(DISCONNECT_WAIT_TIMEOUT_MS)) {
            qWarning() << "disconnecting from broker timed out";
            return false;
        }
    } catch (const mqtt::exception &e) {
        qWarning() << "disconnecting from broker failed:" << e.what();
        return false;
    }
    return true;
}

LibGoal::Mqtt::MqttBase::MqttBase(const QString &clientId, const ServerSettings &serverSettings,
                                  const QStringList &topicParts, QObject *parent)
    : QObject(parent)
    , m_serverSettings(serverSettings)
    , m_topic(topicParts.join(Mqtt::TOPIC_SEPARATOR).toStdString())
    , m_client(QSharedPointer<mqtt::async_client>::create(serverSettings.getServerAddress().toStdString(), clientId.toStdString(), MAX_BUFFERED_MESSAGES))
    , m_connectOptions(QSharedPointer<mqtt::connect_options>::create())
{
    m_connectOptions->set_clean_session(true);
    m_connectOptions->set_connect_timeout(CONNECTION_TIMEOUT_S);
    m_connectOptions->set_keep_alive_interval(KEEPALIVE_INTERVAL_S);
    m_connectOptions->set_max_inflight(MAX_INFLIGHT_MESSAGES);
    m_connectOptions->set_automatic_reconnect(MIN_RECONNECT_INTERVAL_S, MAX_RECONNECT_INTERVAL_S);

    if (m_serverSettings.LWT.has_value()) {
        m_connectOptions->set_will_message(mqtt::message(m_topic, m_serverSettings.LWT.value()));
    }

    // TODO: provide trust store?
    mqtt::ssl_options ssl_opts;
    ssl_opts.set_enable_server_cert_auth(false);
    m_connectOptions->set_ssl(ssl_opts);
}

LibGoal::Mqtt::MqttBase::~MqttBase()
{
    disconnect();
}

QString LibGoal::Mqtt::MqttBase::generateClientId(const QStringList &topicParts)
{
    return QString("%1%2%3").arg(topicParts.join(Mqtt::TOPIC_SEPARATOR), Mqtt::TOPIC_SEPARATOR, QUuid::createUuid().toString(QUuid::WithoutBraces));
}

QString LibGoal::Mqtt::MqttBase::generateClientId(const QString &topic)
{
    return generateClientId(QStringList{topic});
}
