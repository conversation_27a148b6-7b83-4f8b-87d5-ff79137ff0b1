#ifndef PUBLISHER_H
#define PUBLISHER_H

#include "mqttbase.h"

#include <mqtt/async_client.h>

#include <QObject>

namespace LibGoal::Mqtt
{
class PublisherCallback : public CallbackBase
{
    Q_OBJECT

    void delivery_complete(mqtt::delivery_token_ptr token) override;

public:
    explicit PublisherCallback(MqttBase *parent = nullptr);

    Q_SIGNALS:
        void deliveryComplete(const mqtt::const_message_ptr &message);
};

/**
 * @brief Class responsible for publishing on a specific MQTT topic. A message is published by calling the publish method.
 * The messageDelivered signal containing the message is emitted when the message is delivered to the broker.
 * @note The API of this class is thread-safe
 */
class Publisher : public MqttBase
{
    Q_OBJECT

    bool m_retain;

public:
    /**
     * @brief Construct a new Publisher object
     * @param clientId The MQTT client ID, has to be unique for each client at the broker scope
     * @param serverSettings The MQTT server settings
     * @param topicParts QStringList of topic parts to be joined to 1 topic
     * @param retain Whether the message should be retained by the broker
     * @param parent The parent QObject
     */
    explicit Publisher(const QString &clientId, const MqttBase::ServerSettings &serverSettings,
                       const QStringList &topicParts, bool retain, QObject *parent = nullptr);

    /**
     * @brief Construct a new Publisher object
     * @param serverSettings The MQTT server settings
     * @param topicParts QStringList of topic parts to be joined to 1 topic
     * @param retain Whether the message should be retained by the broker
     * @param parent The parent QObject
     */
    explicit Publisher(const MqttBase::ServerSettings &serverSettings,
                       const QStringList &topicParts, bool retain, QObject *parent = nullptr);

    /**
     * @brief Construct a new Publisher object
     * @param clientId The MQTT client ID, has to be unique for each client at the broker scope
     * @param serverSettings The MQTT server settings
     * @param topicParts QStringList of topic parts to be joined to 1 topic
     * @param parent The parent QObject
     */
    explicit Publisher(const QString &clientId, const MqttBase::ServerSettings &serverSettings,
                       const QStringList &topicParts, QObject *parent = nullptr);

    /**
     * @brief Construct a new Publisher object
     * @param serverSettings The MQTT server settings
     * @param topicParts QStringList of topic parts to be joined to 1 topic
     * @param parent The parent QObject
     */
    explicit Publisher(const MqttBase::ServerSettings &serverSettings,
                       const QStringList &topicParts, QObject *parent = nullptr);

    bool getRetain() const { return m_retain; }
    void setRetain(bool retain) { m_retain = retain; }

    /**
     * @brief Publishes a message on the topic
     * @param messagePayload The message payload
     * @param qos The quality of service to use
     * @return The delivery token to track the delivery of the message
     */
    mqtt::delivery_token_ptr publish(const QByteArray &messagePayload, MqttBase::QualityOfService qos = MqttBase::QualityOfService::ExactlyOnce) const;

    /**
     * @brief Publishes a message on the topic
     * @param messageString The message string
     * @param qos The quality of service to use
     * @return The delivery token to track the delivery of the message
     */
    mqtt::delivery_token_ptr publish(const QString &messageString, MqttBase::QualityOfService qos = MqttBase::QualityOfService::ExactlyOnce) const;

    /**
     * @brief Publishes a message on the topic
     * @param messageString The message string
     * @param qos The quality of service to use
     * @return The delivery token to track the delivery of the message
     */
    mqtt::delivery_token_ptr publish(const std::string &messageString, MqttBase::QualityOfService qos = MqttBase::QualityOfService::ExactlyOnce) const;

    Q_SIGNALS:
        void messageDelivered(const mqtt::const_message_ptr &message);
};
} // namespace LibGoal::Mqtt

#endif // PUBLISHER_H
