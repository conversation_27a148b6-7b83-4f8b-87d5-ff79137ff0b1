#ifndef MQTTBASE_H
#define MQTTBASE_H

#include <mqtt/async_client.h>

#include <QSettings>
#include <QSharedPointer>
#include <QString>

namespace LibGoal::Mqtt
{
constexpr auto TOPIC_SEPARATOR = "/";
constexpr auto TOPIC_MULTILEVEL_WILDCARD = "#";
constexpr auto TRANSPORT_PROTOCOL = "ssl";

constexpr auto DEFAULT_BROKER_HOST = "localhost";
constexpr auto DEFAULT_BROKER_PORT = 8883;

constexpr auto CONNECTION_RETRY_TIMEOUT_MS = 3000;
constexpr auto MESSAGE_TOKEN_WAIT_TIMEOUT_MS = 1000;

class MqttBase;

/**
 * @brief TThe MQTT callback handler base class for the Publisher and the Subscriber
 */
class CallbackBase : public QObject, public mqtt::callback
{
    Q_OBJECT

protected:
    explicit CallbackBase(MqttBase *parent = nullptr);

    void connection_lost(const std::string &cause) override;
    void connected(const std::string &cause) override;

Q_SIGNALS:
    void connectionEstablished(const QString &cause);
    void connectionLost(const QString &cause);
};

/**
 * @brief The MQTT base class for the Publisher and the Subscriber
 */
class MqttBase : public QObject
{
    Q_OBJECT

public:
    enum QualityOfService {
        AtMostOnce = 0,
        AtLeastOnce = 1,
        ExactlyOnce = 2
    };

    /**
     * @brief The MQTT broker server settings
     */
    struct ServerSettings {
        QString host;
        int port;
        QString transportProtocol = Mqtt::TRANSPORT_PROTOCOL;
        std::optional<std::string> LWT = std::nullopt; // Last Will and Testament (LWT)

        QString getServerAddress() const
        {
            return QString("%1://%2:%3").arg(transportProtocol, host, QString::number(port));
        }
    };

    ~MqttBase() override;

    bool isConnected() const { return m_client->is_connected(); }
    bool disconnect() const;

    /**
     * @brief Returns the full topic
     * @return The full topic as a string
     */
    std::string getTopic() const { return m_topic; }

    void setTopic(const std::string_view &topic) { m_topic = topic; }
    void setTopic(const QString &topic) { setTopic(topic.toStdString()); }

    /**
     * @brief Generates a client ID based on the topic prefix and the topic
     * @param topicParts The topic parts
     * @return The generated client ID as a string
     */
    static QString generateClientId(const QStringList &topicParts);

    /**
     * @brief Generates a client ID based on the topic
     * @param topic The topic
     * @return The generated client ID as a string
     */
    static QString generateClientId(const QString &topic);

Q_SIGNALS:
    void connectionLost(const QString &cause);
    void connectionEstablished(const QString &cause);

protected:
    MqttBase(const QString &clientId, const MqttBase::ServerSettings &serverSettings,
             const QStringList &topicParts, QObject *parent);

    MqttBase::ServerSettings m_serverSettings;
    std::string m_topic;

    QSharedPointer<mqtt::async_client> m_client;
    QSharedPointer<mqtt::connect_options> m_connectOptions;
    mqtt::callback *m_callback = nullptr;
};
} // namespace Libgoal::Mqtt

#endif // MQTTBASE_H
