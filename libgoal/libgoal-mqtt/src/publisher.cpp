#include "publisher.h"

LibGoal::Mqtt::PublisherCallback::PublisherCallback(MqttBase *parent)
    : CallbackBase(parent)
{
}

void LibGoal::Mqtt::PublisherCallback::delivery_complete(const mqtt::delivery_token_ptr token)
{
    Q_EMIT deliveryComplete(token->get_message());
}

LibGoal::Mqtt::Publisher::Publisher(const QString &clientId, const MqttBase::ServerSettings &serverSettings,
                                    const QStringList &topicParts, const bool retain, QObject *parent)
    : MqttBase(clientId, serverSettings, topicParts, parent)
    , m_retain(retain)
{
    auto *callback = new PublisherCallback(this);
    QObject::connect(callback, &PublisherCallback::deliveryComplete, this, &Publisher::messageDelivered);

    m_callback = callback;
    m_client->set_callback(*m_callback);

    try {
        m_client->connect(*m_connectOptions);
    } catch (const mqtt::exception &e) {
        qWarning() << "publisher" << getTopic() << "failed to connect:" << e.what();
    }
}

LibGoal::Mqtt::Publisher::Publisher(const MqttBase::ServerSettings &serverSettings, const QStringList &topicParts, const bool retain, QObject *parent)
    : Publisher(generateClientId(topicParts), serverSettings, topicParts, retain, parent)
{
}

LibGoal::Mqtt::Publisher::Publisher(const QString &clientId, const MqttBase::ServerSettings &serverSettings,
                                    const QStringList &topicParts, QObject *parent)
    : Publisher(clientId, serverSettings, topicParts, false, parent)
{
}

LibGoal::Mqtt::Publisher::Publisher(const MqttBase::ServerSettings &serverSettings, const QStringList &topicParts, QObject *parent)
    : Publisher(generateClientId(topicParts), serverSettings, topicParts, parent)
{
}

mqtt::delivery_token_ptr LibGoal::Mqtt::Publisher::publish(const QByteArray &messagePayload, const MqttBase::QualityOfService qos) const
{
    return publish(messagePayload.toStdString(), qos);
}

mqtt::delivery_token_ptr LibGoal::Mqtt::Publisher::publish(const QString &messageString, const MqttBase::QualityOfService qos) const
{
    return publish(messageString.toStdString(), qos);
}

mqtt::delivery_token_ptr LibGoal::Mqtt::Publisher::publish(const std::string &messageString, const MqttBase::QualityOfService qos) const
{
    if (m_topic.empty()) {
        qWarning() << "trying to publish to an undefined topic";
        return nullptr;
    }

    if (!isConnected()) {
        qWarning() << "publisher" << getTopic() << "is disconnected, cannot publish";
        return nullptr;
    }

    try {
        return m_client->publish(getTopic(), messageString, qos, m_retain);
    } catch (const mqtt::exception &e) {
        qWarning() << "publishing to" << getTopic() << "failed:" << e.what();
        return nullptr;
    }
}
