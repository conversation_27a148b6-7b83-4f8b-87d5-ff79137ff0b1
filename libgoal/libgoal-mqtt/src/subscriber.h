#ifndef SUBSCRIBER_H
#define SUBSCRIBER_H

#include "mqttbase.h"

#include <mqtt/async_client.h>

#include <QObject>

namespace LibGoal::Mqtt
{
class SubscriberCallback : public CallbackBase
{
    Q_OBJECT

    void message_arrived(mqtt::const_message_ptr msg) override;

public:
    explicit SubscriberCallback(MqttBase *parent = nullptr);

    Q_SIGNALS:
        void messageArrived(const QByteArray &payload);
};

/**
 * @brief Class responsible for subscribing to a specific MQTT topic.
 * The messageReceived signal containing the message payload is emitted when a message from the topic is received.
 * @note The API of this class is thread-safe
 */
class Subscriber : public MqttBase
{
    Q_OBJECT

    MqttBase::QualityOfService m_qos;

public:
    /**
     * @brief Construct a new Subscriber object
     * @param clientId The MQTT client ID, has to be unique for each client at the broker scope
     * @param serverSettings The MQTT server settings
     * @param topicParts QStringList of topic parts to be joined to 1 topic
     * @param qos The quality of service to use
     * @param parent The parent QObject
     */
    explicit Subscriber(const QString &clientId, const MqttBase::ServerSettings &serverSettings,
                        const QStringList &topicParts, MqttBase::QualityOfService qos, QObject *parent = nullptr);

    /**
     * @brief Construct a new Subscriber object
     * @param serverSettings The MQTT server settings
     * @param topicParts QStringList of topic parts to be joined to 1 topic
     * @param qos The quality of service to use
     * @param parent The parent QObject
     */
    explicit Subscriber(const MqttBase::ServerSettings &serverSettings,
                        const QStringList &topicParts, MqttBase::QualityOfService qos, QObject *parent = nullptr);

    /**
     * @brief Construct a new Subscriber object
     * @param clientId The MQTT client ID, has to be unique for each client at the broker scope
     * @param serverSettings The MQTT server settings
     * @param topic The topic to subscribe to
     * @param qos The quality of service to use
     * @param parent The parent QObject
     */
    explicit Subscriber(const QString &clientId, const MqttBase::ServerSettings &serverSettings,
                        const QString &topic, MqttBase::QualityOfService qos, QObject *parent = nullptr);

    /**
     * @brief Construct a new Subscriber object
     * @param serverSettings The MQTT server settings
     * @param topic The topic to subscribe to
     * @param qos The quality of service to use
     * @param parent The parent QObject
     */
    explicit Subscriber(const MqttBase::ServerSettings &serverSettings,
                        const QString &topic, MqttBase::QualityOfService qos, QObject *parent = nullptr);

    /**
     * @brief Construct a new Subscriber object
     * @param clientId The MQTT client ID, has to be unique for each client at the broker scope
     * @param serverSettings The MQTT server settings
     * @param topicParts QStringList of topic parts to be joined to 1 topic
     * @param parent The parent QObject
     */
    explicit Subscriber(const QString &clientId, const MqttBase::ServerSettings &serverSettings,
                        const QStringList &topicParts, QObject *parent = nullptr);

    /**
     * @brief Construct a new Subscriber object
     * @param serverSettings The MQTT server settings
     * @param topicParts QStringList of topic parts to be joined to 1 topic
     * @param parent The parent QObject
     */
    explicit Subscriber(const MqttBase::ServerSettings &serverSettings,
                        const QStringList &topicParts, QObject *parent = nullptr);

    /**
     * @brief Construct a new Subscriber object
     * @param clientId The MQTT client ID, has to be unique for each client at the broker scope
     * @param serverSettings The MQTT server settings
     * @param topic The topic to subscribe to
     * @param parent The parent QObject
     */
    explicit Subscriber(const QString &clientId, const MqttBase::ServerSettings &serverSettings, const QString &topic, QObject *parent = nullptr);

    /**
     * @brief Construct a new Subscriber object
     * @param serverSettings The MQTT server settings
     * @param topic The topic to subscribe to
     * @param parent The parent QObject
     */
    explicit Subscriber(const MqttBase::ServerSettings &serverSettings, const QString &topic, QObject *parent = nullptr);

    Q_SIGNALS:
        void messageReceived(const QByteArray &payload);

    private Q_SLOTS:
        void setupSubscription() const;
};
} // namespace LibGoal::Mqtt

#endif // SUBSCRIBER_H
