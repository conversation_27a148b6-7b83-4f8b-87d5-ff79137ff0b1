cmake_minimum_required(VERSION 3.19)

if(NOT WIN32 AND NOT APPLE)

find_package(Qt6 REQUIRED COMPONENTS Core)
find_package(PahoMqttCpp REQUIRED)

project(GSS_libgoal_mqtt VERSION 0.1 LANGUAGES CXX)
libGoalProject(GSS_libgoal_mqtt)

add_library(${PROJECT_NAME} STATIC
        src/mqttbase.h
        src/publisher.h
        src/subscriber.h

        src/mqttbase.cpp
        src/publisher.cpp
        src/subscriber.cpp
)

add_library(GSS::libgoal::mqtt ALIAS ${PROJECT_NAME})

if (NOT ${LIBGOAL_STANDALONE_BUILD})
    set_target_properties(${PROJECT_NAME} PROPERTIES EXCLUDE_FROM_ALL TRUE)
    message(STATUS "removed target ${PROJECT_NAME} from ALL")
endif()

target_include_directories(${PROJECT_NAME} PUBLIC
        ${CMAKE_CURRENT_LIST_DIR}/src
)

target_link_libraries(${PROJECT_NAME} PUBLIC
        Qt6::Core
        PahoMqttCpp::paho-mqttpp3
)

endif()
