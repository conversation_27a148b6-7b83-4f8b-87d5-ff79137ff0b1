cmake_minimum_required(VERSION 3.10.0)

project(LibGoal VERSION 0.1 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set_property(GLOBAL PROPERTY LIBGOAL_ROOT_PATH ${CMAKE_CURRENT_LIST_DIR})
set_property(GLOBAL PROPERTY LIBGOAL_PACKAGES)

set(LIBGOAL_PACKAGES_BLACKLIST "" CACHE STRING "Blacklist of packages to not include in the build") 
set_property(GLOBAL PROPERTY LIBGOAL_PACKAGES_BLACKLIST ${LIBGOAL_PACKAGES_BLACKLIST})

option(LIBGOAL_STANDALONE_BUILD "Build all libgoal libs - include them in the ALL target" OFF)

find_package(QT NAMES Qt6 Qt5)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

find_package(QT NAMES Qt6 Qt5)

function(findLibGoalPackage)
    set(<PERSON>IB<PERSON><PERSON><PERSON>_PACKAGE_NAME ${ARGV0})

    get_property(loadedPackages GLOBAL PROPERTY LIBGOAL_PACKAGES)
    get_property(libgoalPath GLOBAL PROPERTY LIBGOAL_ROOT_PATH)
    get_property(libgoalPackagesBlacklist GLOBAL PROPERTY LIBGOAL_PACKAGES_BLACKLIST)

    list(FIND loadedPackages ${LIBGOAL_PACKAGE_NAME} LIBGOAL_PACKAGE_INDEX)
    if(${LIBGOAL_PACKAGE_INDEX} GREATER -1)
        return()
    endif()

    list(FIND libgoalPackagesBlacklist ${LIBGOAL_PACKAGE_NAME} LIBGOAL_PACKAGE_BLACKLIST_INDEX)
    if(${LIBGOAL_PACKAGE_BLACKLIST_INDEX} GREATER -1)
        message(STATUS "Package ${LIBGOAL_PACKAGE_NAME} is blacklisted.")
        return()
    endif()

    string(REPLACE "GSS::" "" PACKAGE_NAME ${LIBGOAL_PACKAGE_NAME})
    string(REPLACE "::" "-" PACKAGE_FOLDER ${PACKAGE_NAME})
    add_subdirectory(${libgoalPath}/${PACKAGE_FOLDER} ${CMAKE_BINARY_DIR}/${PACKAGE_FOLDER})
    message(STATUS "Found " ${LIBGOAL_PACKAGE_NAME} in ${libgoalPath}/${PACKAGE_FOLDER})
endfunction()

function(libGoalProject)
    set(PROJNAME ${ARGV0})
    string(REPLACE "_" "::" PACKAGENAME ${PROJNAME})
    get_property(loadedPackages GLOBAL PROPERTY LIBGOAL_PACKAGES)
    list(APPEND loadedPackages ${PACKAGENAME})
    set_property(GLOBAL PROPERTY LIBGOAL_PACKAGES "${loadedPackages}") 
    message(STATUS "added package: " ${PACKAGENAME} " to packages list")
endfunction()

if(${LIBGOAL_STANDALONE_BUILD}) 
    enable_testing()

    findLibGoalPackage(GSS::libgoal::remote-var)
    findLibGoalPackage(GSS::libgoal::videoframe)
    findLibGoalPackage(GSS::libgoal::utils)
    findLibGoalPackage(GSS::libgoal::libav)
    findLibGoalPackage(GSS::libgoal::widgets)

    if(NOT APPLE)

        findLibGoalPackage(GSS::libgoal::smemory-video)
        findLibGoalPackage(GSS::libgoal::ndi)
        findLibGoalPackage(GSS::libgoal::ndi-embedded)
        findLibGoalPackage(GSS::libgoal::sdi)
        findLibGoalPackage(GSS::libgoal::devices)

        if (QT_VERSION_MAJOR EQUAL 6)
            findLibGoalPackage(GSS::libgoal::avrouter)
            findLibGoalPackage(GSS::libgoal::audio)
            findLibGoalPackage(GSS::libgoal::3drendering)
            findLibGoalPackage(GSS::libgoal::mqtt)
        endif()
    endif()
endif()
