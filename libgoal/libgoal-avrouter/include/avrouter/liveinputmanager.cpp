#include "liveinputmanager.h"

LiveInputManager::LiveInputManager(const std::function<void(const VideoFrame &)> &externalPushConsumer,
                                   const std::function<void (const QUuid &, const QByteArray &)> &externalAudioConsumer)
    : externalConsumer{externalPushConsumer}
    , externalAudioConsumer{externalAudioConsumer}
{
}

LiveInputManager::~LiveInputManager()
{
    reset();
}

bool LiveInputManager::setDeckLinkInput(const QUuid uuid, int hwDecklinkNum)
{
    for(_LiveInputProperties &prop : inputs) {
        if(prop.sdiCapture && prop.sdiCapture->getInputNumber() == hwDecklinkNum && prop.uuid == uuid) {
            return true; //and do nothing
        }
        //not possible to have multiple captures for single decklink...
        else if(prop.sdiCapture && prop.sdiCapture->getInputNumber() == hwDecklinkNum && prop.uuid != uuid) {
            //we need to change the UUID for this one, and also set it to the capture
            prop.uuid = uuid;
            prop.sdiCapture->setMediaUUID(uuid);
            //switches immediately to new UUID
            return true;
        }
        else if(prop.sdiCapture && prop.sdiCapture->getInputNumber() != hwDecklinkNum && prop.uuid == uuid) {
            //wtf? user probably wants to use the same UUID for a different input/type of input, OK let's do that
            removeInput(uuid);
            //and continue as nothing has happened...
        }
    }


    _LiveInputProperties prop;
    prop.uuid = uuid;
    prop.sdiCapture = new Capture();
    prop.sdiCapture->setDeinterlaceType(mDeckLinkDeinterlaceType);
    prop.sdiCapture->addVideoConsumer(externalConsumer);
    prop.sdiCapture->addAudioConsumer([this, uuid]([[maybe_unused]] int id, const QByteArray &buffer) { externalAudioConsumer(uuid, buffer); });
    prop.sdiCapture->setMediaUUID(uuid);
    if(prop.sdiCapture->init(hwDecklinkNum, -1, true, Capture::NormalizerRealTimeSync)) {
        prop.sdiCapture->start();
    }
    else {
        delete prop.sdiCapture;
        return false;
    }
    inputs.append(prop);
    return true;
}

bool LiveInputManager::setNdiInput(const QUuid uuid, const QString &source, const VideoFrame::Type inputType)
{
    for(_LiveInputProperties &prop : inputs) {
        if(prop.ndirec && prop.ndirec->getSourceName() == source && prop.uuid == uuid) {
            return true; //and do nothing
        }
        //Not needed, multiple NDI receivers can connect to the same source. However we want some consistency around here.
        // else if(prop.ndirec && prop.ndirec->getSourceName() == source && prop.uuid != uuid) {
        //     //we need to change the UUID for this one, and also set it to the capture
        //     prop.uuid = uuid;
        //     prop.ndirec->setMediaUUID(uuid);
        //     //switches immediately to new UUID
        //     return true;
        // }
        else if(prop.ndirec && prop.ndirec->getSourceName() != source && prop.uuid == uuid) {
            //wtf? user probably wants to use the same UUID for a different input, OK let's do that
            prop.ndirec->setSourceName(source);
            prop.ndirec->reinit();
            return true;
        }
        else if(prop.sdiCapture && prop.uuid == uuid) {
            removeInput(uuid);
            //and continue as if nothing happened
        }
    }


    _LiveInputProperties prop;
    prop.uuid = uuid;
    prop.ndirec = new NDIReceiver();
    prop.ndirec->addConsumer(externalConsumer);
    prop.ndirec->addAudioConsumer([uuid, this]([[maybe_unused]] int id, const QByteArray &buffer) { externalAudioConsumer(uuid, buffer); });
    prop.ndirec->setSourceName(source);
    prop.ndirec->setMediaUUID(uuid);
    prop.ndirec->setDesiredFrameType(inputType);
    prop.ndirec->setReceiveAudio(true);
    prop.ndirec->enableRecordingMode();
    prop.ndirec->setWantExplicitMetadata(true);
    prop.ndirec->start();

    inputs.append(prop);
    return true;
}

bool LiveInputManager::setShmInput(QUuid uuid, int mediaID)
{

    for(_LiveInputProperties &prop : inputs) {
        if(prop.uuid == uuid) {
            removeInput(uuid);
            //continue as if nothing
        }
        //multiple shm players cam attach to mediaID, however we want consistency. Plus, it would probably create some performance issues
        else if(prop.smemoryPlayer && prop.smemoryPlayer->getMediaID() == mediaID) {
            removeInput(uuid);
            //continue as if nothing
        }
    }

    _LiveInputProperties prop;
    prop.uuid = uuid;
    prop.smemoryPlayer = new SharedMemoryVideoPlayer(mediaID);
    prop.smemoryPlayer->setUuid(uuid);
    prop.smemoryPlayer->addConsumer(externalConsumer);
    prop.smemoryPlayer->addConsumerAudio([uuid, this]([[maybe_unused]] int id, const QByteArray &buffer) { externalAudioConsumer(uuid, buffer); });
    prop.smemoryPlayer->play();

    inputs.append(prop);
    return true;
}

void LiveInputManager::removeInput(QUuid uuid)
{
    int index = -1;
    int indexToRemove = -1;
    for(_LiveInputProperties &prop : inputs) {
        ++index;
        if(prop.uuid == uuid) {
            if(prop.sdiCapture) {
                prop.sdiCapture->stop();
                delete prop.sdiCapture;
                indexToRemove = index;
            }
            else if(prop.ndirec) {
                prop.ndirec->stop(true);
                prop.ndirec->deinit();
                delete prop.ndirec;
                indexToRemove = index;
            }
            else if(prop.smemoryPlayer) {
                prop.smemoryPlayer->stop();
                delete prop.smemoryPlayer;
                indexToRemove = index;
            }
        }
    }

    inputs.removeAt(indexToRemove);
}

void LiveInputManager::reset()
{
    for (const _LiveInputProperties &in : inputs) {
        delete in.ndirec;
        delete in.sdiCapture;
        delete in.smemoryPlayer;
    }
}

//void LiveInputManager::consumingMethod(const VideoFrame &f)
//{
//    QReadLocker l(&inputBuffersLock);
//    if(!inputBuffers.contains(f.mediaUUID())) {
//        return;
//    }
//    inputBuffers[f.mediaUUID()].addFrame(f);
//}

