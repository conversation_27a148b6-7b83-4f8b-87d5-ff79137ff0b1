#ifndef AVROUTER_H
#define AVROUTER_H

#include "avrouter/avr_helper.h"
#include "fpssync.h"
#include "liveinputmanager.h"
#include "liveoutputmanager.h"
#include "qthreadedobject.h"
#include "qvideoframehelper.h"
#include "timedqueue.h"
#include "videoframe.h"
#include "videooutputprocess.h"
#include <QReadWriteLock>
#include <QDataStream>
#include <QAudioFormat>
#include <QVideoSink>
#include <QBuffer>
#include <atomic>

struct SdiDevice {
    int sdiId = -1;
    int64_t persistentID = -1;
    QString name;
};

struct MappingSwitchPlan {
    bool addOrRemove{true}; //True = add this route, False = remove this route
    QUuid in;
    QUuid out;
    int layer{0}; //positive - going to the top, negative - going to the bottom
    bool directMapping{false}; //Direct mapping would override any processing (fps sync and format conversion) and tries to send VideoFrames directly
    //might not be used in the end
};

struct AudioSwitchPlan {
    bool addOrRemove{true}; //True = add this route, False = remove this route
    QUuid in;
    QUuid out;
};

struct AudioDelegate {
    std::function<void(const QUuid &, const QByteArray &)> externalAudioConsumer = [](const QUuid &, const QByteArray &) {};
    std::function<void(const std::list<AudioSwitchPlan> &)> externalAudioSwitchPlanCallback = [](const std::list<AudioSwitchPlan> &) {};
};

class AVRouter :  public QThreadedObject
{
    Q_OBJECT
public:
    AVRouter(const AudioDelegate &audioDelegate)
        : QThreadedObject()
        , inputmanager([this](const VideoFrame &f) { consumer(f); }, audioDelegate.externalAudioConsumer)
        , externalAudioSwitchPlanCallback(audioDelegate.externalAudioSwitchPlanCallback)
    {
        QThreadedObject::t_init();
    }
    ~AVRouter()
    {
        exitinglock.lockForWrite(); //we want to destroy it locked...
        QThreadedObject::t_destroy();
    }

    QVector<SdiDevice> getSdiDevices() {return sdiDevices;}

    void setAudioFormat(QAudioFormat fmt);

public slots:
    void reset();
    //OUTPUTS ---------------
    //outputs with needed output mode
    bool setOutputSDI(QUuid uuid, int hwInput, AVRHelper::OutputMode mode = AVRHelper::mode1080p50); //the format is UYVY anyways
    bool setOutputSDIPersistentID(QUuid uuid, int64_t persistentID, AVRHelper::OutputMode mode = AVRHelper::mode1080p50); //the format is UYVY anyways
    //outputs with dynamic output possibilities
    void registerSink(QUuid uuid, QVideoSink* sink);
    bool setOutputNDI(QUuid uuid, QString sourceName);
    bool setOutputSHM(QUuid uuid, int mediaID);
    void setOutputMID(QUuid uuid);
    void setOutputGeneric(QUuid uuid, std::function<void (VideoFrame &)> cons, bool reIdentificate = true);
    //changing arbitrary output mode for registered outputs that do not need it
    bool setOutputArbitraryMode(QUuid uuid, AVRHelper::OutputMode mode = AVRHelper::mode1080p50);
    bool setOutputArbitraryFormat(QUuid uuid, VideoFrame::Type type = VideoFrame::TypeUYVY);

    //control inputs
    bool setInputSdiNativeID(QUuid uuid, int id);
    bool setInputSdiPersistentID(QUuid uuid, int64_t persistentID);
    void setInputSdiDeinterlacingMode(Capture::DeinterlaceType type = Capture::YADIF_CPU_UYVY); //valid for all newly created SDI inputs
    bool setInputNDI(QUuid uuid, QString sourceID, VideoFrame::Type desiredType = VideoFrame::TypeUYVY); //here we basically do not need to use the FrameSync if we have our own. The question is - what about audio?
    bool setInputSHM(QUuid uuid, int mediaID); //SHM tries to send all frames (so it has its own framesync implicitly) but we can buffer it here also
    void setInputGeneric(QUuid uuid);
    void removeInput(QUuid uuid);


    //control in-out mapping + deal with layers and buffering/processing override

    void planMapping(SecsNanosecs time, std::list<MappingSwitchPlan> inToOuts);
    void planAudioMapping(SecsNanosecs time, std::list<AudioSwitchPlan> inToOuts);

    //Ingress
    //Step 1 - put it to the fucking framesync
    void consumer(const VideoFrame &f);
    void audioConsumer(const QUuid& uuid, const QPair<QByteArray, QAudioFormat> &buf);

protected slots:
    void priv_init() override;
    void priv_destroy() override;

    //Last step - just send to the right output according to current multimaps
    void simpleFastSwitcherReentrant(const VideoFrame &f);

    //for videooutputprocess
    VideoFrame getFrameFromInputBuffer(QUuid uuid, SecsNanosecs time);
    QPair<QByteArray, QAudioFormat> getAudioFromOutputBuffer(QUuid uuid, qsizetype bufferSize);

private:
    ElapsedTimeReference timeref;
    LiveInputManager inputmanager;
    LiveOutputManager outputmanager;

    std::function<void(const std::list<AudioSwitchPlan> &)> externalAudioSwitchPlanCallback;

    void scanDeckLinks();
    QVector<SdiDevice> sdiDevices;

    //input buffers
    QHash<QUuid, FPSSync*> inputBuffers;
    QReadWriteLock inputBuffersLock;

    //output Processors
    QHash<QUuid, VideoOutputProcess*> outputProcesses;
    QReadWriteLock outputProcessesLock;

    //generic inputs/output mapping
    void checkAndUpdateMapping();
    TimedQueue<MappingSwitchPlan> switchQueue;
    QMultiHash<QUuid,QUuid> videoswitchmap; //current switch map
    QHash<QUuid,QMap<int,QUuid>> outputLayers; //per output ID there is a map. In map key is original layer number, value is input UUID because of implicit sorting
    QReadWriteLock videoswitchmaplock;

    QHash<QUuid, std::function<void(const VideoFrame &)>> outUuidMap;
    QReadWriteLock outUuidMapLock;

    //sinks - QVideoSinks and their uuids
    QHash<QUuid, QVideoSink*> sinkmap;
    QReadWriteLock sinkmaplock;

    // audio
    QHash<QUuid, QPair<QByteArray*, QAudioFormat>> outputAudioBuffers;
    QReadWriteLock audioBuffersLock;
    const int k_maxAudioBufferSize = 100000; // 100 kB per audio output
    QAudioFormat m_audioFormat;
    TimedQueue<AudioSwitchPlan> audioSwitchQueue;

    //OK let's try to create this part in parallel
    QReadWriteLock exitinglock;
};

#endif // AVROUTER_H
