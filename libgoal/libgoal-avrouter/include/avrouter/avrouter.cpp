#include "avrouter.h"


void AVRouter::priv_init()
{
    //go through SDIs
    scanDeckLinks();
}

void AVRouter::priv_destroy()
{
    reset();
}

void AVRouter::simpleFastSwitcherReentrant(const VideoFrame &f)
{
    //fast way of route through locked maps
    //First check if we have current mapping
    checkAndUpdateMapping();

    //Gather all of the outputs for frame's uuid
    videoswitchmaplock.lockForRead();
    QList<QUuid> outlist = videoswitchmap.values(f.mediaUUID());
    videoswitchmaplock.unlock();

//    qInfo() << "Incoming frame" << f.mediaUUID() << "Outlist" << outlist;

    //fill sinks and consumers
    QList<QVideoSink*> outsinks;
    QList<std::function<void(const VideoFrame&)>> outconsumers;
    sinkmaplock.lockForRead();
    outUuidMapLock.lockForRead();
    for(const QUuid &id : outlist) {
        QVideoSink* s = sinkmap.value(id, nullptr);
        std::function<void(const VideoFrame&)> c  = outUuidMap.value(id, nullptr);
        if(s) {
            outsinks << s;
        }
        if(c) {
            outconsumers << c;
        }
    }
    sinkmaplock.unlock();
    outUuidMapLock.unlock();

    //send sinks
    if(!outsinks.isEmpty()) {
        QVideoFrame qf = QVideoFrameHelper::getQVideoFrameFromVideoFrame(f);
        for(QVideoSink* s : outsinks) {
            s->setVideoFrame(qf);
        }
    }

    //send direct outputs
    if(!outconsumers.isEmpty()) {
        for(const std::function<void(const VideoFrame&)> &c : outconsumers) {
            c(f);
        }
    }
}

VideoFrame AVRouter::getFrameFromInputBuffer(QUuid uuid, SecsNanosecs time)
{
    if(!exitinglock.tryLockForRead()) {
        return VideoFrame();
    }
    inputBuffersLock.lockForRead();
    FPSSync *fpssync = inputBuffers.value(uuid, nullptr);
    if(fpssync) {
        inputBuffersLock.unlock();
        exitinglock.unlock();
        return fpssync->pullFrame(time);
    }

    inputBuffersLock.unlock();
    exitinglock.unlock();
    return VideoFrame();
}

QPair<QByteArray, QAudioFormat> AVRouter::getAudioFromOutputBuffer(QUuid uuid, qsizetype bufferSize)
{
    if(!exitinglock.tryLockForRead() || bufferSize <= 0) {
        return {};
    }

    audioBuffersLock.lockForWrite();
    if(outputAudioBuffers.contains(uuid)) {
        auto [outputBuffer, fmt] = *outputAudioBuffers.find(uuid);

        QByteArray readBytes = outputBuffer->left(bufferSize);
        outputBuffer->remove(0, readBytes.size());

        audioBuffersLock.unlock();
        exitinglock.unlock();
        return {readBytes, fmt};
    }


    audioBuffersLock.unlock();
    exitinglock.unlock();
    return {};
}

void AVRouter::consumer(const VideoFrame &f)
{
    if(!exitinglock.tryLockForRead()) { //specialized lock for exiting, do not wait!
        return;
    }

    if(f.type() == VideoFrame::TypeAudioOnly) {
        //TBD - totally different way of doing things
        exitinglock.unlock();
        return;
    }

    inputBuffersLock.lockForRead();
    FPSSync *fpssync = inputBuffers.value(f.mediaUUID(), nullptr);
    if(fpssync) {
        fpssync->addFrame(f);
        inputBuffersLock.unlock();
        exitinglock.unlock();
    }
    inputBuffersLock.unlock();

    //and send out everywhere where it might be needed
    simpleFastSwitcherReentrant(f);
    exitinglock.unlock();
}

void AVRouter::audioConsumer(const QUuid& uuid, const QPair<QByteArray, QAudioFormat> &bufPair)
{
    if(!exitinglock.tryLockForRead()) { //specialized lock for exiting, do not wait!
        return;
    }

    audioBuffersLock.lockForWrite();
    if(outputAudioBuffers.contains(uuid)) {
        auto [outputBuffer, fmt] = *outputAudioBuffers.find(uuid);
        if (fmt != bufPair.second) {
            outputAudioBuffers.find(uuid)->second = bufPair.second;
        }

        // limit buffer size
        qint64 nextSize = outputBuffer->size() + bufPair.first.size();
        if (nextSize > k_maxAudioBufferSize) {
            outputBuffer->remove(0, nextSize - k_maxAudioBufferSize);
        }
        
        outputBuffer->append(bufPair.first);
    }

    audioBuffersLock.unlock();
    exitinglock.unlock();
}

void AVRouter::registerSink(QUuid uuid, QVideoSink *sink)
{
    sinkmaplock.lockForWrite();
    sinkmap.insert(uuid, sink);
    sinkmaplock.unlock();
}

bool AVRouter::setOutputNDI(QUuid uuid, QString sourceName)
{
    if(outputmanager.setNDIOutput(uuid, sourceName)) {
        outUuidMapLock.lockForWrite();
        outUuidMap.insert(uuid, outputmanager.getConsumerForUuid(uuid));
        outUuidMapLock.unlock();

        audioBuffersLock.lockForWrite();
        outputAudioBuffers.insert(uuid, {new QByteArray(), QAudioFormat()});
        audioBuffersLock.unlock();

        VideoOutputProcess *vop = new VideoOutputProcess();
        vop->setConversionMode(VideoOutputProcess::FormatOnlyConversion);
        vop->setOutputFormat(VideoFrame::TypeUYVY);
        vop->setOutputUUID(uuid);
        vop->setConsumer(outputmanager.getConsumerForUuid(uuid));
        vop->setPullFunction(std::bind(&AVRouter::getFrameFromInputBuffer, this, std::placeholders::_1, std::placeholders::_2));
        vop->setAudioFormat(m_audioFormat);
        vop->setAudioPullFunction(std::bind(&AVRouter::getAudioFromOutputBuffer, this, std::placeholders::_1, std::placeholders::_2));

        outputProcessesLock.lockForWrite();
        outputProcesses.insert(uuid, vop);
        outputLayers.insert(uuid, QMap<int, QUuid>());
        outputProcessesLock.unlock();
        vop->start();

        return true;
    }
    return false;
}

void AVRouter::reset()
{
    QWriteLocker l(&outputProcessesLock);
    QWriteLocker l2(&outUuidMapLock);
    QWriteLocker l3(&sinkmaplock);
    QWriteLocker l4(&inputBuffersLock);
    QWriteLocker l5(&videoswitchmaplock);
    QWriteLocker l6(&audioBuffersLock);

    inputmanager.reset();
    outputmanager.reset();

    for(VideoOutputProcess *vop : outputProcesses) {
        vop->stop();
        delete vop;
    }
    for(FPSSync *ib : inputBuffers) {
        delete ib;
    }
    for(auto [arr, _] : outputAudioBuffers.values()) {
        delete arr;
    }
    outputProcesses.clear();
    inputBuffers.clear();
    outUuidMap.clear();
    sinkmap.clear();
    switchQueue.clear();
    videoswitchmap.clear();
    outputLayers.clear();
    outputAudioBuffers.clear();
}

void AVRouter::setAudioFormat(QAudioFormat fmt)
{
    m_audioFormat = fmt;
}

bool AVRouter::setOutputSDI(QUuid uuid, int hwInput, AVRHelper::OutputMode mode)
{
    if(outputmanager.setSDIOutput(uuid, hwInput, mode.bmdMode)) {
        outUuidMapLock.lockForWrite();
        outUuidMap.insert(uuid, outputmanager.getConsumerForUuid(uuid));
        outUuidMapLock.unlock();

        audioBuffersLock.lockForWrite();
        outputAudioBuffers.insert(uuid, {new QByteArray(), QAudioFormat()});
        audioBuffersLock.unlock();

        VideoOutputProcess *vop = new VideoOutputProcess();
        vop->setConversionMode(VideoOutputProcess::FullConversion);
        vop->setOutputFPS(mode.fps);
        vop->setOutputFormat(VideoFrame::TypeUYVY);
        vop->setOutputResolution(QSize(mode.width,mode.height));
        vop->setOutputUUID(uuid);
        vop->setConsumer(outputmanager.getConsumerForUuid(uuid));
        vop->setPullFunction(std::bind(&AVRouter::getFrameFromInputBuffer, this, std::placeholders::_1, std::placeholders::_2));
        vop->setAudioFormat(m_audioFormat);
        vop->setAudioPullFunction(std::bind(&AVRouter::getAudioFromOutputBuffer, this, std::placeholders::_1, std::placeholders::_2));

        outputProcessesLock.lockForWrite();
        outputProcesses.insert(uuid, vop);
        outputLayers.insert(uuid, QMap<int, QUuid>());
        outputProcessesLock.unlock();
        vop->start();

        return true;
    }
    return false;
}

bool AVRouter::setOutputSDIPersistentID(QUuid uuid, int64_t persistentID, AVRHelper::OutputMode mode)
{
    for(SdiDevice &dev : sdiDevices) {
        if (dev.persistentID == persistentID) {
            return setOutputSDI(uuid, dev.sdiId, mode);
        }
    }
    return false;
}

bool AVRouter::setOutputSHM(QUuid uuid, int mediaID)
{
    if(outputmanager.setSHMOutput(uuid, mediaID)) {
            outUuidMapLock.lockForWrite();
            outUuidMap.insert(uuid, outputmanager.getConsumerForUuid(uuid));
            outUuidMapLock.unlock();
            return true;
        }
    return false;
}

void AVRouter::setOutputMID(QUuid uuid)
{
    QWriteLocker l(&inputBuffersLock);
    QWriteLocker l2(&outUuidMapLock);
    QWriteLocker l3(&outputProcessesLock);

    inputBuffers.insert(uuid, new FPSSync());

    audioBuffersLock.lockForWrite();
    outputAudioBuffers.insert(uuid, { new QByteArray(), QAudioFormat() });
    audioBuffersLock.unlock();

    outUuidMap.insert(uuid, [this, uuid](VideoFrame f){f.setMediaUUID(uuid); this->consumer(f);});
    VideoOutputProcess *vop = new VideoOutputProcess();
    vop->setConversionMode(VideoOutputProcess::NoConversion);
    vop->setOutputUUID(uuid);
    vop->setConsumer([this, uuid](VideoFrame f){f.setMediaUUID(uuid); this->consumer(f);}); //loop back to AVRouter
    vop->setPullFunction(std::bind(&AVRouter::getFrameFromInputBuffer, this, std::placeholders::_1, std::placeholders::_2));
    vop->setAudioFormat(m_audioFormat);
    vop->setAudioPullFunction(std::bind(&AVRouter::getAudioFromOutputBuffer, this, std::placeholders::_1, std::placeholders::_2));

    outputProcesses.insert(uuid, vop);
    vop->start();

    outputLayers.insert(uuid, QMap<int, QUuid>());
}

void AVRouter::setOutputGeneric(QUuid uuid, std::function<void (VideoFrame &)> cons, bool reIdentificate)
{
    QWriteLocker l1(&outUuidMapLock);
    QWriteLocker l2(&outputProcessesLock);

    audioBuffersLock.lockForWrite();
    outputAudioBuffers.insert(uuid, { new QByteArray(), QAudioFormat() });
    audioBuffersLock.unlock();

    outUuidMap.insert(uuid, [uuid, cons, reIdentificate](VideoFrame f){if(reIdentificate) f.setMediaUUID(uuid); cons(f);});
    VideoOutputProcess *vop = new VideoOutputProcess();
    vop->setConversionMode(VideoOutputProcess::NoConversion);
    vop->setOutputUUID(uuid);
    vop->setConsumer([uuid, cons, reIdentificate](VideoFrame f){if(reIdentificate) f.setMediaUUID(uuid); cons(f);});
    vop->setPullFunction(std::bind(&AVRouter::getFrameFromInputBuffer, this, std::placeholders::_1, std::placeholders::_2));
    vop->setAudioFormat(m_audioFormat);
    vop->setAudioPullFunction(std::bind(&AVRouter::getAudioFromOutputBuffer, this, std::placeholders::_1, std::placeholders::_2));

    outputProcesses.insert(uuid, vop);
    vop->start();

    outputLayers.insert(uuid, QMap<int, QUuid>());
}

bool AVRouter::setOutputArbitraryMode(QUuid uuid, AVRHelper::OutputMode mode)
{
    QReadLocker l(&outputProcessesLock);
    VideoOutputProcess *vop = outputProcesses.value(uuid, nullptr);
    if(!vop) {
        return false;
    }
    vop->setConversionMode(VideoOutputProcess::FullConversion);
    vop->setOutputResolution(QSize(mode.width,mode.height));
    vop->setOutputFPS(mode.fps);
    return true;
}

bool AVRouter::setOutputArbitraryFormat(QUuid uuid, VideoFrame::Type type)
{
    QReadLocker l(&outputProcessesLock);
    VideoOutputProcess *vop = outputProcesses.value(uuid, nullptr);
    if(!vop) {
        return false;
    }
    vop->setOutputFormat(type);
    return true;
}

bool AVRouter::setInputSdiNativeID(QUuid uuid, int id)
{
    if(inputmanager.setDeckLinkInput(uuid, id)) {
        QWriteLocker l(&inputBuffersLock);
        inputBuffers.insert(uuid, new FPSSync());
        return true;
    }
    return false;
}

bool AVRouter::setInputSdiPersistentID(QUuid uuid, int64_t persistentID)
{
    for(SdiDevice &dev : sdiDevices) {
        if (dev.persistentID == persistentID) {
            return setInputSdiNativeID(uuid, dev.sdiId);
        }
    }
    return false;
}

void AVRouter::setInputSdiDeinterlacingMode(Capture::DeinterlaceType type)
{
    inputmanager.setDeckLinkDeinterlacingType(type);
}

bool AVRouter::setInputNDI(QUuid uuid, QString sourceID, VideoFrame::Type desiredType)
{
    if(inputmanager.setNdiInput(uuid, sourceID, desiredType)) {
        QWriteLocker l(&inputBuffersLock);
        inputBuffers.insert(uuid, new FPSSync());
        return true;
    }
    return false;
}

bool AVRouter::setInputSHM(QUuid uuid, int mediaID)
{
    if(inputmanager.setShmInput(uuid, mediaID)) {
        QWriteLocker l(&inputBuffersLock);
        inputBuffers.insert(uuid, new FPSSync());
        return true;
    }
    return false;
}

void AVRouter::setInputGeneric(QUuid uuid)
{
    QWriteLocker l(&inputBuffersLock);
    inputBuffers.insert(uuid, new FPSSync());
}

void AVRouter::removeInput(QUuid uuid)
{
    inputmanager.removeInput(uuid);
    QWriteLocker l(&inputBuffersLock);
    delete inputBuffers.value(uuid, nullptr);
    inputBuffers.remove(uuid);
}


void AVRouter::planMapping(SecsNanosecs time, std::list<MappingSwitchPlan> inToOuts)
{
    //no need to check the inputs and outputs - the switcher does not care
    switchQueue.push(time, inToOuts);
}

void AVRouter::planAudioMapping(SecsNanosecs time, std::list<AudioSwitchPlan> inToOuts)
{
    audioSwitchQueue.push(time, inToOuts);
}

void AVRouter::scanDeckLinks()
{
    IDeckLinkIterator *decklinkIterator = CreateDeckLinkIteratorInstance();
    if(decklinkIterator == NULL)
    {
        qWarning() << "Could not create Decklink iterator. Check if you have installed Blackmagic Decklink drivers";
    }

    IDeckLink *deckLink{nullptr};
    int deckLinkHWnumber = 0;
    while(decklinkIterator->Next(&deckLink) == S_OK)
    {
        SdiDevice dev;
        dev.sdiId = deckLinkHWnumber;
        char *modelname;
        deckLink->GetModelName((const char**)&modelname);
        dev.name = QString("%1 (#%2)").arg(QString(modelname)).arg(deckLinkHWnumber);
        free(modelname);
        //get what profile it knows
        IDeckLinkProfileAttributes *deckLinkAttributes = nullptr;
        if(deckLink->QueryInterface(IID_IDeckLinkProfileAttributes, (void**)&deckLinkAttributes) == S_OK){
            int64_t persistentID = 0;
            if(deckLinkAttributes->GetInt(BMDDeckLinkPersistentID, &persistentID) == S_OK){
                dev.persistentID = persistentID;
            }
        }
//        qInfo() << "Detected Decklink" << dev.name << dev.sdiId << dev.persistentID;
        sdiDevices.append(dev);
        //we will keep with that for now
        ++deckLinkHWnumber;
    }
}

void AVRouter::checkAndUpdateMapping()
{
    if(switchQueue.isEmpty() && audioSwitchQueue.isEmpty()) {
        return;
    }
    timeref.update();
    while (timeref.lastSsinceEpochNS() >= audioSwitchQueue.nextItemTime() && !audioSwitchQueue.isEmpty()) {
        std::list<AudioSwitchPlan> plans = audioSwitchQueue.popNextItem();
        externalAudioSwitchPlanCallback(plans);
    }
    
    while(timeref.lastSsinceEpochNS() >= switchQueue.nextItemTime() && !switchQueue.isEmpty()) {
        QWriteLocker l(&videoswitchmaplock);
        QReadLocker l2(&outputProcessesLock);
        std::list<MappingSwitchPlan> plans = switchQueue.popNextItem();

        for(const MappingSwitchPlan &plan : plans) {
            VideoOutputProcess *vop = outputProcesses.value(plan.out, nullptr);

            if(plan.directMapping) { //direct mapping
                //dealing with direct mapping on this output means that outputProcessor must be stopped!
                if(vop) {
                    vop->stop();
                }
                qDebug() << "switching" << plan.addOrRemove << plan.in << plan.out;
                if(plan.addOrRemove) {
                    //prevent multiple inputs to one output!!!
                    QList<QUuid> ins = videoswitchmap.keys(plan.out);
                    for(const QUuid &in : ins) {
                        videoswitchmap.remove(in, plan.out);
                    }
                    videoswitchmap.insert(plan.in, plan.out);
                }
                else {
                    videoswitchmap.remove(plan.in, plan.out);
                }
            }
            else { //processed mapping
                //vop should be started and direct mappings removed
                if(vop) {
                    vop->start();

                    for(const QUuid &ins : videoswitchmap.keys(plan.out)) {
                        videoswitchmap.remove(ins, plan.out);
                    }
                }
                else {
                    qWarning() << "[AVRouter] VOP does not exist, can not use processed mapping";
                    continue;
                }

                if(!outputLayers.contains(plan.out)) {
                    continue;
                }

                qDebug() << "[AVRouter] Switching processed" << plan.addOrRemove << plan.in << plan.out;

                if(plan.addOrRemove) {
                    outputLayers[plan.out].insert(plan.layer, plan.in);
                }
                else {
                    if(outputLayers[plan.out].values().contains(plan.in))
                        outputLayers[plan.out].remove(outputLayers[plan.out].key(plan.in));
                }

                vop->setLayers(outputLayers[plan.out].values());
            }

        }
    }
}
