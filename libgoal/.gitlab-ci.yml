variables:
  GIT_SUBMODULE_STRATEGY: recursive
  DOCKER_REPOSITORY: gitlab.goalsport.software:5050/goalsport.software/libgoal
  XKEYS_VERSION: "1.0.0"
  XKEYS_URL: $CI_API_V4_URL/projects/$CI_PROJECT_ID/packages/generic/xkeys/$XKEYS_VERSION
  NDI_VERSION: "5.6.1"
  NDI_URL: $CI_API_V4_URL/projects/$CI_PROJECT_ID/packages/generic/ndi-embedded-installer/$NDI_VERSION
  GPUJPEG_VERSION: "0.14.1"
  GPUJPEG_URL: $CI_API_V4_URL/projects/$CI_PROJECT_ID/packages/generic/gpujpeg/$GPUJPEG_VERSION


stages:
  - docker_build
  - build
  - regression_tests

### RULES
.release_rules:
  rules:
  - if: $CI_COMMIT_TAG != null
    when: on_success
  - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
    when: on_success
  - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    when: manual
    allow_failure: true
  - if: $CI_PIPELINE_SOURCE == 'web'
    when: manual
    allow_failure: true

.debug_rules:
  rules:
  - if: $CI_COMMIT_TAG != null
    when: on_success
  - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
    when: on_success
  - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    when: on_success
  - if: $CI_PIPELINE_SOURCE == 'web'
    when: manual
    allow_failure: true

### DOCKER BUILD
.docker_build_template:
  stage: docker_build
  tags: ["linux", "docker", "build"]
  image: docker:latest
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - cd $CI_PROJECT_DIR/$DIR
    - apk add --no-cache curl
    - 'curl -O --header "JOB-TOKEN: $CI_JOB_TOKEN" $XKEYS_URL/xkeys-master.zip'
    - 'curl -O --header "JOB-TOKEN: $CI_JOB_TOKEN" $NDI_URL/ndi-embedded-installer.zip'
    - 'curl -O --header "JOB-TOKEN: $CI_JOB_TOKEN" $GPUJPEG_URL/GPUJPEG-master.zip'
  script:
    - if [ "$CI_COMMIT_REF_SLUG" != "$CI_DEFAULT_BRANCH" ]; then export BRANCH_TAG=-testing; fi
    - cd $CI_PROJECT_DIR/$DIR
    - export BASE_TAG=$OS-$ARCH-$EXTENSION
    - export SIMPLE_TAG=$BASE_TAG$BRANCH_TAG
    - export COMPLETE_TAG=$BASE_TAG-$CI_JOB_ID$BRANCH_TAG
    - DOCKER_BUILDKIT=1 docker build --build-arg smb_credentials="$SMB_CREDENTIALS" --cache-from $DOCKER_REPOSITORY/$NAME:$BASE_TAG --cache-from $DOCKER_REPOSITORY/$NAME:$BASE_TAG-testing --pull --build-arg BUILDKIT_INLINE_CACHE=1 -t $DOCKER_REPOSITORY/$NAME:$SIMPLE_TAG -t $DOCKER_REPOSITORY/$NAME:$COMPLETE_TAG -f $DOCKER_FILE .
    - docker push $DOCKER_REPOSITORY/$NAME:$SIMPLE_TAG
    - docker push $DOCKER_REPOSITORY/$NAME:$COMPLETE_TAG

docker-u22-qt5:
  extends:
    - .docker_build_template
  variables:
    NAME: libgoal
    DOCKER_FILE: Dockerfile-qt5
    DIR: Docker/Ubuntu22
    OS: "jammy"
    ARCH: amd64
    EXTENSION: qt5
  only:
    refs:
      - master
      - merge_requests
    changes:
      - Docker/Ubuntu22/Dockerfile-qt5

docker-u22-qt6:
  extends:
    - .docker_build_template
  variables:
    NAME: libgoal
    DOCKER_FILE: Dockerfile-qt6
    DIR: Docker/Ubuntu22
    OS: "jammy"
    ARCH: amd64
    EXTENSION: qt6
  only:
    refs:
      - master
      - merge_requests
    changes:
      - Docker/Ubuntu22/Dockerfile-qt6

docker-u24-qt6:
  extends:
    - .docker_build_template
  variables:
    NAME: libgoal
    DOCKER_FILE: Dockerfile-qt6
    DIR: Docker/Ubuntu24
    OS: "noble"
    ARCH: amd64
    EXTENSION: qt6
  only:
    refs:
      - master
      - merge_requests
    changes:
      - Docker/Ubuntu24/Dockerfile-qt6

### BUILD
.build_template:
  stage: build
  tags: ["linux", "docker", "build"]
  script:
    - mkdir -p build && cd build
    - cmake -DCMAKE_BUILD_TYPE=$BUILD_TYPE -DLIBGOAL_STANDALONE_BUILD=ON -DLIBGOAL_PACKAGES_BLACKLIST=${LIBGOAL_PACKAGES_BLACKLIST} ..
    - make -j 4 all
    - ctest --output-on-failure --output-junit unittests.xml || true
    - rm -r libgoal-libav/tests/testVideos

.win_build_template:
  stage: build
  tags: ["windows"]
  script:
    - otherFiles\deps\win64includes.bat
    - cmake -S $env:CI_PROJECT_DIR -B $env:CI_PROJECT_DIR/build -DCMAKE_BUILD_TYPE=$env:BUILD_TYPE -DLIBGOAL_STANDALONE_BUILD=ON
    - cmake --build $env:CI_PROJECT_DIR/build --parallel --config=$env:BUILD_TYPE
    - ctest --output-on-failure --output-junit unittests.xml

.macos_build_template:
  stage: build
  tags: ["macos"]
  script:
    - cmake -G Xcode -S $CI_PROJECT_DIR -B $CI_PROJECT_DIR/build -DCMAKE_BUILD_TYPE=$BUILD_TYPE -DLIBGOAL_STANDALONE_BUILD=ON
    - xcodebuild -project build/LibGoal.xcodeproj -alltargets
    - ctest --output-on-failure --output-junit unittests.xml || true

#Ubuntu 22 - Qt5
U22-qt5-release:
  extends:
    - .build_template
    - .release_rules
  image: ${DOCKER_REPOSITORY}/libgoal:jammy-amd64-qt5
  variables:
    BUILD_TYPE: release

U22-qt5-debug:
  extends:
    - .build_template
    - .debug_rules
  image: ${DOCKER_REPOSITORY}/libgoal:jammy-amd64-qt5-testing
  variables:
    BUILD_TYPE: debug
  artifacts:
    when: always
    expire_in: 3 days
    paths:
      - build/unittests.xml
    reports:
      junit: build/unittests.xml

#Ubuntu 22 - Qt6
U22-qt6-release:
  extends:
    - .build_template
    - .release_rules
  image: ${DOCKER_REPOSITORY}/libgoal:jammy-amd64-qt6
  variables:
    BUILD_TYPE: release
    LIBGOAL_PACKAGES_BLACKLIST: "GSS::libgoal::audio"

U22-qt6-debug:
  extends:
    - .build_template
    - .debug_rules
  image: ${DOCKER_REPOSITORY}/libgoal:jammy-amd64-qt6-testing
  variables:
    BUILD_TYPE: debug
    LIBGOAL_PACKAGES_BLACKLIST: "GSS::libgoal::audio"
  artifacts:
    when: always
    expire_in: 3 days
    paths:
      - build/unittests.xml
    reports:
      junit: build/unittests.xml

#Ubuntu 24 - Qt6
U24-qt6-release:
  extends:
    - .build_template
    - .release_rules
  image: ${DOCKER_REPOSITORY}/libgoal:noble-amd64-qt6
  variables:
    BUILD_TYPE: release

U24-qt6-debug:
  extends:
    - .build_template
    - .debug_rules
  image: ${DOCKER_REPOSITORY}/libgoal:noble-amd64-qt6-testing
  variables:
    BUILD_TYPE: debug
  artifacts:
    when: always
    expire_in: 3 days
    paths:
      - build/unittests.xml
    reports:
      junit: build/unittests.xml

#Windows Qt5
Windows-qt5-release:
  extends:
    - .win_build_template
    - .release_rules
  variables:
    BUILD_TYPE: release
    CMAKE_PREFIX_PATH: C:\Qt\5.15.2\msvc2019_64

Windows-qt5-debug:
  extends:
    - .win_build_template
    - .debug_rules
  variables:
    BUILD_TYPE: debug
    CMAKE_PREFIX_PATH: C:\Qt\5.15.2\msvc2019_64
  artifacts:
    when: always
    expire_in: 3 days
    paths:
      - build/unittests.xml
    reports:
      junit: build/unittests.xml

#Windows Qt6
Windows-qt6-release:
  extends:
    - .win_build_template
    - .release_rules
  variables:
    BUILD_TYPE: release
    CMAKE_PREFIX_PATH: C:\Qt\6.8.2\msvc2022_64

Windows-qt6-debug:
  extends:
    - .win_build_template
    - .debug_rules
  variables:
    BUILD_TYPE: debug
    CMAKE_PREFIX_PATH: C:\Qt\6.8.2\msvc2022_64
  artifacts:
    when: always
    expire_in: 3 days
    paths:
      - build/unittests.xml
    reports:
      junit: build/unittests.xml

#macOS Qt6
MacOS-qt6-release:
  extends:
    - .macos_build_template
    - .release_rules
  variables:
    BUILD_TYPE: release

MacOS-qt6-debug:
  extends:
    - .macos_build_template
    - .debug_rules
  variables:
    BUILD_TYPE: debug
  artifacts:
    when: always
    expire_in: 3 days
    paths:
      - build/unittests.xml
    reports:
      junit: build/unittests.xml

