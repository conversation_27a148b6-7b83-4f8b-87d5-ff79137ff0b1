FROM ubuntu:22.04

LABEL maintainer="mkra<PERSON><PERSON><EMAIL>"

RUN echo "root:gss" | chpasswd

# create user gss
RUN useradd --create-home --shell=/bin/bash gss
RUN echo "gss:gss" | chpasswd
RUN usermod -aG sudo gss
RUN echo "gss ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers

# set file system permissions
RUN chown -R gss:gss /home/<USER>

# Install sudo (force-confold  - https://askubuntu.com/questions/104899/make-apt-get-or-aptitude-run-with-y-but-not-prompt-for-replacement-of-configu)
RUN apt-get update && \
  apt-get install -o Dpkg::Options::="--force-confold" -y sudo &&\
  apt-get clean && \
  rm -rf /var/lib/apt/lists/*

####### Install packages needed for docker build
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
    wget ssh curl gnupg locales git bash-completion && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

## Install tools for build
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
    gcc g++ make cmake && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

####### Install deps
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
    qtmultimedia5-dev libqt5xmlpatterns5-dev libqt5websockets5-dev libhidapi-dev smbclient unzip libusb-1.0-0-dev nvidia-cuda-toolkit libgcrypt20-dev libglm-dev libcurl4-gnutls-dev autoconf automake build-essential libtool ffmpeg libegl1-mesa-dev libass-dev libvlc-dev libvlccore-dev libturbojpeg0-dev libjack-jackd2-dev libavcodec-dev libavformat-dev libavdevice-dev libavfilter-dev libavutil-dev libswscale-dev libswresample-dev libgtest-dev libssl-dev libmsgpack-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# External deps
ARG build_threads=4
ARG smb_credentials
USER gss 
WORKDIR /home/<USER>
# use sudo (GS_NDI_Installer/install uses it)

#install xkeys
COPY xkeys-master.zip xkeys-master.zip
RUN unzip xkeys-master.zip && cd xkeys-master/ && \
    ./configure && make -j $build_threads && sudo make install && cd .. && \
    rm xkeys-master.zip && rm -rf xkeys-master/

# Install NDI
COPY ndi-embedded-installer.zip ndi-embedded-installer.zip
RUN unzip ndi-embedded-installer.zip && GS_NDI_Installer/install && \
    rm ndi-embedded-installer.zip && rm -r GS_NDI_Installer/

# Install GPUJPEG
COPY GPUJPEG-master.zip GPUJPEG-master.zip
RUN unzip GPUJPEG-master.zip && cd GPUJPEG-master/ && ./autogen.sh && make -j $build_threads && sudo make install || true && \
    cd .. && rm GPUJPEG-master.zip && rm -r GPUJPEG-master/

USER root

####### configure environment
RUN sed -i '/en_US.UTF-8/s/^# //g' /etc/locale.gen && \
    locale-gen
ENV LANG=en_US.UTF-8
ENV LC_ALL=en_US.UTF-8
ENV LANGUAGE=en_US:en

ENV HOME=/home/<USER>
USER gss
WORKDIR /home/<USER>
CMD /bin/bash
