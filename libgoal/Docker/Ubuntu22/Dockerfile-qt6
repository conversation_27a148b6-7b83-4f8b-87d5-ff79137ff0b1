FROM ubuntu:22.04

LABEL maintainer="mkra<PERSON><PERSON><EMAIL>"

RUN echo "root:gss" | chpasswd

# create user gss
RUN useradd --create-home --shell=/bin/bash gss
RUN echo "gss:gss" | chpasswd
RUN usermod -aG sudo gss
RUN echo "gss ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers

# set file system permissions
RUN chown -R gss:gss /home/<USER>

# Install sudo (force-confold  - https://askubuntu.com/questions/104899/make-apt-get-or-aptitude-run-with-y-but-not-prompt-for-replacement-of-configu)
RUN apt-get update && \
  apt-get install -o Dpkg::Options::="--force-confold" -y sudo &&\
  apt-get clean && \
  rm -rf /var/lib/apt/lists/*

####### Install packages needed for docker build
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
    wget ssh curl gnupg locales git bash-completion && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

## Install tools for build
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
    gcc g++ make cmake && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

####### Install deps
## Qt
ARG QT=6.8.2
# set the correct Qt location (it could be rewritten by PCL)
ENV QT_SELECT=opt-qt615
ENV QTDIR=/opt/Qt/${QT}/gcc_64/lib/cmake/Qt6
ENV LD_LIBRARY_PATH=/opt/Qt/${QT}/gcc_64/lib/:$LD_LIBRARY_PATH
ENV PATH=/opt/Qt/${QT}/gcc_64/bin:$PATH
ENV PKG_CONFIG_PATH=/opt/Qt/${QT}/gcc_64/lib/pkgconfig:$PKG_CONFIG_PATH
ENV QML_IMPORT_PATH /opt/Qt/${QT}/gcc_64/qml/
ENV QML2_IMPORT_PATH /opt/Qt/${QT}/gcc_64/qml/

RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
    python3-pip  && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

RUN mkdir /opt/Qt/ && chown -R gss:gss /opt/Qt/
USER gss
RUN cd ~ && pip install aqtinstall && ~/.local/bin/aqt install-qt linux desktop ${QT} -m debug_info qt5compat qtmultimedia qtwebsockets qtwebengine -O /opt/Qt/
USER root
# .local/bin/aqt list-qt linux desktop --modules ${QT} gcc_64 

RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
    libxcb-xkb-dev libxkbcommon-x11-dev libhidapi-dev smbclient unzip libusb-1.0-0-dev nvidia-cuda-toolkit libgcrypt20-dev libglm-dev libcurl4-gnutls-dev autoconf automake build-essential libtool ffmpeg libegl1-mesa-dev libass-dev libvlc-dev libvlccore-dev libturbojpeg0-dev libjack-jackd2-dev libavcodec-dev libavformat-dev libavdevice-dev libavfilter-dev libavutil-dev libswscale-dev libswresample-dev libgtest-dev libssl-dev libmsgpack-dev \
    libeigen3-dev libopencv-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# External deps
ARG build_threads=4
USER gss 
WORKDIR /home/<USER>

#install xkeys
COPY xkeys-master.zip xkeys-master.zip
RUN unzip xkeys-master.zip && cd xkeys-master/ && \
    ./configure && make -j $build_threads && sudo make install && cd .. && \
    rm xkeys-master.zip && rm -rf xkeys-master/

# Install NDI
COPY ndi-embedded-installer.zip ndi-embedded-installer.zip
RUN unzip ndi-embedded-installer.zip && GS_NDI_Installer/install && \
    rm ndi-embedded-installer.zip && rm -r GS_NDI_Installer/

# Install GPUJPEG
COPY GPUJPEG-master.zip GPUJPEG-master.zip
RUN unzip GPUJPEG-master.zip && cd GPUJPEG-master/ && ./autogen.sh && make -j $build_threads && sudo make install || true && \
    cd .. && rm GPUJPEG-master.zip && rm -r GPUJPEG-master/

USER root

# Download & extract Filament - last release before GLIBC_2.38
RUN curl -L https://github.com/google/filament/releases/download/v1.56.5/filament-v1.56.5-linux.tgz > ~/filament.tgz && \
    tar -xf ~/filament.tgz -C /opt/ && \
    rm ~/filament.tgz

# Install MQTT dependencies
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
    libpaho-mqtt-dev libpaho-mqttpp-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

####### configure environment
RUN sed -i '/en_US.UTF-8/s/^# //g' /etc/locale.gen && \
    locale-gen
ENV LANG=en_US.UTF-8
ENV LC_ALL=en_US.UTF-8
ENV LANGUAGE=en_US:en

ENV HOME=/home/<USER>
USER gss
WORKDIR /home/<USER>
CMD /bin/bash
