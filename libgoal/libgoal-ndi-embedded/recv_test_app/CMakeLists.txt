cmake_minimum_required(VERSION 3.10)

project(recv_test_app_embedded VERSION 0.1 LANGUAGES CXX)

findLibGoalPackage(GSS::libgoal::ndi-embedded)

add_executable(${PROJECT_NAME}
    mainwindow.h mainwindow.cpp mainwindow.ui
    main.cpp
)

target_include_directories(${PROJECT_NAME} PUBLIC
    ${CMAKE_CURRENT_LIST_DIR}
)

target_link_libraries(${PROJECT_NAME} PUBLIC
    GSS::libgoal::ndi-embedded
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Multimedia
)
