#include "bmdsdihelper.h"
#include <QDebug>

BmdSdiHelper::BmdSdiHelper() {}

QList<BmdSdiHelper::DeviceInfo> BmdSdiHelper::listDevices()
{
    IDeckLinkIterator *decklinkIterator = CreateDeckLinkIteratorInstance();
    QList<DeviceInfo> devices;

    if(decklinkIterator){
        IDeckLink *decklink = nullptr;
        int cardID = -1;
        while(decklinkIterator->Next(&decklink) == S_OK && decklink){
            ++cardID;
            DeviceInfo devInfo;
            devInfo.cardID = cardID;

            //get model name as identified by the driver
            char *decklinkName = nullptr;
            if(decklink->GetModelName((const char **)(&decklinkName)) == S_OK){
                devInfo.deviceName = QString(decklinkName);
                free(decklinkName);
            }

            //read DeckLink attribs - active + persistent ID
            IDeckLinkProfileAttributes *deckLinkAttributes = nullptr;
            if(decklink->QueryInterface(IID_IDeckLinkProfileAttributes, (void**)&deckLinkAttributes) == S_OK){
                int64_t duplexMode = 0;
                if(deckLinkAttributes->GetInt(BMDDeckLinkDuplex, &duplexMode) == S_OK && duplexMode == bmdDuplexInactive){
                    devInfo.deviceActive = false;
                }

                int64_t persistentID = 0;
                if(deckLinkAttributes->GetInt(BMDDeckLinkPersistentID, &persistentID) == S_OK){
                    devInfo.persistentID = persistentID;
                }

                deckLinkAttributes->Release();
                deckLinkAttributes = nullptr;
            }

            devices.append(devInfo);

            decklink->Release();
        }
        decklinkIterator->Release();
    }
    return devices;
}

bool BmdSdiHelper::isOuptutModeSupported(int cardID, BMDDisplayMode displayMode)
{
    bool isSupported = false;
    IDeckLink *decklink = queryDeckLink(cardID);
    if(decklink){
        IDeckLinkOutput *deckLinkOutput = nullptr;
        if(decklink->QueryInterface(IID_IDeckLinkOutput, (void**)&deckLinkOutput) == S_OK && deckLinkOutput){
            bool displayModeSupported = false;
            if(deckLinkOutput->DoesSupportVideoMode(bmdVideoConnectionUnspecified, displayMode, bmdFormatUnspecified, bmdVideoOutputFlagDefault, nullptr, &displayModeSupported) == S_OK){
                isSupported = displayModeSupported;
            }

            deckLinkOutput->Release();
        }
        decklink->Release();
    }

    return isSupported;
}

bool BmdSdiHelper::isInputModeSupported(int cardID, BMDDisplayMode displayMode)
{
    bool isSupported = false;
    IDeckLink *decklink = queryDeckLink(cardID);
    if(decklink){
        IDeckLinkInput *deckLinkInput = nullptr;
        if(decklink->QueryInterface(IID_IDeckLinkOutput, (void**)&deckLinkInput) == S_OK && deckLinkInput){
            bool displayModeSupported = false;
            if(deckLinkInput->DoesSupportVideoMode(bmdVideoConnectionUnspecified, displayMode, bmdFormatUnspecified, bmdVideoOutputFlagDefault, &displayModeSupported) == S_OK){
                isSupported = displayModeSupported;
            }

            deckLinkInput->Release();
        }
        decklink->Release();
    }

    return isSupported;
}

QList<BMDDisplayMode> BmdSdiHelper::supportedCardOutputModes(int cardID)
{
    QList<BMDDisplayMode> dispModes;
    IDeckLink *decklink = queryDeckLink(cardID);
    if(decklink){
        IDeckLinkOutput *deckLinkOutput = nullptr;
        if(decklink->QueryInterface(IID_IDeckLinkOutput, (void**)&deckLinkOutput) == S_OK && deckLinkOutput){
            IDeckLinkDisplayModeIterator *it = nullptr;
            if(deckLinkOutput->GetDisplayModeIterator(&it) == S_OK && it){
                IDeckLinkDisplayMode *dm = nullptr;
                while(it->Next(&dm) == S_OK && dm){
                    dispModes.append(dm->GetDisplayMode());
                    const char *name;
                    dm->GetName(&name);
                    qDebug() << name;
                    dm->Release();
                    dm = nullptr;
                }
                it->Release();
            }

            deckLinkOutput->Release();
        }
        decklink->Release();
    }
    return dispModes;
}

QList<BMDDisplayMode> BmdSdiHelper::modesSD()
{
    return QList<BMDDisplayMode>() << bmdModePAL << bmdModePALp << bmdModeNTSC << bmdModeNTSCp << bmdModeNTSC2398;
}

QList<BMDDisplayMode> BmdSdiHelper::modesHD()
{
    return QList<BMDDisplayMode>() << bmdModeHD720p50 << bmdModeHD720p5994 << bmdModeHD720p60;
}

QList<BMDDisplayMode> BmdSdiHelper::modesFullHD()
{
    return QList<BMDDisplayMode>() << bmdModeHD1080p2398 << bmdModeHD1080p24 << bmdModeHD1080p25 << bmdModeHD1080p2997 << bmdModeHD1080p30
                                   << bmdModeHD1080p4795 << bmdModeHD1080p48 << bmdModeHD1080p50 << bmdModeHD1080p5994 << bmdModeHD1080p6000
                                   << bmdModeHD1080p9590 << bmdModeHD1080p96 << bmdModeHD1080p100 << bmdModeHD1080p11988 << bmdModeHD1080p120
                                   << bmdModeHD1080i50 << bmdModeHD1080i5994 << bmdModeHD1080i6000;
}

QList<BMDDisplayMode> BmdSdiHelper::modes2K()
{
    return QList<BMDDisplayMode>() << bmdMode2k2398 << bmdMode2k24<< bmdMode2k25
                                   << bmdMode2kDCI2398 << bmdMode2kDCI24 << bmdMode2kDCI25 << bmdMode2kDCI2997 << bmdMode2kDCI30
                                   << bmdMode2kDCI4795 << bmdMode2kDCI48 << bmdMode2kDCI50 << bmdMode2kDCI5994 << bmdMode2kDCI60
                                   << bmdMode2kDCI9590 << bmdMode2kDCI96 << bmdMode2kDCI100 << bmdMode2kDCI11988 << bmdMode2kDCI120;
}

QList<BMDDisplayMode> BmdSdiHelper::modes4K()
{
    return QList<BMDDisplayMode>() << bmdMode4K2160p2398 << bmdMode4K2160p24 << bmdMode4K2160p25 << bmdMode4K2160p2997 << bmdMode4K2160p30
                                   << bmdMode4K2160p4795 << bmdMode4K2160p48 << bmdMode4K2160p50 << bmdMode4K2160p5994 << bmdMode4K2160p60
                                   << bmdMode4K2160p9590 << bmdMode4K2160p96 << bmdMode4K2160p100 << bmdMode4K2160p11988 << bmdMode4K2160p120
                                   << bmdMode4kDCI2398 << bmdMode4kDCI24 << bmdMode4kDCI25 << bmdMode4kDCI2997 << bmdMode4kDCI30
                                   << bmdMode4kDCI4795 << bmdMode4kDCI48 << bmdMode4kDCI50 << bmdMode4kDCI5994 << bmdMode4kDCI60
                                   << bmdMode4kDCI9590 << bmdMode4kDCI96 << bmdMode4kDCI100 << bmdMode4kDCI11988 << bmdMode4kDCI120;
}

QList<BMDDisplayMode> BmdSdiHelper::modes8K()
{
    return QList<BMDDisplayMode>() << bmdMode8K4320p2398 << bmdMode8K4320p24 << bmdMode8K4320p25 << bmdMode8K4320p2997 << bmdMode8K4320p30
                                   << bmdMode8K4320p4795 << bmdMode8K4320p48 << bmdMode8K4320p50 << bmdMode8K4320p5994 << bmdMode8K4320p60
                                   << bmdMode8kDCI2398 << bmdMode8kDCI24 << bmdMode8kDCI25 << bmdMode8kDCI2997 << bmdMode8kDCI30
                                   << bmdMode8kDCI4795 << bmdMode8kDCI48 << bmdMode8kDCI50 << bmdMode8kDCI5994 << bmdMode8kDCI60;
}

QStringList BmdSdiHelper::modesToStrings(const QList<BMDDisplayMode> &list)
{
    QStringList strList;
    for(auto mode : list){
        strList.append(modeNames.value(mode));
    }
    return strList;
}

QSize BmdSdiHelper::getModeResolution(BMDDisplayMode dispMode)
{
    QSize resolution;

    switch (dispMode) {
    case bmdModeHD720p50:
    case bmdModeHD720p60:
    case bmdModeHD720p5994:
        resolution = QSize(1280, 720);
        break;
    case bmdModeHD1080i50:
    case bmdModeHD1080i5994:
    case bmdModeHD1080i6000:
    case bmdModeHD1080p24:
    case bmdModeHD1080p25:
    case bmdModeHD1080p30:
    case bmdModeHD1080p50:
    case bmdModeHD1080p2398:
    case bmdModeHD1080p2997:
    case bmdModeHD1080p5994:
    case bmdModeHD1080p6000:
    case bmdModeHD1080p48:
    case bmdModeHD1080p96:
    case bmdModeHD1080p100:
    case bmdModeHD1080p120:
    case bmdModeHD1080p4795:
    case bmdModeHD1080p9590:
    case bmdModeHD1080p11988:
        resolution = QSize(1920, 1080);
        break;
    case bmdMode2k2398:
    case bmdMode2k24:
    case bmdMode2k25:
        resolution = QSize(2048, 1556);
        break;
    case bmdMode2kDCI2398 :
    case bmdMode2kDCI24   :
    case bmdMode2kDCI25   :
    case bmdMode2kDCI2997 :
    case bmdMode2kDCI30   :
    case bmdMode2kDCI4795 :
    case bmdMode2kDCI48   :
    case bmdMode2kDCI50   :
    case bmdMode2kDCI5994 :
    case bmdMode2kDCI60   :
    case bmdMode2kDCI9590 :
    case bmdMode2kDCI96   :
    case bmdMode2kDCI100  :
    case bmdMode2kDCI11988:
    case bmdMode2kDCI120  :
        resolution = QSize(2048, 1080);
        break;
    case bmdMode4K2160p24:
    case bmdMode4K2160p25:
    case bmdMode4K2160p30:
    case bmdMode4K2160p50:
    case bmdMode4K2160p60:
    case bmdMode4K2160p2398:
    case bmdMode4K2160p2997:
    case bmdMode4K2160p5994:
    case bmdMode4K2160p48:
    case bmdMode4K2160p96:
    case bmdMode4K2160p100:
    case bmdMode4K2160p120:
    case bmdMode4K2160p4795:
    case bmdMode4K2160p9590:
    case bmdMode4K2160p11988:
        resolution = QSize(3840, 2160);
        break;
    case bmdMode4kDCI2398 :
    case bmdMode4kDCI24   :
    case bmdMode4kDCI25   :
    case bmdMode4kDCI2997 :
    case bmdMode4kDCI30   :
    case bmdMode4kDCI4795 :
    case bmdMode4kDCI48   :
    case bmdMode4kDCI50   :
    case bmdMode4kDCI5994 :
    case bmdMode4kDCI60   :
    case bmdMode4kDCI9590 :
    case bmdMode4kDCI96   :
    case bmdMode4kDCI100  :
    case bmdMode4kDCI11988:
    case bmdMode4kDCI120  :
        resolution = QSize(4096, 2160);
        break;
    case bmdMode8K4320p2398:
    case bmdMode8K4320p24:
    case bmdMode8K4320p25:
    case bmdMode8K4320p2997:
    case bmdMode8K4320p30:
    case bmdMode8K4320p4795:
    case bmdMode8K4320p48:
    case bmdMode8K4320p50:
    case bmdMode8K4320p5994:
    case bmdMode8K4320p60:
        resolution = QSize(7680, 4320);
        break;
    case bmdMode8kDCI2398:
    case bmdMode8kDCI24:
    case bmdMode8kDCI25:
    case bmdMode8kDCI2997:
    case bmdMode8kDCI30:
    case bmdMode8kDCI4795:
    case bmdMode8kDCI48:
    case bmdMode8kDCI50:
    case bmdMode8kDCI5994:
    case bmdMode8kDCI60:
        resolution = QSize(8192, 4320);
        break;
    case bmdModePAL:
    case bmdModePALp:
        resolution = QSize(720, 576);
        break;
    case bmdModeNTSC:
    case bmdModeNTSCp:
    case bmdModeNTSC2398:
        resolution = QSize(720, 486);
        break;
    default:
        break;
    }
    return resolution;
}

double BmdSdiHelper::getModeNativeFPS(BMDDisplayMode dispMode)
{
    double fps = 0.0;

    switch (dispMode) {
    case bmdModeHD1080p48:
    case bmdMode2kDCI48:
    case bmdMode4K2160p48:
    case bmdMode4kDCI48:
    case bmdMode8K4320p48:
    case bmdMode8kDCI48:
        fps = 48.0;
        break;
    case bmdModeHD1080p96:
    case bmdMode2kDCI96:
    case bmdMode4K2160p96:
    case bmdMode4kDCI96:
        fps = 96.0;
        break;
    case bmdModeHD1080p100:
    case bmdMode2kDCI100:
    case bmdMode4K2160p100:
    case bmdMode4kDCI100:
        fps = 100.0;
        break;
    case bmdModeHD1080p120:
    case bmdMode2kDCI120:
    case bmdMode4K2160p120:
    case bmdMode4kDCI120:
        fps = 120;
        break;
    case bmdModeHD1080p4795:
    case bmdMode2kDCI4795:
    case bmdMode4K2160p4795:
    case bmdMode4kDCI4795:
    case bmdMode8K4320p4795:
    case bmdMode8kDCI4795:
        fps = 48 / 1.001;
        break;
    case bmdModeHD1080p9590:
    case bmdMode2kDCI9590:
    case bmdMode4K2160p9590:
    case bmdMode4kDCI9590:
        fps = 96 / 1.001;
        break;
    case bmdModeHD1080p11988:
    case bmdMode2kDCI11988:
    case bmdMode4K2160p11988:
    case bmdMode4kDCI11988:
        fps = 120 / 1.001;
        break;
    case bmdModeHD720p50:
    case bmdModeHD1080p50:
    case bmdModeHD1080i50:
    case bmdMode2kDCI50:
    case bmdMode4K2160p50:
    case bmdMode4kDCI50:
    case bmdMode8K4320p50:
    case bmdMode8kDCI50:
    case bmdModePALp:
        fps = 50.0;
        break;
    case bmdModeHD720p60:
    case bmdModeHD1080p6000:
    case bmdModeHD1080i6000:
    case bmdMode2kDCI60:
    case bmdMode4K2160p60:
    case bmdMode4kDCI60:
    case bmdMode8K4320p60:
    case bmdMode8kDCI60:
        fps = 60.0;
        break;
    case bmdModeHD720p5994:
    case bmdModeHD1080p5994:
    case bmdModeHD1080i5994:
    case bmdMode2kDCI5994:
    case bmdMode4K2160p5994:
    case bmdMode4kDCI5994:
    case bmdMode8K4320p5994:
    case bmdMode8kDCI5994:
    case bmdModeNTSCp:
        fps = 60 / 1.001;
        break;
    case bmdModeHD1080p24:
    case bmdMode2k24:
    case bmdMode2kDCI24:
    case bmdMode4K2160p24:
    case bmdMode4kDCI24:
    case bmdMode8K4320p24:
    case bmdMode8kDCI24:
        fps = 24.0;
        break;
    case bmdModeHD1080p25:
    case bmdMode2k25:
    case bmdMode2kDCI25:
    case bmdMode4K2160p25:
    case bmdMode4kDCI25:
    case bmdMode8K4320p25:
    case bmdMode8kDCI25:
    case bmdModePAL:
        fps = 25.0;
        break;
    case bmdModeHD1080p30:
    case bmdMode2kDCI30:
    case bmdMode4K2160p30:
    case bmdMode4kDCI30:
    case bmdMode8K4320p30:
    case bmdMode8kDCI30:
        fps = 30.0;
        break;
    case bmdModeHD1080p2398:
    case bmdMode2k2398:
    case bmdMode2kDCI2398:
    case bmdMode4K2160p2398:
    case bmdMode4kDCI2398:
    case bmdMode8K4320p2398:
    case bmdMode8kDCI2398:
        fps = 24 / 1.001;
        break;
    case bmdModeHD1080p2997:
    case bmdMode2kDCI2997:
    case bmdMode4K2160p2997:
    case bmdMode4kDCI2997:
    case bmdMode8K4320p2997:
    case bmdMode8kDCI2997:
    case bmdModeNTSC:
    case bmdModeNTSC2398:  //BMD doc says that this is playing as 29.97 with 3:2 pulldown
        fps = 30 / 1.001;
        break;
    default:
        break;
    }

    return fps;
}

BmdSdiHelper::DisplayModeInfo BmdSdiHelper::getModeInfo(BMDDisplayMode dispMode)
{
    DisplayModeInfo info;
    info.bmdMode = dispMode;
    info.resolution = getModeResolution(dispMode);
    info.frameRate = getModeNativeFPS(dispMode);
    info.name = getModeName(dispMode);
    return info;
}

IDeckLink *BmdSdiHelper::queryDeckLink(int cardID)
{
    IDeckLink *decklink = nullptr;
    if(cardID >= 0){
        IDeckLinkIterator *decklinkIterator = CreateDeckLinkIteratorInstance();
        if(decklinkIterator){
            //skip previous DeckLinks
            for(int i = 0; i < cardID; ++i){
                if(decklinkIterator->Next(&decklink) == S_OK && decklink){
                    decklink->Release();
                    decklink = nullptr;
                }
            }

            //next deckLink device is the requested one
            if(decklinkIterator->Next(&decklink) != S_OK){
                decklink = nullptr;
            }

            decklinkIterator->Release();
        }
    }
    return decklink;
}
