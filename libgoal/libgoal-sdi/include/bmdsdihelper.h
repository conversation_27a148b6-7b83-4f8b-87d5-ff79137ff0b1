#ifndef BMDSDIHELPER_H
#define BMDSDIHELPER_H

#include "DeckLinkAPI/DeckLinkAPI.h"
#include <QString>
#include <QList>
#include <QSize>
#include <QMap>

class BmdSdiHelper
{
public:
    struct DeviceInfo {
        int cardID = -1;  //card index as detected by the driver. Indexed from 0
        int64_t persistentID = 0;  //unique HW number
        bool deviceActive = true;  //Card models that supports half/full duplex mode switching can have sub-devices in inactive state (when device is in full duplex mode)
        QString deviceName;
    };

    struct DisplayModeInfo {
        BMDDisplayMode bmdMode = bmdModeUnknown;
        QSize resolution;
        double frameRate;
        QString name;
    };

    BmdSdiHelper();

    static QList<DeviceInfo> listDevices();
    static bool isOuptutModeSupported(int cardID, BMDDisplayMode displayMode);
    static bool isInputModeSupported(int cardID, BMDDisplayMode displayMode);
    /**
     * @brief Returns modes supported on specific card model regardless
     * of the current card profile (half/full duplex).
     * Use isOuptutModeSupported method to check supported mode in current profile.
     * @param cardID
     * @return
     */
    static QList<BMDDisplayMode> supportedCardOutputModes(int cardID);
    static QList<BMDDisplayMode> modesAll() { return modeNames.keys(); }  //unsorted
    static QList<BMDDisplayMode> modesSD();
    static QList<BMDDisplayMode> modesHD();
    static QList<BMDDisplayMode> modesFullHD();
    static QList<BMDDisplayMode> modes2K();
    static QList<BMDDisplayMode> modes4K();
    static QList<BMDDisplayMode> modes8K();
    static QStringList modesToStrings(const QList<BMDDisplayMode> &list);

    static QSize getModeResolution(BMDDisplayMode dispMode);
    static double getModeNativeFPS(BMDDisplayMode dispMode);
    static QString getModeName(BMDDisplayMode dispMode) { return modeNames.value(dispMode); }
    static BMDDisplayMode getModeFromString(const QString &modeStr) { return modeNames.key(modeStr, bmdModeUnknown); }  //linear time search
    static DisplayModeInfo getModeInfo(BMDDisplayMode dispMode);

private:
    /**
     * @brief queryDeckLink Get pointer do existing BMD DeckLink HW card.
     * Be sure to call IDeckLink::Release after use, BMD objects has internal reference counting.
     * @param cardID
     * @return Pointer to IDeckLink object or null when no such cardID found in the system.
     */
    static IDeckLink *queryDeckLink(int cardID);

    //all modes we want to be supported in SW
    inline static const QMap<BMDDisplayMode, QString> modeNames {
        {bmdModeUnknown,    "Unknown"},
        {bmdModePAL,        "PAL"},
        {bmdModePALp,       "PALp"},
        {bmdModeNTSC,       "NTSC"},
        {bmdModeNTSCp,      "NTSCp"},
        {bmdModeNTSC2398,   "NTSC 23.98"},
        {bmdModeHD720p50,   "HD 720p 50"},
        {bmdModeHD720p5994, "HD 720p 59.94"},
        {bmdModeHD720p60,   "HD 720p 60"},
        {bmdModeHD1080p2398,"HD 1080p 23.98"},
        {bmdModeHD1080p24,  "HD 1080p 24"},
        {bmdModeHD1080p25,  "HD 1080p 25"},
        {bmdModeHD1080p2997,"HD 1080p 29.97"},
        {bmdModeHD1080p30,  "HD 1080p 30"},
        {bmdModeHD1080p4795,"HD 1080p 47.95"},
        {bmdModeHD1080p48,  "HD 1080p 48"},
        {bmdModeHD1080p50,  "HD 1080p 50"},
        {bmdModeHD1080p5994,"HD 1080p 59.94"},
        {bmdModeHD1080p6000,"HD 1080p 60"},
        {bmdModeHD1080p9590,"HD 1080p 95.90"},
        {bmdModeHD1080p96,  "HD 1080p 96"},
        {bmdModeHD1080p100, "HD 1080p 100"},
        {bmdModeHD1080p11988,"HD 1080p 119.88"},
        {bmdModeHD1080p120, "HD 1080p 120"},
        {bmdModeHD1080i50,  "HD 1080i 50"},
        {bmdModeHD1080i5994,"HD 1080i 59.94"},
        {bmdModeHD1080i6000,"HD 1080i 60"},
        {bmdMode2k2398,     "2K 1556p 23.98"},
        {bmdMode2k24,       "2K 1556p 24"},
        {bmdMode2k25,       "2K 1556p 25"},
        {bmdMode2kDCI2398,  "2K DCI 1080p 23.98"},
        {bmdMode2kDCI24,    "2K DCI 1080p 24"},
        {bmdMode2kDCI25,    "2K DCI 1080p 25"},
        {bmdMode2kDCI2997,  "2K DCI 1080p 29.97"},
        {bmdMode2kDCI30,    "2K DCI 1080p 30"},
        {bmdMode2kDCI4795,  "2K DCI 1080p 47.95"},
        {bmdMode2kDCI48,    "2K DCI 1080p 48"},
        {bmdMode2kDCI50,    "2K DCI 1080p 50"},
        {bmdMode2kDCI5994,  "2K DCI 1080p 59.94"},
        {bmdMode2kDCI60,    "2K DCI 1080p 60"},
        {bmdMode2kDCI9590,  "2K DCI 1080p 95.90"},
        {bmdMode2kDCI96,    "2K DCI 1080p 96"},
        {bmdMode2kDCI100,   "2K DCI 1080p 100"},
        {bmdMode2kDCI11988, "2K DCI 1080p 119.88"},
        {bmdMode2kDCI120,   "2K DCI 1080p 120"},
        {bmdMode4K2160p2398,"4K 2160p 23.98"},
        {bmdMode4K2160p24,  "4K 2160p 24"},
        {bmdMode4K2160p25,  "4K 2160p 25"},
        {bmdMode4K2160p2997,"4K 2160p 29.97"},
        {bmdMode4K2160p30,  "4K 2160p 30"},
        {bmdMode4K2160p4795,"4K 2160p 47.95"},
        {bmdMode4K2160p48,  "4K 2160p 48"},
        {bmdMode4K2160p50,  "4K 2160p 50"},
        {bmdMode4K2160p5994,"4K 2160p 59.94"},
        {bmdMode4K2160p60,  "4K 2160p 60"},
        {bmdMode4K2160p9590,"4K 2160p 95.90"},
        {bmdMode4K2160p96,  "4K 2160p 96"},
        {bmdMode4K2160p100, "4K 2160p 100"},
        {bmdMode4K2160p11988,"4K 2160p 119.88"},
        {bmdMode4K2160p120, "4K 2160p 120"},
        {bmdMode4kDCI2398,  "4K DCI 2160p 23.98"},
        {bmdMode4kDCI24,    "4K DCI 2160p 24"},
        {bmdMode4kDCI25,    "4K DCI 2160p 25"},
        {bmdMode4kDCI2997,  "4K DCI 2160p 29.97"},
        {bmdMode4kDCI30,    "4K DCI 2160p 30"},
        {bmdMode4kDCI4795,  "4K DCI 2160p 47.95"},
        {bmdMode4kDCI48,    "4K DCI 2160p 48"},
        {bmdMode4kDCI50,    "4K DCI 2160p 50"},
        {bmdMode4kDCI5994,  "4K DCI 2160p 59.94"},
        {bmdMode4kDCI60,    "4K DCI 2160p 60"},
        {bmdMode4kDCI9590,  "4K DCI 2160p 95.90"},
        {bmdMode4kDCI96,    "4K DCI 2160p 96"},
        {bmdMode4kDCI100,   "4K DCI 2160p 100"},
        {bmdMode4kDCI11988, "4K DCI 2160p 119.88"},
        {bmdMode4kDCI120,   "4K DCI 2160p 120"},
        {bmdMode8K4320p2398,"8K 4320p 23.98"},
        {bmdMode8K4320p24,  "8K 4320p 24"},
        {bmdMode8K4320p25,  "8K 4320p 25"},
        {bmdMode8K4320p2997,"8K 4320p 29.97"},
        {bmdMode8K4320p30,  "8K 4320p 30"},
        {bmdMode8K4320p4795,"8K 4320p 47.95"},
        {bmdMode8K4320p48,  "8K 4320p 48"},
        {bmdMode8K4320p50,  "8K 4320p 50"},
        {bmdMode8K4320p5994,"8K 4320p 59.94"},
        {bmdMode8K4320p60,  "8K 4320p 60"},
        {bmdMode8kDCI2398,  "8K DCI 4320p 23.98"},
        {bmdMode8kDCI24,    "8K DCI 4320p 24"},
        {bmdMode8kDCI25,    "8K DCI 4320p 25"},
        {bmdMode8kDCI2997,  "8K DCI 4320p 29.97"},
        {bmdMode8kDCI30,    "8K DCI 4320p 30"},
        {bmdMode8kDCI4795,  "8K DCI 4320p 47.95"},
        {bmdMode8kDCI48,    "8K DCI 4320p 48"},
        {bmdMode8kDCI50,    "8K DCI 4320p 50"},
        {bmdMode8kDCI5994,  "8K DCI 4320p 59.94"},
        {bmdMode8kDCI60,    "8K DCI 4320p 60"},
    };
};

#endif // BMDSDIHELPER_H
