cmake_minimum_required(VERSION 3.14)

if(NOT WIN32)

project(GSS_libgoal_3drendering VERSION 0.1 LANGUAGES CXX)
libGoalProject(GSS_libgoal_3drendering)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(FILAMENT_DIR /opt/filament)

find_package(Qt6 REQUIRED COMPONENTS Core Widgets Concurrent)
find_package(PkgConfig REQUIRED)
find_package(Eigen3 REQUIRED)

pkg_check_modules(OpenCV REQUIRED IMPORTED_TARGET opencv4)

# Run the material conversion script
set(CONVERSION_SCRIPT ${PROJECT_SOURCE_DIR}/materials/convert.sh)
set(MATC_BINARY ${FILAMENT_DIR}/bin/matc)
execute_process(
    COMMAND ${CONVERSION_SCRIPT} ${FILAMENT_DIR}
    WORKING_DIRECTORY ${PROJECT_SOURCE_DIR}
    RESULT_VARIABLE conversion_result
    # OUTPUT_QUIET
)
if(NOT conversion_result EQUAL 0)
    message(FATAL_ERROR "Material conversion failed!")
endif()

add_library(${PROJECT_NAME} STATIC
    include/mathUtils.h
    include/renderingPrimitives.h
    include/canvasWidget.h
    include/renderingWorker.h
    include/renderComponent.h
    include/inputmanager.h
    include/fileLoader.h

    include/renderingPrimitives.cpp
    include/renderingWorker.cpp
    include/renderComponent.cpp
    include/inputmanager.cpp

    materials/singleColor.filamat
    materials/transparentTexture.filamat
)

add_library(GSS::libgoal::3drendering ALIAS ${PROJECT_NAME})

if (NOT ${LIBGOAL_STANDALONE_BUILD})
    set_target_properties(${PROJECT_NAME} PROPERTIES EXCLUDE_FROM_ALL TRUE)
    message(STATUS "removed target ${PROJECT_NAME} from ALL")
endif()

target_include_directories(${PROJECT_NAME} PUBLIC
    ${CMAKE_CURRENT_LIST_DIR}
    ${CMAKE_CURRENT_LIST_DIR}/include
    ${FILAMENT_DIR}/include
)

target_link_directories(${PROJECT_NAME} PUBLIC
    ${FILAMENT_DIR}/lib/x86_64
)

target_link_libraries(${PROJECT_NAME} PUBLIC
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Concurrent

    PkgConfig::OpenCV
    Eigen3::Eigen

    # Filament libraries (the proper order is unclear, but seems to work like this):
    ######################
    gltfio
    gltfio_core
    filamat
    filament
    backend
    bluegl
    bluevk
    dracodec
    filabridge
    filaflat
    filameshio
    geometry
    ibl
    ibl-lite
    ktxreader
    shaders
    smol-v
    utils
    vkshaders
    basis_transcoder
    zstd
    ######################

    stb
    c++
)

endif() # NOT WIN32
