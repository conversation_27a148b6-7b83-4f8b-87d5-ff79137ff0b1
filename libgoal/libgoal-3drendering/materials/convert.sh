#!/bin/bash

# Convert all .mat files in the current directory to .filamat, 
# using the first argument as the path to the filament folder

FILAMENT_PATH=$1
SCRIPT_DIRECTORY=$(dirname "$0")

# check if the filament path is provided
if [ -z "$FILAMENT_PATH" ]; then
    echo "Usage: $0 <filament_path>"
    exit 1
fi

MATC_BINARY="${FILAMENT_PATH}/bin/matc"
# check if the matc binary exists
if [ ! -f "$MATC_BINARY" ]; then
    echo "Error: $MATC_BINARY not found"
    exit 1
fi

for MAT_FILE in "${SCRIPT_DIRECTORY}"/*.mat; do
    if [ ! -f "$MAT_FILE" ]; then
        echo "No .mat files found in $SCRIPT_DIRECTORY."
        continue
    fi

    FILENAME_BASE=$(basename "$MAT_FILE" .mat)
    OUT_FILE="${SCRIPT_DIRECTORY}/${FILENAME_BASE}.filamat"

    # check if the .filamat file already exists
    if [ -f "$OUT_FILE" ]; then
        echo "Removing existing $OUT_FILE"
        rm "$OUT_FILE"
    fi

    echo "Converting $MAT_FILE to $OUT_FILE"
    "$MATC_BINARY" -f header -a vulkan -a opengl -o "$OUT_FILE" "$MAT_FILE" 
done