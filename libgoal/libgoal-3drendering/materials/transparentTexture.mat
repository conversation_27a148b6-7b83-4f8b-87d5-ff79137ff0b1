material {
    name : TransparentTexture,    
    parameters : [
        {
            type : sampler2d,
            name : texture
        }
    ],
    requires : [
        uv0
    ],
    shadingModel : unlit,
    blending : transparent,
}

fragment {
    void material(inout MaterialInputs material) {
        prepareMaterial(material);
        
        // convert UV to NDC
        vec2 uv = getUV0();

        if (uv.x < 0.0 || uv.x > 1.0 || uv.y < 0.0 || uv.y > 1.0) {
            discard;
        }
        
        vec4 color = texture(materialParams_texture, uv);
        material.baseColor = color;
    }
}