#include "renderComponent.h"
#include <QDebug>

namespace fil = filament;

RenderComponent::RenderComponent(QObject* parent, const std::shared_ptr<fil::Engine>& engine, QSize size)
    : QObject(parent)
    , m_engine(engine)
    , m_cameraEntity(utils::EntityManager::get().create())
    , m_camera(engine->createCamera(m_cameraEntity))
    , m_view(engine->createView())
    , m_scene(engine->createScene())
    , m_size(size) {
    m_view->setScene(m_scene);
    m_view->setCamera(m_camera);
    m_view->setBlendMode(fil::View::BlendMode::TRANSLUCENT);

    m_view->setPostProcessingEnabled(true);
    m_view->setMultiSampleAntiAliasingOptions({.enabled = true, .sampleCount = 4});
    fil::TemporalAntiAliasingOptions TAA;
    TAA.enabled = true;
    m_view->setTemporalAntiAliasingOptions(TAA);

    // set initial viewport size and camera projection
    resize(m_size);
}

RenderComponent::~RenderComponent() {
    qDebug() << "Destroying RenderComponent";

    Q_ASSERT(m_assets.empty()); // should be cleared by calling deleteAssets from parent

    for (utils::Entity entity : m_entities) {
        m_engine->destroy(entity);
    }

    m_engine->destroy(m_sunEntity);

    m_engine->destroy(m_scene);
    m_engine->destroy(m_view);
    m_engine->destroyCameraComponent(m_cameraEntity);
}

void RenderComponent::deleteAssets(FileLoader* fileLoader) {
    for (filament::gltfio::FilamentAsset* asset : m_assets) {
        m_scene->removeEntities(asset->getEntities(), asset->getEntityCount());
        fileLoader->destroyAsset(asset);
    }
    m_assets.clear();
}

void RenderComponent::resize(QSize newSize) {
    m_size = newSize + m_sizeMargin;

    m_view->setViewport({0, 0, static_cast<uint32_t>(m_size.width()), static_cast<uint32_t>(m_size.height())}); // TODO: Try to crop by shifting?
    setFov(m_cameraFov);
}

void RenderComponent::addEntity(const RenderingPrimitives::MeshList& meshes) {
    if (!m_engine) {
        qCritical("Engine not initialized");
        return;
    }

    for (RenderingPrimitives::ObjectMesh* mesh : meshes) {
        std::vector<fil::math::float3>* positions = &mesh->vertices;
        std::vector<uint16_t>* indices = &mesh->indices;

        auto* vertexBuffer = fil::VertexBuffer::Builder()
                                 .vertexCount(positions->size())
                                 .bufferCount(1)
                                 .attribute(fil::VertexAttribute::POSITION, 0, fil::VertexBuffer::AttributeType::FLOAT3, 0, 12)
                                 .build(*m_engine);
        vertexBuffer->setBufferAt(*m_engine, 0,
                                  fil::VertexBuffer::BufferDescriptor(positions->data(), positions->size() * 3 * sizeof(float), nullptr));
        auto* indexBuffer = fil::IndexBuffer::Builder()
                                .indexCount(indices->size())
                                .bufferType(fil::IndexBuffer::IndexType::USHORT)
                                .build(*m_engine);
        indexBuffer->setBuffer(*m_engine, fil::IndexBuffer::BufferDescriptor(indices->data(), indices->size() * sizeof(uint16_t), nullptr));
        auto* lineMaterial = fil::Material::Builder()
                                 .package(SINGLE_COLOR_PACKAGE, sizeof(SINGLE_COLOR_PACKAGE))
                                 .build(*m_engine);
        auto* lineMaterialInstance = lineMaterial->createInstance();
        lineMaterialInstance->setParameter("albedo", mesh->rgba);
        auto entity = utils::EntityManager::get().create();
        fil::RenderableManager::Builder(1)
            .material(0, lineMaterialInstance)
            .geometry(0, mesh->primitiveType, vertexBuffer,
                      indexBuffer, 0, indices->size())
            .culling(false)
            .receiveShadows(false)
            .castShadows(false)
            .build(*m_engine, entity);
        m_entities.push_back(entity);

        auto& tcm = m_engine->getTransformManager();
        float rotationRadians = mesh->rotationDegrees * M_PI / 180.0;
        auto rotation = fil::math::mat4f::rotation(rotationRadians, mesh->rotationAxis);
        auto translationMat = fil::math::mat4f::translation(mesh->translation);
        tcm.setTransform(tcm.getInstance(entity), translationMat * rotation);
        m_scene->addEntity(entity);
    }
}

void RenderComponent::addEntity(utils::Entity entity) {
    m_scene->addEntity(entity);
}

void RenderComponent::addGlb(filament::gltfio::FilamentAsset* asset) {
    if (asset) {
        m_scene->addEntities(asset->getEntities(), asset->getEntityCount());
        m_assets.push_back(asset);
    }
}

void RenderComponent::setSkybox(QColor color) {
    auto* skybox = fil::Skybox::Builder().color({color.redF(), color.greenF(), color.blueF(), color.alphaF()}).build(*m_engine);
    m_scene->setSkybox(skybox);
}

fil::View* RenderComponent::getView() {
    return m_view;
}

fil::Camera* RenderComponent::getCamera() {
    return m_camera;
}

void RenderComponent::setFov(float fov) {
    m_cameraFov = fov;
    float aspectRatio = static_cast<float>(m_size.width()) / m_size.height();
    double verticalFovRad = 2 * std::atan(std::tan(((fov * M_PI) / 180) / 2) / (aspectRatio));
    double verticalFovDeg = (verticalFovRad * 180) / M_PI; // convert to degrees
    m_camera->setProjection(verticalFovDeg, aspectRatio, m_cameraNear, m_cameraFar);
}

void RenderComponent::setMsaa(bool enabled, int sampleCount) {
    uint8_t sampleCountClamped = std::clamp(sampleCount, 1, 16);
    if (sampleCount != sampleCountClamped) {
        qWarning() << "Sample count" << sampleCount << "clamped to" << sampleCountClamped;
    }
    m_view->setMultiSampleAntiAliasingOptions({.enabled = enabled, .sampleCount = sampleCountClamped});
}

void RenderComponent::addSun(int intensity, filament::math::float3 color) {
    m_engine->destroy(m_sunEntity);
    m_sunEntity = utils::EntityManager::get().create();
    fil::LightManager::Builder(fil::LightManager::Type::SUN)
        .color(fil::Color::toLinear<fil::ACCURATE>(color))
        .intensity(intensity)
        .sunAngularRadius(1.9f)
        .castShadows(false)
        .build(*m_engine, m_sunEntity);
    m_scene->addEntity(m_sunEntity);
}