#ifndef RENDERINGWORKER_H
#define RENDERINGWORKER_H

#include "canvasWidget.h"
#include "fileLoader.h"
#include "inputmanager.h"
#include "renderComponent.h"
#include "renderingPrimitives.h"

#include <QDebug>
#include <QElapsedTimer>
#include <QMouseEvent>
#include <QThread>
#include <QWindow>
#include <QtConcurrent>

#include <filament/Engine.h>
#include <filament/Renderer.h>
#include <filament/Scene.h>
#include <filament/SwapChain.h>
#include <filament/View.h>

class RenderingWorker : public QObject {
    Q_OBJECT

public:
    // if providing a QThread it must be waited on after renderingWorker.deleteLater(),
    // otherwise the engine destructor might not be called
    RenderingWorker(CanvasWidget* widget, QMutex* mutex, QThread* thread);
    ~RenderingWorker() override;

    void startThread();
    virtual void loadGlb(const QString& filePath, RenderComponent* target = nullptr);
    void addEntity(const RenderingPrimitives::MeshList& meshes, RenderComponent* target = nullptr);
    void setFov(float fov);
    void setFrameTime(int ms);
    bool isInitialized() const;
    QWidget* getWidget();

private:
#define ASSERT_PROPER_THREAD Q_ASSERT(QThread::currentThread() == m_filamentThread);

    RenderComponent* m_mainComponent{nullptr};
    std::vector<RenderComponent*> m_renderComponents{};

protected:
    bool m_initialized{false};
    bool m_captureInput{false};
    bool m_destroyOwnThread{false};
    int m_frameCount = 0;

    std::shared_ptr<filament::Engine> m_engine;
    filament::SwapChain* m_swapChain{nullptr};
    filament::Renderer* m_renderer{nullptr};

    FileLoader* m_fileLoader{nullptr};
    InputManager m_inputManager{};
    CanvasWidget* m_widget; // Widget lives on the main thread

    QThread* m_filamentThread;
    QTimer* m_renderTimer{nullptr};
    QTimer* m_frameCountTimer{nullptr};
    QMutex* m_mutex;

    void run();
    void initialize();
    void stopRendering();
    virtual void afterInitialize();
    virtual void initRenderComponents();
    void renderFrame();
    void resize(const QSize& size);
    void setMainComponent(RenderComponent* component);
    void registerRenderComponent(RenderComponent* component);
    virtual void afterFrameRender();
    void printFrameCount();
    QThread* getFilamentThread();
    void mouseEvent(QMouseEvent* event);
    void keyEvent(QKeyEvent* event);
    void mouseWheelEvent(QWheelEvent* event);
    void setCaptureInput(bool capture);

signals:
    void filamentInitialized();
};

#endif // RENDERINGWORKER_H