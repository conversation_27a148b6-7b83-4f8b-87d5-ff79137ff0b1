#ifndef FILELOADER_H
#define FILELOADER_H

#include <cstdint>
#include <fcntl.h>
#include <fstream>
#include <iostream>
#include <string>
#include <unistd.h>

#include <QDebug>
#include <QObject>
#include <QString>

#include <filament/Engine.h>
#include <filament/Fence.h>
#include <filament/IndexBuffer.h>
#include <filament/Material.h>
#include <filament/RenderableManager.h>
#include <filament/VertexBuffer.h>

#include <filameshio/MeshReader.h>
#include <gltfio/AssetLoader.h>
#include <gltfio/FilamentAsset.h>
#include <gltfio/ResourceLoader.h>
#include <gltfio/TextureProvider.h>
#include <utils/Entity.h>
#include <utils/EntityManager.h>

class FileLoader : public QObject {
    Q_OBJECT
    struct GltfioData {
        filament::gltfio::MaterialProvider* materialProvider{};
        filament::gltfio::AssetLoader* assetLoader{};
        filament::gltfio::TextureProvider* stbProvider{};
        filament::gltfio::TextureProvider* ktxProvider{};
    };

    GltfioData* m_gltfioData = nullptr;

    std::shared_ptr<filament::Engine> m_engine;

    int createdAssets = 0; // for debugging, TODO remove

public:
    FileLoader(QObject* parent, const std::shared_ptr<filament::Engine>& engine)
        : QObject(parent)
        , m_engine(engine) {
    }

    ~FileLoader() override {
        qDebug() << "Destroying FileLoader";
        if (createdAssets > 0) {
            qWarning() << "There are still" << createdAssets << "created assets";
        }
        if (m_gltfioData) {
            m_gltfioData->materialProvider->destroyMaterials();
            delete m_gltfioData->materialProvider;
            delete m_gltfioData->stbProvider;
            delete m_gltfioData->ktxProvider;
            filament::gltfio::AssetLoader::destroy(&m_gltfioData->assetLoader);
        }
    }

    void initializeGltfio() {
        Q_ASSERT(m_engine && !m_gltfioData);
        filament::gltfio::MaterialProvider* materialProvider = filament::gltfio::createJitShaderProvider(m_engine.get());
        m_gltfioData = new GltfioData{
            .materialProvider = materialProvider,
            .assetLoader = filament::gltfio::AssetLoader::create({.engine = m_engine.get(), .materials = materialProvider}),
            .stbProvider = filament::gltfio::createStbProvider(m_engine.get()),
            .ktxProvider = filament::gltfio::createKtx2Provider(m_engine.get()),
        };
    }

    utils::Entity loadFilamesh(const QString& filePath, filament::MaterialInstance* materialInstance) {
        return loadFilamesh(m_engine.get(), filePath, materialInstance);
    }

    static utils::Entity loadFilamesh(filament::Engine* engine, const QString& filePath, filament::MaterialInstance* materialInstance) {
        if (!filePath.endsWith(".filamesh")) {
            qFatal() << "Provided mesh is not a filamesh file" << filePath;
        }

        filamesh::MeshReader::MaterialRegistry materialRegistry;

        // Taken from filamesh::MeshReader::loadMeshFromFile
        // in order to avoid having to use utils::Path, which needs libc++
        int fd = open(filePath.toStdString().c_str(), O_RDONLY | O_CLOEXEC);
        auto size = static_cast<size_t>(lseek(fd, 0, SEEK_END));
        lseek(fd, 0, SEEK_SET);
        char* data = static_cast<char*>(malloc(size)); // NOLINT(hicpp-no-malloc, cppcoreguidelines-no-malloc)
        read(fd, data, size);
        filamesh::MeshReader::Mesh mesh = filamesh::MeshReader::loadMeshFromBuffer(engine, data, nullptr, nullptr, materialRegistry);
        filament::Fence::waitAndDestroy(engine->createFence());
        free(data); // NOLINT(hicpp-no-malloc, cppcoreguidelines-no-malloc)
        close(fd);
        // End stolen block

        if (!mesh.renderable) {
            qFatal() << "Failed to load filamesh file" << filePath;
        }
        auto* vertexBuffer = mesh.vertexBuffer;
        auto* indexBuffer = mesh.indexBuffer;

        utils::Entity entity = utils::EntityManager::get().create();
        filament::RenderableManager::Builder(1)
            .material(0, materialInstance)
            .geometry(0, filament::RenderableManager::PrimitiveType::TRIANGLES, vertexBuffer,
                      indexBuffer, 0, indexBuffer->getIndexCount())
            .culling(false)
            .receiveShadows(false)
            .castShadows(false)
            .build(*engine, entity);

        return entity;
    }

    filament::gltfio::FilamentAsset* loadGlb(const QString& filePath) {
        auto* engine = m_engine.get();
        if (!filePath.endsWith(".glb") && !filePath.endsWith(".gltf")) {
            qCritical() << "Asset must be a glb or gltf file!";
            return nullptr;
        }
        char* filePathChar = filePath.toUtf8().data();
        std::ifstream in(filePathChar, std::ifstream::ate | std::ifstream::binary);
        int64_t contentSize = static_cast<int64_t>(in.tellg());
        if (contentSize <= 0) {
            qCritical() << "Unable to open " << filePathChar;
            return nullptr;
        }
        in = std::ifstream(filePathChar, std::ifstream::binary | std::ifstream::in);
        std::vector<uint8_t> buffer(static_cast<uint64_t>(contentSize));
        if (!in.read(reinterpret_cast<char*>(buffer.data()), contentSize)) {
            qCritical() << "Unable to read " << filePathChar;
            return nullptr;
        }

        if (!m_gltfioData) {
            initializeGltfio();
        }

        auto* asset = m_gltfioData->assetLoader->createAsset(buffer.data(), buffer.size());
        if (!asset) {
            qCritical() << "Unable to load asset";
            return nullptr;
        }

        filament::gltfio::ResourceLoader resourceLoader({.engine = engine, .gltfPath = filePathChar, .normalizeSkinningWeights = true});
        resourceLoader.addTextureProvider("image/png", m_gltfioData->stbProvider);
        resourceLoader.addTextureProvider("image/jpeg", m_gltfioData->stbProvider);
        resourceLoader.addTextureProvider("image/ktx2", m_gltfioData->ktxProvider);
        if (!resourceLoader.loadResources(asset)) {
            qCritical() << "Unable to load resources";
            return nullptr;
        }
        createdAssets++;
        asset->releaseSourceData();
        return asset;
    }

    void destroyAsset(filament::gltfio::FilamentAsset* asset) {
        if (asset) {
            Q_ASSERT(m_gltfioData);
            m_gltfioData->assetLoader->destroyAsset(asset);
            createdAssets--;
        }
    }

    filament::gltfio::FilamentAsset* loadGltf(const QString& filePath) {
        return loadGlb(filePath);
    }
};

#endif // FILELOADER_H