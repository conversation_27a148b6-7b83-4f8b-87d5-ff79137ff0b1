#include "renderingPrimitives.h"
#include "mathUtils.h"

#include <vector>

namespace RenderingPrimitives {

FilamentLine::FilamentLine(const std::vector<std::tuple<float, float, float>>& points, QColor color, int widthcm, QString name)
    : m_width(widthcm)
    , m_name(std::move(name)) {
    QObject::setObjectName(m_name);

    setColor(color);

    for (auto point : points) {
        m_points.emplace_back(std::get<0>(point), std::get<1>(point), std::get<2>(point));
    }
}

void FilamentLine::setColor(const QColor& color) {
    m_objectMesh.rgba = filament::math::float4(color.redF(), color.greenF(), color.blueF(), color.alphaF());
}

MeshList FilamentLine::objectVertices() {
    createVertices();
    return {&m_objectMesh};
}

void FilamentLine::createVertices() {
    m_objectMesh.vertices.clear();

    auto first = *m_points.begin();
    auto last = *m_points.rbegin();

    filament::math::float3 normDir = normalize(first - last);
    filament::math::float3 normPerpDir1(normDir.z, 0, -normDir.x);
    filament::math::float3 normPerpDir2(-normDir.z, 0, normDir.x);

    for (auto m_point : m_points) {
        m_objectMesh.vertices.push_back(m_point + ((m_width / 2) * normPerpDir1));
        m_objectMesh.vertices.push_back(m_point + ((m_width / 2) * normPerpDir2));
    }

    for (int i = 0; i < m_objectMesh.vertices.size(); i++) {
        m_objectMesh.indices.push_back(i);
    }
}

FilamentCylinder::FilamentCylinder(float radius, float length, QColor color, QString name)
    : m_radius(radius)
    , m_length(length)
    , m_name(std::move(name)) {
    QObject::setObjectName(m_name);
    setColor(color);
}

void FilamentCylinder::setColor(const QColor& color) {
    m_objectMesh.rgba = filament::math::float4(color.redF(), color.greenF(), color.blueF(), color.alphaF());
}

void FilamentCylinder::setRotation(float degrees, filament::math::float3 axis) {
    m_objectMesh.rotationDegrees = degrees;
    m_objectMesh.rotationAxis = axis;
}

void FilamentCylinder::setTranslation(filament::math::float3 translation) {
    m_objectMesh.translation = translation;
}

MeshList FilamentCylinder::objectVertices() {
    createVertices();
    return {&m_objectMesh};
}

void FilamentCylinder::createVertices() {
    Q_ASSERT(m_segments >= 3);
    m_objectMesh.vertices.clear();

    std::vector<filament::math::float3> unitCircleVertices = getUnitCircleVertices(m_segments);

    float radians = m_objectMesh.rotationDegrees * M_PI / 180.0f;
    auto rotationMatrix = filament::math::mat4f::rotation(radians, m_objectMesh.rotationAxis);

    for (auto vertex : unitCircleVertices) {
        vertex *= m_radius;

        m_objectMesh.vertices.emplace_back(vertex.x, m_length / 2, vertex.z);
        m_objectMesh.vertices.emplace_back(vertex.x, -m_length / 2, vertex.z);
    }

    // close the cylinder
    m_objectMesh.vertices.push_back(m_objectMesh.vertices[0]);
    m_objectMesh.vertices.push_back(m_objectMesh.vertices[1]);

    for (int i = 0; i < m_objectMesh.vertices.size(); i++) {
        m_objectMesh.indices.push_back(i);
    }
}

std::vector<filament::math::float3> FilamentCylinder::getUnitCircleVertices(int segments) {
    std::vector<filament::math::float3> vertices;
    float step = 2 * M_PI / segments;

    for (int i = 0; i < segments; i++) {
        float theta = step * i;
        vertices.emplace_back(cosf(theta), 0, sinf(theta));
    }

    return vertices;
}

FilamentGoal::FilamentGoal(float radius, float width, float height, QColor color, QString name)
    : m_radius(radius)
    , m_width(width)
    , m_height(height)
    , m_name(std::move(name)) {
    QObject::setObjectName(m_name);

    setColor(color);

    m_cylinderTop.setTranslation(m_topOffset);
    m_cylinderTop.setRotation(90, {1, 0, 0});

    m_cylinderLeft.setTranslation(m_leftOffset);

    m_cylinderRight.setTranslation(m_rightOffset);
}

void FilamentGoal::setColor(const QColor& color) {
    m_cylinderTop.setColor(color);
    m_cylinderLeft.setColor(color);
    m_cylinderRight.setColor(color);
}

void FilamentGoal::setTranslation(filament::math::float3 translation) {
    m_cylinderTop.setTranslation(translation + m_topOffset);
    m_cylinderLeft.setTranslation(translation + m_leftOffset);
    m_cylinderRight.setTranslation(translation + m_rightOffset);
}

MeshList FilamentGoal::objectVertices() {
    MeshList meshList;

    auto topMesh = m_cylinderTop.objectVertices();
    auto leftMesh = m_cylinderLeft.objectVertices();
    auto rightMesh = m_cylinderRight.objectVertices();

    meshList.insert(meshList.end(), topMesh.begin(), topMesh.end());
    meshList.insert(meshList.end(), leftMesh.begin(), leftMesh.end());
    meshList.insert(meshList.end(), rightMesh.begin(), rightMesh.end());

    return meshList;
}

FilamentArc::FilamentArc(float thickness, float radius, float angle, float rotation, int slices, filament::math::float3 position, QColor color, QString name)
    : m_thickness(thickness)
    , m_radius(radius)
    , m_angle(angle)
    , m_slices(slices)
    , m_name(std::move(name)) {
    QObject::setObjectName(m_name);
    setColor(color);

    m_objectMesh.translation = position;
    m_objectMesh.rotationDegrees = rotation;
    m_objectMesh.rotationAxis = filament::math::float3(0, 1, 0);
}

void FilamentArc::setColor(const QColor& color) {
    m_objectMesh.rgba = filament::math::float4(color.redF(), color.greenF(), color.blueF(), color.alphaF());
}

MeshList FilamentArc::objectVertices() {
    createVertices();
    return {&m_objectMesh};
}

std::vector<filament::math::float3> FilamentArc::getArcVertices(float radius) {
    std::vector<filament::math::float3> vertices;
    const float dTheta = ((M_PI * 2) / static_cast<float>(m_slices)) / (360.0f / m_angle);

    // First measure min, center and max Z coords for transformed vertices
    float minZ = std::numeric_limits<float>::max();
    float maxZ = std::numeric_limits<float>::lowest();
    for (int slice = 0; slice <= m_slices; ++slice) {
        // position
        const float theta = static_cast<float>(slice) * dTheta;
        const float ct = qCos(theta);
        const float st = qSin(theta);

        filament::math::float3 vertexPos(radius * ct, 0, radius * st);
        filament::math::float3 transformedVertexPos = m_objectMesh.translation + vertexPos;

        minZ = std::min(transformedVertexPos.z, minZ);
        maxZ = std::max(transformedVertexPos.z, maxZ);
    }
    const float centerz = (minZ + maxZ) / 2.0;

    for (int slice = 0; slice <= m_slices; ++slice) {
        // position
        const float theta = static_cast<float>(slice) * dTheta;
        const float ct = qCos(theta);
        const float st = qSin(theta);

        // Transform vertex and compute Y (elevation) from transformed coords
        filament::math::float3 vertexPos(radius * ct, 0, radius * st);
        filament::math::float3 transformedVertexPos = m_objectMesh.translation + vertexPos;

        float y = 0;
        if (transformedVertexPos.z <= centerz) {
            y = computeLinearInterpolation(Eigen::Vector3d(0, m_heightProfile[0], minZ), Eigen::Vector3d(0, m_heightProfile[1], centerz), transformedVertexPos.z);
        } else {
            y = computeLinearInterpolation(Eigen::Vector3d(0, m_heightProfile[1], centerz), Eigen::Vector3d(0, m_heightProfile[2], maxZ), transformedVertexPos.z);
        }
        vertices.emplace_back(vertexPos.x, y, vertexPos.z);
    }

    return vertices;
}

void FilamentArc::createVertices() {
    m_objectMesh.vertices.clear();

    std::vector<filament::math::float3> innerVertices = getArcVertices(m_radius - (m_thickness / 2.0));
    std::vector<filament::math::float3> outerVertices = getArcVertices(m_radius + (m_thickness / 2.0));

    Q_ASSERT(innerVertices.size() == outerVertices.size());

    for (int i = 0; i < innerVertices.size(); i++) {
        m_objectMesh.vertices.push_back(innerVertices[i]);
        m_objectMesh.vertices.push_back(outerVertices[i]);
    }

    for (int i = 0; i < m_objectMesh.vertices.size(); i++) {
        m_objectMesh.indices.push_back(i);
    }
}

FilamentCircle::FilamentCircle(float thickness, float radius, int slices, QColor color, QString name)
    : m_thickness(thickness)
    , m_radius(radius)
    , m_slices(slices)
    , m_name(std::move(name)) {
    QObject::setObjectName(m_name);
    setColor(color);
}

void FilamentCircle::setColor(const QColor& color) {
    m_objectMesh.rgba = filament::math::float4(color.redF(), color.greenF(), color.blueF(), color.alphaF());
}

void FilamentCircle::setTranslation(filament::math::float3 translation) {
    m_objectMesh.translation = translation;
}

MeshList FilamentCircle::objectVertices() {
    createVertices();
    return {&m_objectMesh};
}

void FilamentCircle::createVertices() {
    m_objectMesh.vertices.clear();
    const float dTheta = (M_PI * 2) / static_cast<float>(m_slices);

    for (int slice = 0; slice <= m_slices; slice++) {
        const float theta = static_cast<float>(slice) * dTheta;
        const float ct = qCos(theta);
        const float st = qSin(theta);

        float y = 0;
        float radius = m_radius - (m_thickness / 2.0);
        if (radius * st <= 0.0) {
            y = computeLinearInterpolation(Eigen::Vector3d(0, m_heightProfile[0], -m_radius), Eigen::Vector3d(0, m_heightProfile[1], 0), radius * st);
        } else {
            y = computeLinearInterpolation(Eigen::Vector3d(0, m_heightProfile[1], 0), Eigen::Vector3d(0, m_heightProfile[2], m_radius), radius * st);
        }
        m_objectMesh.vertices.emplace_back(radius * ct, y, radius * st);

        radius = m_radius + m_thickness / 2.0;
        if (radius * st <= 0.0) {
            y = computeLinearInterpolation(Eigen::Vector3d(0, m_heightProfile[0], -m_radius), Eigen::Vector3d(0, m_heightProfile[1], 0), radius * st);
        } else {
            y = computeLinearInterpolation(Eigen::Vector3d(0, m_heightProfile[1], 0), Eigen::Vector3d(0, m_heightProfile[2], m_radius), radius * st);
        }
        m_objectMesh.vertices.emplace_back(radius * ct, y, radius * st);
    }

    for (int i = 0; i < m_objectMesh.vertices.size(); i++) {
        m_objectMesh.indices.push_back(i);
    }
}

FilamentSpot::FilamentSpot(float radius, QColor color, QString name)
    : m_radius(radius)
    , m_name(std::move(name)) {

    QObject::setObjectName(m_name);

    m_circle = new FilamentCircle(m_radius, m_radius / 2, m_slices, color, m_name + "_circle");

    setColor(color);
}

void FilamentSpot::setColor(const QColor& color) {
    m_circle->setColor(color);
}

void FilamentSpot::setTranslation(filament::math::float3 translation) {
    m_circle->setTranslation(translation);
}

MeshList FilamentSpot::objectVertices() {
    return m_circle->objectVertices();
}

} // namespace RenderingPrimitives
