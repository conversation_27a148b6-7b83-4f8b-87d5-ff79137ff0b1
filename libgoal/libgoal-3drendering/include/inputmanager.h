#ifndef INPUTMANAGER_H
#define INPUTMANAGER_H

#include <QElapsedTimer>
#include <QMouseEvent>
#include <QtConcurrent>

#include <filament/Camera.h>

#include <array>

class InputManager {

    bool m_initialized{false};
    qreal m_oldMouseX = 0;
    qreal m_oldMouseY = 0;
    enum Key {
        W,
        S,
        A,
        D,
        Shift,
        E,
        Q,
        KeyCount
    };
    std::array<bool, KeyCount> m_keysPressed{};
    QElapsedTimer m_timer;

    float m_mouseRotateScale = 1.0;
    float m_mouseTranslateScale = 1.0;
    float m_mouseZoomScale = 1.0;
    float m_keyboardMoveScale = 1.0;

public:
    InputManager() = default;
    ~InputManager() {
        qDebug() << "Destroying InputManager";
    }
    void mouseEvent(QMouseEvent* event, filament::Camera* camera, QSize parentSize);
    void wheelEvent(QWheelEvent* event, filament::Camera* camera) const;
    void keyEvent(QKeyEvent* event);
    void handleKeyboardMovement(filament::Camera* camera);
    void resetKeysPressed();
    static void printCameraDebugInfo(filament::Camera* camera);

    void setInputScales(int mouseRotate, int mouseTranslate, int mouseZoom, int keyboardMove);

private:
    void mouseMoveEvent(QMouseEvent* event, filament::Camera* camera, QSize parentSize);
    void mousePressEvent(QMouseEvent* event);

    filament::math::float3 rotateVector(const filament::math::float3& vec, const filament::math::float3& axis, float theta);
    void rotateCamera(float dx, float dy, filament::Camera* camera);
    void translateCamera(float distanceX, float distanceY, filament::Camera* camera) const;
};

#endif // INPUTMANAGER_H
