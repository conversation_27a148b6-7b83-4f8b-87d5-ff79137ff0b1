#include "renderingWorker.h"

namespace fil = filament;

RenderingWorker::RenderingWorker(CanvasWidget* widget, QMutex* mutex, QThread* thread)
    : m_widget(widget)
    , m_mutex(mutex) {

    if (!m_widget) {
        qDebug() << "No CanvasWidget provided, creating default one";
        m_widget = new CanvasWidget();
    }
    if (!m_mutex) {
        qDebug() << "No mutex provided, creating new one";
        m_mutex = new QMutex();
    }
    if (!thread) {
        // wait and destroy the thread in the destructor
        m_destroyOwnThread = true;
        m_filamentThread = new QThread();
    } else {
        m_filamentThread = thread;
    }

    this->moveToThread(m_filamentThread);

    qDebug() << "Worker moved to thread:" << m_filamentThread;

    QObject::connect(m_filamentThread, &QThread::started, this, &RenderingWorker::run, Qt::QueuedConnection);
    QObject::connect(m_widget, &CanvasWidget::resized, this, &RenderingWorker::resize, Qt::QueuedConnection);
    QObject::connect(m_widget, &CanvasWidget::lostFocus, this, [this]() { m_inputManager.resetKeysPressed(); });
    QObject::connect(m_widget, &CanvasWidget::mouseEvent, this, &RenderingWorker::mouseEvent, Qt::QueuedConnection);
    QObject::connect(m_widget, &CanvasWidget::keyEvent, this, &RenderingWorker::keyEvent, Qt::QueuedConnection);
    QObject::connect(m_widget, &CanvasWidget::mouseWheelEvent, this, &RenderingWorker::mouseWheelEvent, Qt::QueuedConnection);
}

RenderingWorker::~RenderingWorker() {
    qDebug() << "Destroying RenderingWorker";
    ASSERT_PROPER_THREAD
    stopRendering();
    for (auto* component : m_renderComponents) {
        component->deleteAssets(m_fileLoader);
    }
    if (m_destroyOwnThread) {
        auto future = QtConcurrent::run([this]() {
            m_filamentThread->quit();
            m_filamentThread->wait();
            delete m_filamentThread;
        });
    }
}

void RenderingWorker::startThread() {
    if (m_filamentThread->isRunning()) {
        qDebug() << "Thread already running, calling run() manually";
        QMetaObject::invokeMethod(this, &RenderingWorker::run, Qt::QueuedConnection);
    } else {
        qDebug() << "Starting thread";
        m_filamentThread->start();
    }
}

void RenderingWorker::loadGlb(const QString& filePath, RenderComponent* target) {
    if (QThread::currentThread() != this->thread()) {
        QMetaObject::invokeMethod(this, &RenderingWorker::loadGlb, Qt::QueuedConnection, filePath, target);
        return;
    }

    target = target ? target : m_mainComponent;
    if (!m_initialized || !target) {
        qWarning() << "Not initialized, ignoring loadGlb";
        return;
    }
    auto* asset = m_fileLoader->loadGlb(filePath);
    if (!asset) {
        qCritical() << "Failed to load asset";
        return;
    }
    target->addGlb(asset);
}

void RenderingWorker::addEntity(const RenderingPrimitives::MeshList& meshes, RenderComponent* target) {
    if (QThread::currentThread() != this->thread()) {
        QMetaObject::invokeMethod(this, &RenderingWorker::addEntity, Qt::QueuedConnection, meshes, target);
        return;
    }

    target = target ? target : m_mainComponent;
    if (!m_initialized || !target) {
        qWarning() << "Not initialized, ignoring addPitchEntity";
        return;
    }
    target->addEntity(meshes);
}

void RenderingWorker::setFov(float fov) {
    if (QThread::currentThread() != this->thread()) {
        QMetaObject::invokeMethod(this, &RenderingWorker::setFov, Qt::QueuedConnection, fov);
        return;
    }

    if (m_mainComponent) {
        m_mainComponent->setFov(fov);
    } else {
        qWarning() << "No main component set, ignoring setFov";
    }
}

bool RenderingWorker::isInitialized() const {
    return m_initialized;
}

QWidget* RenderingWorker::getWidget() {
    return m_widget;
}

void RenderingWorker::setFrameTime(int ms) {
    if (QThread::currentThread() != this->thread()) {
        QMetaObject::invokeMethod(this, &RenderingWorker::setFrameTime, Qt::QueuedConnection, ms);
        return;
    }

    if (m_renderTimer) {
        m_renderTimer->setInterval(ms);
    }
}

void RenderingWorker::run() {
    ASSERT_PROPER_THREAD
    qDebug() << "Initializing on thread:" << QThread::currentThread();

    initialize();

    resize(m_widget->size());

    m_renderTimer = new QTimer(this);
    m_frameCountTimer = new QTimer(this);

    connect(m_renderTimer, &QTimer::timeout, this, &RenderingWorker::renderFrame);
    m_renderTimer->start(17); // ~60 FPS

    connect(m_frameCountTimer, &QTimer::timeout, this, &RenderingWorker::printFrameCount);
    // m_frameCountTimer->start(1000);

    qDebug() << "RenderingWorker running";
}

void RenderingWorker::initialize() {
    ASSERT_PROPER_THREAD
    m_engine = std::shared_ptr<fil::Engine>(fil::Engine::Builder()
                                                .backend(fil::Engine::Backend::OPENGL)
                                                .featureLevel(fil::backend::FeatureLevel::FEATURE_LEVEL_3)
                                                .build(),
                                            [](fil::Engine* engine) {
                                                qDebug() << "Destroying engine from thread:" << QThread::currentThread();
                                                fil::Engine::destroy(engine);
                                                qDebug() << "Engine destroyed";
                                            });

    m_mutex->lock(); // apparently, winId() is not thread safe
    m_swapChain = m_engine->createSwapChain(
        reinterpret_cast<void*>(m_widget->winId())); // NOLINT(performance-no-int-to-ptr)
    m_mutex->unlock();
    m_renderer = m_engine->createRenderer();
    m_fileLoader = new FileLoader(this, m_engine);

    initRenderComponents();

    m_initialized = true;

    afterInitialize();
}

void RenderingWorker::stopRendering() {
    ASSERT_PROPER_THREAD
    if (m_renderTimer) {
        m_renderTimer->stop();
    }
}

void RenderingWorker::afterInitialize() {
    ASSERT_PROPER_THREAD
    emit filamentInitialized();
}

void RenderingWorker::initRenderComponents() {
    ASSERT_PROPER_THREAD
    Q_ASSERT(m_engine);
    auto* component = new RenderComponent(this, m_engine);
    registerRenderComponent(component);
    setMainComponent(component);
}

void RenderingWorker::renderFrame() {
    ASSERT_PROPER_THREAD
    if (m_renderer->beginFrame(m_swapChain)) {
        m_frameCount++;

        for (auto* component : m_renderComponents) {
            m_renderer->render(component->getView());
        }

        m_renderer->endFrame();

        afterFrameRender();
    }
}

void RenderingWorker::resize(const QSize& size) {
    ASSERT_PROPER_THREAD
    if (!m_initialized) {
        qWarning() << "Not initialized, ignoring resize";
        return;
    }
    qDebug() << "Resizing Filament window to" << size;
    for (auto* component : m_renderComponents) {
        component->resize(size);
    }
}

void RenderingWorker::setMainComponent(RenderComponent* component) {
    if (std::count(m_renderComponents.begin(), m_renderComponents.end(), component) == 0) {
        qWarning() << "Component not registered, ignoring setMainComponent";
        return;
    }
    m_mainComponent = component;
}

void RenderingWorker::registerRenderComponent(RenderComponent* component) {
    if (std::count(m_renderComponents.begin(), m_renderComponents.end(), component) != 0) {
        qWarning() << "Component already registered, ignoring registerRenderComponent";
        return;
    }
    m_renderComponents.push_back(component);
    if (!m_mainComponent) {
        m_mainComponent = component;
    }
}

void RenderingWorker::afterFrameRender() {
    ASSERT_PROPER_THREAD
    if (m_mainComponent) {
        m_inputManager.handleKeyboardMovement(m_mainComponent->getCamera());
    }
}

void RenderingWorker::printFrameCount() {
    qDebug() << m_frameCount << "FPS";
    m_frameCount = 0;
}

QThread* RenderingWorker::getFilamentThread() {
    return m_filamentThread;
}

void RenderingWorker::mouseEvent(QMouseEvent* event) {
    ASSERT_PROPER_THREAD
    if (m_captureInput && m_initialized && m_mainComponent) {
        m_inputManager.mouseEvent(event, m_mainComponent->getCamera(), m_widget->size());
    }
}

void RenderingWorker::keyEvent(QKeyEvent* event) {
    ASSERT_PROPER_THREAD
    if (m_captureInput && m_initialized && m_mainComponent) {
        m_inputManager.keyEvent(event);
    }
}

void RenderingWorker::mouseWheelEvent(QWheelEvent* event) {
    ASSERT_PROPER_THREAD
    if (m_captureInput && m_initialized && m_mainComponent) {
        m_inputManager.wheelEvent(event, m_mainComponent->getCamera());
    }
}

void RenderingWorker::setCaptureInput(bool capture) {
    if (m_captureInput == capture) {
        return;
    }
    m_captureInput = capture;
    if (m_captureInput) {
        m_widget->setFocusPolicy(Qt::StrongFocus);
    } else {
        m_widget->setFocusPolicy(Qt::NoFocus);
        m_inputManager.resetKeysPressed();
    }
}