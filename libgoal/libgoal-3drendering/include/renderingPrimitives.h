#ifndef PRIMITIVES_H
#define PRIMITIVES_H

#include <QColor>
#include <QObject>

#include <filament/RenderableManager.h>

namespace RenderingPrimitives {

struct ObjectMesh {
    filament::RenderableManager::PrimitiveType primitiveType;
    std::vector<filament::math::float3> vertices;
    std::vector<uint16_t> indices;

    filament::math::float4 rgba = filament::math::float4(1.0f, 0.0f, 0.0f, 1.0f);
    filament::math::float3 translation = filament::math::float3(0.0f, 0.0f, 0.0f);
    filament::math::float3 rotationAxis = filament::math::float3(1.0f, 0.0f, 0.0f); // x-axis
    float rotationDegrees = 0.0f;
};

using MeshList = std::vector<ObjectMesh*>;

class FilamentLine : public QObject {
    Q_OBJECT

    const QString m_name;
    ObjectMesh m_objectMesh = ObjectMesh{.primitiveType = filament::RenderableManager::PrimitiveType::TRIANGLE_STRIP, .vertices = {}, .indices = {}};

    int m_width;
    std::vector<filament::math::float3> m_points;

public:
    FilamentLine(const std::vector<std::tuple<float, float, float>>& points, QColor color, int widthcm, QString name);
    void setColor(const QColor& color);
    MeshList objectVertices();

private:
    void createVertices();
};

class FilamentCylinder : public QObject {
    Q_OBJECT

    const QString m_name;
    ObjectMesh m_objectMesh = ObjectMesh{.primitiveType = filament::RenderableManager::PrimitiveType::TRIANGLE_STRIP, .vertices = {}, .indices = {}};

    static constexpr int m_segments = 16;
    float m_radius;
    float m_length;

public:
    FilamentCylinder(float radius, float length, QColor color, QString name);
    void setColor(const QColor& color);
    MeshList objectVertices();

    void setRotation(float degrees, filament::math::float3 axis);
    void setTranslation(filament::math::float3 translation);

private:
    void createVertices();
    std::vector<filament::math::float3> getUnitCircleVertices(int segments);
};

class FilamentGoal : public QObject {
    Q_OBJECT

    const QString m_name;

    float m_radius;
    float m_width;
    float m_height;

    FilamentCylinder m_cylinderTop = FilamentCylinder(m_radius, m_width, QColor(), m_name + "_cylinderTop");
    FilamentCylinder m_cylinderLeft = FilamentCylinder(m_radius, m_height, QColor(), m_name + "_cylinderLeft");
    FilamentCylinder m_cylinderRight = FilamentCylinder(m_radius, m_height, QColor(), m_name + "_cylinderRight");

    filament::math::float3 m_topOffset = {0, m_height, 0};
    filament::math::float3 m_leftOffset = {0, m_height / 2, -m_width / 2};
    filament::math::float3 m_rightOffset = {0, m_height / 2, m_width / 2};

public:
    FilamentGoal(float radius, float width, float height, QColor color, QString name);
    void setColor(const QColor& color);
    MeshList objectVertices();

    void setTranslation(filament::math::float3 translation);
};

class FilamentArc : public QObject {
    Q_OBJECT

    const QString m_name;
    ObjectMesh m_objectMesh = ObjectMesh{.primitiveType = filament::RenderableManager::PrimitiveType::TRIANGLE_STRIP, .vertices = {}, .indices = {}};

    float m_thickness;
    float m_radius;
    float m_angle;
    int m_slices;
    std::vector<float> m_heightProfile = {0.0, 0.0, 0.0};

public:
    FilamentArc(float thickness, float radius, float angle, float rotation, int slices, filament::math::float3 position, QColor color, QString name);
    void setColor(const QColor& color);
    MeshList objectVertices();

private:
    void createVertices();
    std::vector<filament::math::float3> getArcVertices(float radius);
};

class FilamentCircle : public QObject {
    Q_OBJECT

    const QString m_name;
    ObjectMesh m_objectMesh = ObjectMesh{.primitiveType = filament::RenderableManager::PrimitiveType::TRIANGLE_STRIP, .vertices = {}, .indices = {}};

    float m_thickness;
    float m_radius;
    int m_slices;
    std::vector<float> m_heightProfile = {0.0, 0.0, 0.0};

public:
    FilamentCircle(float thickness, float radius, int slices, QColor color, QString name);
    void setColor(const QColor& color);
    MeshList objectVertices();

    void setTranslation(filament::math::float3 translation);

private:
    void createVertices();
};

class FilamentSpot : public QObject {
    Q_OBJECT

    const QString m_name;
    FilamentCircle* m_circle;

    float m_radius;
    const int m_slices = 16;

public:
    FilamentSpot(float radius, QColor color, QString name);
    void setColor(const QColor& color);
    MeshList objectVertices();

    void setTranslation(filament::math::float3 translation);
};

} // namespace RenderingPrimitives

#endif // PRIMITIVES_H
