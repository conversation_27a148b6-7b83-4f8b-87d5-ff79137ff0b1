#ifndef DISTUTILS_H
#define DISTUTILS_H

#include <QDebug>
#include <QElapsedTimer>
#include <QPoint>
#include <QSize>

#include <array>
#include <vector>

#include <filament/Camera.h>

#include <opencv2/calib3d.hpp>
#include <opencv2/core/mat.hpp>
#include <opencv2/imgproc.hpp>

struct SimulatedCamera {
    QSize resolution{1920, 1080};
    float focalLength{1000.0f};
    std::array<float, 8> distortionCoefficients{};
};

namespace distortionUtils {

inline cv::Mat createCameraMatrix(const double focalLength, const cv::Point2d& imageCenter) {
    cv::Mat cameraMatrix(3, 3, CV_64F, cv::Scalar(0));
    cameraMatrix.at<double>(0, 0) = cameraMatrix.at<double>(1, 1) = focalLength;
    cameraMatrix.at<double>(0, 2) = imageCenter.x;
    cameraMatrix.at<double>(1, 2) = imageCenter.y;
    cameraMatrix.at<double>(2, 2) = 1;
    return cameraMatrix;
}

inline std::vector<filament::math::float2> undistort(std::vector<filament::math::float2>& vertices, SimulatedCamera camera) {
    int width = camera.resolution.width();
    int height = camera.resolution.height();
    double focalLen = camera.focalLength;

    std::vector<cv::Point2f> pointsUndistorted;
    std::vector<cv::Point2f> pointsDistorted;

    pointsDistorted.reserve(vertices.size());
    for (auto& vertice : vertices) {
        pointsDistorted.emplace_back(vertice.x, vertice.y);
    }

    cv::Mat cameraMatrix = createCameraMatrix(focalLen, cv::Point2d(width / 2, height / 2));
    auto criteria = cv::TermCriteria(cv::TermCriteria::COUNT + cv::TermCriteria::EPS, 100, 0.01);
    cv::undistortPoints(pointsDistorted, pointsUndistorted, cameraMatrix, camera.distortionCoefficients, cv::noArray(), cameraMatrix, criteria);

    std::vector<filament::math::float2> undistortedConverted;
    for (auto& i : pointsUndistorted) {
        if (isnan(i.x) || isnan(i.y)) {
            undistortedConverted.emplace_back(-1, -1); // intentionally set to an invalid point, prevents flickering
        } else {
            undistortedConverted.emplace_back(i.x, i.y);
        }
    }

    return undistortedConverted;
}

inline std::pair<std::vector<filament::math::float2>, std::vector<uint16_t>>
generateQuadGrid(QSize size, int n) {
    int width = size.width();
    int height = size.height();

    std::vector<filament::math::float2> vertices;
    std::vector<uint16_t> indices;

    for (int row = 0; row <= n; row++) {
        for (int col = 0; col <= n; col++) {
            float x = (static_cast<float>(width) * col / n);
            float y = (static_cast<float>(height) * row / n);
            vertices.emplace_back(x, y);
        }
    }

    for (int row = 0; row < n; row++) {
        for (int col = 0; col < n; col++) {
            int topLeft = (row * (n + 1)) + col;
            int topRight = topLeft + 1;
            int bottomLeft = ((row + 1) * (n + 1)) + col;
            int bottomRight = bottomLeft + 1;

            indices.push_back(topLeft);
            indices.push_back(bottomRight);
            indices.push_back(bottomLeft);

            indices.push_back(topLeft);
            indices.push_back(topRight);
            indices.push_back(bottomRight);
        }
    }

    return std::make_pair(vertices, indices);
}

} // namespace distortionUtils

#endif // DISTUTILS_H