#ifndef CANVASWIDGET_H
#define CANVASWIDGET_H

#include <QMouseEvent>
#include <QWidget>

class CanvasWidget : public QWidget {
    Q_OBJECT

public:
    CanvasWidget() {
        setFocusPolicy(Qt::StrongFocus); // Needed to receive key events, TODO remove later
        this->winId();                   // Create winID, crashes sometimes without this (no idea why)
    }

    ~CanvasWidget() override {
        qDebug() << "Destroying CanvasWidget";
    }

private:
    void resizeEvent(QResizeEvent* event) override {
        QWidget::resizeEvent(event);
        emit resized(event->size());
    }

    void focusInEvent(QFocusEvent* event) override {
        // Override to prevent flicker on focus gain
    }

    void focusOutEvent(QFocusEvent* event) override {
        // Override to 1) prevent flicker on focus loss and 2) emit signal for input state reset
        emit lostFocus();
    }

    // Events must be copied if not handled immediately
    void mouseMoveEvent(QMouseEvent* event) override {
        auto* eventCopy = new QMouseEvent(event->type(), event->position(), event->scenePosition(), event->globalPosition(), event->button(), event->buttons(), event->modifiers());
        emit mouseEvent(eventCopy);
    }

    void mousePressEvent(QMouseEvent* event) override {
        auto* eventCopy = new QMouseEvent(event->type(), event->position(), event->scenePosition(), event->globalPosition(), event->button(), event->buttons(), event->modifiers());
        emit mouseEvent(eventCopy);
    }

    void keyPressEvent(QKeyEvent* event) override {
        auto* eventCopy = new QKeyEvent(event->type(), event->key(), event->modifiers(), event->text(), event->isAutoRepeat(), event->count());
        emit keyEvent(eventCopy);
    }

    void keyReleaseEvent(QKeyEvent* event) override {
        auto* eventCopy = new QKeyEvent(event->type(), event->key(), event->modifiers(), event->text(), event->isAutoRepeat(), event->count());
        emit keyEvent(eventCopy);
    }

    void wheelEvent(QWheelEvent* event) override {
        auto* eventCopy = new QWheelEvent(event->position(), event->globalPosition(), event->pixelDelta(), event->angleDelta(), event->buttons(), event->modifiers(), event->phase(), event->inverted());
        emit mouseWheelEvent(eventCopy);
    }

signals:
    void resized(QSize size);
    void lostFocus();
    void mouseEvent(QMouseEvent* event);
    void keyEvent(QKeyEvent* event);
    void mouseWheelEvent(QWheelEvent* event);
};

#endif // CANVASWIDGET_H