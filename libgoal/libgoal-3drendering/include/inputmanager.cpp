#include "inputmanager.h"

void InputManager::mouseMoveEvent(QMouseEvent* event, filament::Camera* camera, QSize parentSize) {
    if (!camera) {
        return;
    }

    qreal dx = (event->scenePosition().x() - m_oldMouseX) / parentSize.width(); // consider using event->position() ?
    qreal dy = (event->scenePosition().y() - m_oldMouseY) / parentSize.height();
    m_oldMouseX = event->scenePosition().x();
    m_oldMouseY = event->scenePosition().y();

    if (event->buttons() == Qt::LeftButton) {
        rotateCamera(dx, dy, camera);
    } else if (event->buttons() == Qt::RightButton) {
        translateCamera(dx, dy, camera);
    }
}

void InputManager::mousePressEvent(QMouseEvent* event) {
    m_oldMouseX = event->scenePosition().x(); // consider using event->position() ?
    m_oldMouseY = event->scenePosition().y();
}

void InputManager::mouseEvent(QMouseEvent* event, filament::Camera* camera, QSize parentSize) {
    if (event->type() == QEvent::MouseButtonPress) {
        mousePressEvent(event);
    } else if (event->type() == QEvent::MouseMove) {
        mouseMoveEvent(event, camera, parentSize);
    }
}

void InputManager::rotateCamera(float dx, float dy, filament::Camera* camera) {
    filament::math::float3 position = camera->getPosition();
    filament::math::float3 forward = camera->getForwardVector();
    filament::math::float3 up = camera->getUpVector();
    filament::math::float3 left = camera->getLeftVector();

    float yawAngle = dx * m_mouseRotateScale;
    float pitchAngle = dy * m_mouseRotateScale;

    constexpr float EPSILON = 0.05f;
    constexpr float minPitch = (-filament::math::F_PI / 2) + EPSILON;
    constexpr float maxPitch = (filament::math::F_PI / 2) - EPSILON;

    float currentPitchAngle = std::asin(dot(forward, filament::math::float3(0, 1, 0)));
    pitchAngle = std::clamp(pitchAngle + currentPitchAngle, minPitch, maxPitch) - currentPitchAngle;

    filament::math::float3 newForward = rotateVector(forward, up, yawAngle);
    newForward = rotateVector(newForward, left, pitchAngle);

    filament::math::float3 newCenter = position + newForward;
    camera->lookAt(position, newCenter);
}

void InputManager::keyEvent(QKeyEvent* event) {
    if (event->isAutoRepeat()) {
        return;
    }

    bool isPressEvent = event->type() == QEvent::KeyPress;

    switch (event->key()) {
    case Qt::Key_W:
        m_keysPressed[Key::W] = isPressEvent;
        break;
    case Qt::Key_S:
        m_keysPressed[Key::S] = isPressEvent;
        break;
    case Qt::Key_A:
        m_keysPressed[Key::A] = isPressEvent;
        break;
    case Qt::Key_D:
        m_keysPressed[Key::D] = isPressEvent;
        break;
    case Qt::Key_Shift:
        m_keysPressed[Key::Shift] = isPressEvent;
        break;
    case Qt::Key_E:
        m_keysPressed[Key::E] = isPressEvent;
        break;
    case Qt::Key_Q:
        m_keysPressed[Key::Q] = isPressEvent;
        break;
    default:
        break;
    }
}

void InputManager::handleKeyboardMovement(filament::Camera* camera) {
    if (!camera) {
        return;
    }

    auto position = camera->getPosition();
    auto forward = camera->getForwardVector();
    auto left = camera->getLeftVector();

    if (!m_timer.isValid()) {
        m_timer.start();
    }
    double scaleFactor = 0.01 * m_timer.restart() * m_keyboardMoveScale;

    if (m_keysPressed[Key::Shift]) {
        scaleFactor *= 5;
    }
    if (m_keysPressed[Key::W]) {
        position += forward * scaleFactor;
    }
    if (m_keysPressed[Key::S]) {
        position -= forward * scaleFactor;
    }
    if (m_keysPressed[Key::A]) {
        position -= left * scaleFactor;
    }
    if (m_keysPressed[Key::D]) {
        position += left * scaleFactor;
    }
    if (m_keysPressed[Key::E]) {
        position += filament::math::float3(0, scaleFactor, 0);
    }
    if (m_keysPressed[Key::Q]) {
        position -= filament::math::float3(0, scaleFactor, 0);
    }

    filament::math::float3 newCenter = position + forward;
    camera->lookAt(position, newCenter);
}

void InputManager::resetKeysPressed() {
    for (auto& key : m_keysPressed) {
        key = false;
    }
}

void InputManager::translateCamera(float distanceX, float distanceY, filament::Camera* camera) const {
    auto position = camera->getPosition();
    auto up = camera->getUpVector();
    auto left = camera->getLeftVector();
    auto forward = camera->getForwardVector();

    position -= left * distanceX * m_mouseTranslateScale * 10;
    position += up * distanceY * m_mouseTranslateScale * 10;

    filament::math::float3 newCenter = position + forward;
    camera->lookAt(position, newCenter);
}

filament::math::float3 InputManager::rotateVector(const filament::math::float3& vec, const filament::math::float3& axis, float theta) {
    const double cosAngle = cos(theta);
    const double sinAngle = sin(theta);

    return vec * cosAngle + cross(axis, vec) * sinAngle + axis * dot(axis, vec) * (1 - cosAngle);
}

void InputManager::wheelEvent(QWheelEvent* event, filament::Camera* camera) const {
    double scaleFactor = 0.005 * event->angleDelta().y() * m_mouseZoomScale;
    auto position = camera->getPosition();
    auto forward = camera->getForwardVector();
    position += forward * scaleFactor;
    camera->lookAt(position, position + forward);
}

void InputManager::setInputScales(int mouseRotate, int mouseTranslate, int mouseZoom, int keyboardMove) {
    m_mouseRotateScale = mouseRotate;
    m_mouseTranslateScale = mouseTranslate;
    m_mouseZoomScale = mouseZoom;
    m_keyboardMoveScale = keyboardMove;
    qDebug() << "Input scales set to:" << m_mouseRotateScale << m_mouseTranslateScale << m_mouseZoomScale << m_keyboardMoveScale << this;
}

void InputManager::printCameraDebugInfo(filament::Camera* camera) {
    auto pos = camera->getPosition();
    auto up = camera->getUpVector();
    auto forward = camera->getForwardVector();
    auto right = cross(up, forward);
    auto center = pos + forward;

    double rounding = 0.01;

    qDebug() << "-------------------------------------------------------";
    qDebug() << "time:" << QTime::currentTime().toString();
    qDebug() << qSetFieldWidth(8) << "pos:"
             << qSetFieldWidth(4) << round(pos.x / rounding) * rounding
             << round(pos.y / rounding) * rounding
             << round(pos.z / rounding) * rounding;

    qDebug() << qSetFieldWidth(8) << "center:"
             << qSetFieldWidth(4) << round(center.x / rounding) * rounding
             << round(center.y / rounding) * rounding
             << round(center.z / rounding) * rounding;

    qDebug() << qSetFieldWidth(8) << "right:"
             << qSetFieldWidth(4) << round(right.x / rounding) * rounding
             << round(right.y / rounding) * rounding
             << round(right.z / rounding) * rounding;

    qDebug() << qSetFieldWidth(8) << "up:"
             << qSetFieldWidth(4) << round(up.x / rounding) * rounding
             << round(up.y / rounding) * rounding
             << round(up.z / rounding) * rounding;

    qDebug() << qSetFieldWidth(8) << "forward:"
             << qSetFieldWidth(4) << round(forward.x / rounding) * rounding
             << round(forward.y / rounding) * rounding
             << round(forward.z / rounding) * rounding;

    auto matrix = camera->getModelMatrix();
    qDebug() << "\t model matrix:";
    for (int i = 0; i < 4; ++i) {
        QString rowString;
        for (int j = 0; j < 4; ++j) {
            double roundedValue = std::round(matrix[i][j] / rounding) * rounding;
            rowString += QString("%1, ").arg(roundedValue, 6, 'f', 2);
        }
        qDebug().noquote() << rowString;
    }
};