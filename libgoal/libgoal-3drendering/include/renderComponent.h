#ifndef RENDERCOMPONENT_H
#define RENDERCOMPONENT_H

#include "renderingPrimitives.h"

#include <include/fileLoader.h>

#include <QObject>
#include <QSize>

#include <cmath>

#include <filament/Camera.h>
#include <filament/ColorGrading.h>
#include <filament/Engine.h>
#include <filament/IndexBuffer.h>
#include <filament/LightManager.h>
#include <filament/Material.h>
#include <filament/MaterialInstance.h>
#include <filament/RenderTarget.h>
#include <filament/RenderableManager.h>
#include <filament/Renderer.h>
#include <filament/Scene.h>
#include <filament/Skybox.h>
#include <filament/Texture.h>
#include <filament/TransformManager.h>
#include <filament/VertexBuffer.h>
#include <filament/View.h>
#include <filament/Viewport.h>

#include <gltfio/FilamentAsset.h>
#include <qcolor.h>
#include <utils/EntityManager.h>

class RenderComponent : public QObject {
    Q_OBJECT

public:
    RenderComponent(QObject* parent, const std::shared_ptr<filament::Engine>& engine, QSize size = QSize(1920, 1080));
    ~RenderComponent() override;

    void deleteAssets(FileLoader* fileLoader);
    void resize(QSize newSize);
    void addEntity(const RenderingPrimitives::MeshList& meshes);
    void addEntity(utils::Entity entity);
    void addGlb(filament::gltfio::FilamentAsset* asset);
    void setSkybox(QColor color);

    filament::View* getView();
    filament::Camera* getCamera();

    void setFov(float fov);
    void setMsaa(bool enabled, int sampleCount);
    void addSun(int intensity, filament::math::float3 color = {1, 1, 1});

private:
    static constexpr uint8_t SINGLE_COLOR_PACKAGE[] = // NOLINT
        {
#include "materials/singleColor.filamat"
        };

    std::shared_ptr<filament::Engine> m_engine;

    filament::View* m_view;
    filament::Scene* m_scene;
    utils::Entity m_cameraEntity;
    filament::Camera* m_camera;
    utils::Entity m_sunEntity;

    std::vector<utils::Entity> m_entities;
    std::vector<filament::gltfio::FilamentAsset*> m_assets;

    float m_cameraNear = 1.0f;
    float m_cameraFar = 100000.0f;
    float m_cameraFov = 90.0f;

    QSize m_size;
    QSize m_sizeMargin = QSize(0, 0);
};

#endif // RENDERCOMPONENT_H