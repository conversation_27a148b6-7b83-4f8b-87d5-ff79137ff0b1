#pragma once

#include <Eigen/Geometry>

/*
 * Linear interpolation between two known points
 * https://en.wikipedia.org/wiki/Linear_interpolation
 * we have z coordination instead of x
 */
inline double computeLinearInterpolation(Eigen::Vector3d left, Eigen::Vector3d right, double zCoord) {
    return (left.y() * (right.z() - zCoord) + right.y() * (zCoord - left.z())) / (right.z() - left.z());
}

inline double computeLinearExtrapolation(Eigen::Vector3d left, Eigen::Vector3d right, double zCoord) {
    // compute linear extrapolation from two points using the line y = a*z + b
    double coefA = (left.y() - right.y()) / (left.z() - right.z());
    double coefB = left.y() - coefA * left.z();
    return coefA * zCoord + coefB;
}
