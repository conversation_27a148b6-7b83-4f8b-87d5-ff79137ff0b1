#ifndef CAMERAPARAMS_H
#define CAMERAPARAMS_H

#include "mathUtils.h"
#include "serialization.h"

#include <Eigen/Geometry>
#include <QSize>
#include <opencv2/core/eigen.hpp>
#include <opencv2/core/mat.hpp>
#include <opencv2/core/types.hpp>

namespace CameraParams {

enum class CameraView {
    main,
    reverse,
    left,
    right
};

// Q_ENUM_NS causes a crash. Consider retrying after updating Qt beyond version 6.5.2.
inline QString toString(CameraView view) {
    switch (view) {
    case CameraView::main:
        return "main";
    case CameraView::reverse:
        return "reverse";
    case CameraView::left:
        return "left";
    case CameraView::right:
        return "right";
    default:
        return "main";
    }
}

inline CameraView fromString(const QString& str) {
    if (str == "main")
        return CameraView::main;
    if (str == "reverse")
        return CameraView::reverse;
    if (str == "left")
        return CameraView::left;
    if (str == "right")
        return CameraView::right;
    return CameraView::main;
}

struct Intrinsic {
    cv::Size resolution{};
    cv::Point2d imageCenter{};
    double focalLength{};
    std::vector<double> distortionCoeffs{};

    static constexpr double k_chipSizeCoef = 1 / 100.0;

    cv::Size2d chipSize() const {
        return {resolution.width * k_chipSizeCoef, resolution.height * k_chipSizeCoef};
    }

    cv::Mat cameraMatrix() const {
        return cameraMatrix(focalLength, imageCenter);
    }

    static cv::Mat cameraMatrix(const double focalLength, const cv::Point2d& imageCenter) {
        cv::Mat cameraMatrix(3, 3, CV_64F, cv::Scalar(0));
        cameraMatrix.at<double>(0, 0) = cameraMatrix.at<double>(1, 1) = focalLength;
        cameraMatrix.at<double>(0, 2) = imageCenter.x;
        cameraMatrix.at<double>(1, 2) = imageCenter.y;
        cameraMatrix.at<double>(2, 2) = 1;
        return cameraMatrix;
    }

    static cv::Mat inverseCameraMatrix(cv::Mat camMatrix) {
        cv::Mat inverseCamMatrix;
        camMatrix.copyTo(inverseCamMatrix);
        inverseCamMatrix.at<double>(0, 0) = 1 / camMatrix.at<double>(0, 0);
        inverseCamMatrix.at<double>(1, 1) = 1 / camMatrix.at<double>(1, 1);
        inverseCamMatrix.at<double>(0, 2) = -camMatrix.at<double>(0, 2) / camMatrix.at<double>(0, 0);
        inverseCamMatrix.at<double>(1, 2) = -camMatrix.at<double>(1, 2) / camMatrix.at<double>(1, 1);

        return inverseCamMatrix;
    }

    QSize qResolution() const {
        return {resolution.width, resolution.height};
    }

    double angleFieldOfView() const {
        const cv::Size2d senzorSize = chipSize();
        const double focalLengthmm = focalLength * senzorSize.width / resolution.width;
        const double angle = 2 * std::atan(senzorSize.width / (2 * focalLengthmm));
        return angle;
    }

    Intrinsic withBorder(int border) const {
        Intrinsic intrCamParamsWithBorder = *this;
        intrCamParamsWithBorder.resolution = cv::Size(resolution.width + 2 * border, resolution.height + 2 * border);
        intrCamParamsWithBorder.imageCenter = cv::Point2d(imageCenter.x + border, imageCenter.y + border);
        return intrCamParamsWithBorder;
    }

    MSGPACK_DEFINE_MAP(resolution, imageCenter, focalLength, distortionCoeffs);
};

struct Extrinsic {
    Extrinsic() = default;

    Extrinsic(Eigen::Vector3d rotationCenter, Eigen::Vector3d rotation, double displacement)
                : tVec(std::move(rotationCenter))
                , rVec(std::move(rotation))
                , displacement(displacement) {
    }

    Eigen::Vector3d rotation() const {
        return rVec;
    }

    Eigen::Vector3d rotationCenter() const {
        return tVec;
    }

    double pitch() const {
        return rVec[0];
    }

    double yaw() const {
        return rVec[1];
    }

    double roll() const {
        return rVec[2];
    }

    double displacementZ() const {
        return displacement;
    }

    Eigen::Vector3d opticalCenter() const {
        return tVec + opticalDisplacement(tVec, rVec, displacement);
    }

    static Eigen::Vector3d opticalDisplacement(Eigen::Vector3d tVec, Eigen::Vector3d rVec, double displacement) {
        const Eigen::Vector3d vec(0.0, 0.0, displacement);
        const Eigen::Quaterniond rot = fromEulerAngles(rVec);
        return rot * vec;
    }

    MSGPACK_DEFINE_MAP(tVec, rVec, displacement);

private:
    Eigen::Vector3d tVec{};
    Eigen::Vector3d rVec{};
    double displacement{};
};

} // namespace CameraParams

#endif // CAMERAPARAMS_H
