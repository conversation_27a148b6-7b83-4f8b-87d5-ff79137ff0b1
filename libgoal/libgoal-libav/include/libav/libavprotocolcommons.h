#ifndef LIBAVPROTOCOLCOMMONS_H
#define LIBAVPROTOCOLCOMMONS_H

#include "framemetadata.h"
extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/pixdesc.h>
#include <libavutil/hwcontext.h>
#include <libavutil/opt.h>
#include <libavutil/avassert.h>
#include <libavutil/imgutils.h>
#include <libavutil/intreadwrite.h>
#include <libswscale/swscale.h>
#include <libswresample/swresample.h>
}

#include <QAudioFormat>
#include <QImage>
#include <QByteArray>
#include <QMap>
#include <QIODevice>

namespace libavProtocolCommons {

enum MessageTypeDecoderToReader {
    //decoder -> reader
    LoadURL,
    ReadRequest,
    ReadRequestNext,
    Stop
};
enum MessageTypeReaderToDecoder {
    //reader -> decoder
    MCReaderData, //data is the MediaCommon output after probe
    Packets, //data is QList of GoalAVPackets
    EOFDetected, //data is only the qint64 with EOF
    ErrorMessage, //data is QString with error message
    NoMediaLoaded, //no data, means that libavReader does not have any file loaded! (could happen after reconnection)
};


struct GoalAVPacket {

    GoalAVPacket() {
        //default
    }
    GoalAVPacket(AVPacket *p) {
        if(!p) {
            return;
        }
        pts = p->pts;
        dts = p->dts;
        stream_index = p->stream_index;
        flags = p->flags;
        duration = p->duration;
        pos = p->pos;

        if(p->side_data) {
            sideDataType = p->side_data->type;
            side_data_elems = p->side_data_elems;
            sideData = QByteArray(reinterpret_cast<const char*>(p->side_data->data), p->side_data->size);
        }


        data = QByteArray(reinterpret_cast<const char*>(p->data), p->size);
    }

    void toAVPacketShallow(AVPacket *p) {
        p->pts = pts;
        p->dts = dts;
        p->stream_index = stream_index;
        p->flags = flags;
        p->duration = duration;
        p->pos = pos;

        //TODO sidedata?

        p->data = reinterpret_cast<uint8_t*>(data.data()); //is good until the object is alive...
        p->size = data.size();
    }

    friend QDataStream &operator<<(QDataStream &out, const GoalAVPacket &rs) {
        out << rs.pts;
        out << rs.dts;
        out << rs.stream_index;
        out << rs.flags;
        out << rs.duration;
        out << rs.pos;
        out << static_cast<qint32>(rs.sideDataType);
        out << rs.side_data_elems;

        out << rs.data;
        out << rs.sideData;

        return out;
    }
    friend QDataStream &operator>>(QDataStream &in, GoalAVPacket &rs) {
        in >> rs.pts;
        in >> rs.dts;
        in >> rs.stream_index;
        in >> rs.flags;
        in >> rs.duration;
        in >> rs.pos;
//        in >> rs.sideDataType
        int sidedatatype;
        in >> sidedatatype;
        rs.sideDataType = static_cast<AVPacketSideDataType>(sidedatatype);
        in >> rs.side_data_elems;

        in >> rs.data;
        in >> rs.sideData;

        return in;
    }

    qint64 pts{-1};
    qint64 dts{-1};
    QByteArray data;
    qint32 stream_index{-1};
    qint32 flags{0};
    qint64 duration{-1};
    qint64 pos{-1};

    QByteArray sideData;
    AVPacketSideDataType sideDataType;
    qint64 side_data_elems{0};
};


struct GoalAVCodecParameters {

    GoalAVCodecParameters() {
        //keep default
    }

    GoalAVCodecParameters(AVCodecParameters *avcp) {
        if(!avcp) {
            return;
        }

        codec_type = avcp->codec_type;
        codec_id = avcp->codec_id;
        codec_tag = avcp->codec_tag;
        format = avcp->format;
        bit_rate = avcp->bit_rate;
        bits_per_coded_sample = avcp->bits_per_coded_sample;
        bits_per_raw_sample = avcp->bits_per_raw_sample;
        profile = avcp->profile;
        level = avcp->level;

        width = avcp->width;
        height = avcp->height;
        sample_aspect_ratio_num = avcp->sample_aspect_ratio.num;
        sample_aspect_ratio_den = avcp->sample_aspect_ratio.den;
        field_order = avcp->field_order;

        color_range = avcp->color_range;
        color_primaries = avcp->color_primaries;
        color_trc = avcp->color_trc;
        color_space = avcp->color_space;
        chroma_location = avcp->chroma_location;

        video_delay = avcp->video_delay;
#if LIBAVCODEC_VERSION_MAJOR < 61
        channel_layout = avcp->channel_layout;
        channels = avcp->channels;
#else
        // For FFmpeg 7+, extract channel layout from the new structure
        // but store it in the old format for compatibility
        if (avcp->ch_layout.order == AV_CHANNEL_ORDER_NATIVE) {
            channel_layout = avcp->ch_layout.u.mask;
        } else if (avcp->ch_layout.nb_channels == 1) {
            channel_layout = AV_CH_LAYOUT_MONO;
        } else if (avcp->ch_layout.nb_channels == 2) {
            channel_layout = AV_CH_LAYOUT_STEREO;
        } else {
            // Default to a reasonable value based on channel count
            switch (avcp->ch_layout.nb_channels) {
                case 3: channel_layout = AV_CH_LAYOUT_2POINT1; break;
                case 4: channel_layout = AV_CH_LAYOUT_QUAD; break;
                case 5: channel_layout = AV_CH_LAYOUT_5POINT0; break;
                case 6: channel_layout = AV_CH_LAYOUT_5POINT1; break;
                case 7: channel_layout = AV_CH_LAYOUT_6POINT1; break;
                case 8: channel_layout = AV_CH_LAYOUT_7POINT1; break;
                default: channel_layout = 0; break;
            }
        }
        // Always store the channel count from the new structure
        channels = avcp->ch_layout.nb_channels;
#endif
        sample_rate = avcp->sample_rate;
        block_align = avcp->block_align;
        frame_size = avcp->frame_size;
        initial_padding = avcp->initial_padding;
        trailing_padding = avcp->trailing_padding;
        seek_preroll = avcp->seek_preroll;

        extradata = QByteArray(reinterpret_cast<const char*>(avcp->extradata), avcp->extradata_size);
    }

    AVCodecParameters toAVCodecParametersShallow() { //extra data will exist while this struct object will exist
        AVCodecParameters avcp;

        avcp.codec_type = codec_type;
        avcp.codec_id = codec_id;
        avcp.codec_tag = codec_tag;
        avcp.format = format;
        avcp.bit_rate = bit_rate;
        avcp.bits_per_coded_sample = bits_per_coded_sample;
        avcp.bits_per_raw_sample = bits_per_raw_sample;
        avcp.profile = profile;
        avcp.level = level;

        avcp.width = width;
        avcp.height = height;
        avcp.sample_aspect_ratio.num = sample_aspect_ratio_num;
        avcp.sample_aspect_ratio.den = sample_aspect_ratio_den;
        avcp.field_order = field_order;

        avcp.color_range = color_range;
        avcp.color_primaries = color_primaries;
        avcp.color_trc = color_trc;
        avcp.color_space = color_space;
        avcp.chroma_location = chroma_location;

        avcp.video_delay = video_delay;
#if LIBAVCODEC_VERSION_MAJOR < 61
        avcp.channel_layout = channel_layout;
        avcp.channels = channels;
#else
        // Convert our stored channel_layout back to the new FFmpeg 7+ format
        if (channel_layout != 0) {
            av_channel_layout_from_mask(&avcp.ch_layout, channel_layout);
        } else {
            // If we don't have a valid channel layout, create a default one based on channel count
            av_channel_layout_default(&avcp.ch_layout, channels);
        }
        // The ch_layout.nb_channels will be set by av_channel_layout_from_mask or av_channel_layout_default
        // but we ensure it matches our stored channels value
        if (avcp.ch_layout.nb_channels != channels && channels > 0) {
            // If there's a mismatch, recreate the layout with the correct channel count
            av_channel_layout_uninit(&avcp.ch_layout);
            av_channel_layout_default(&avcp.ch_layout, channels);
        }
#endif
        avcp.sample_rate = sample_rate;
        avcp.block_align = block_align;
        avcp.frame_size = frame_size;
        avcp.initial_padding = initial_padding;
        avcp.trailing_padding = trailing_padding;
        avcp.seek_preroll = seek_preroll;

        avcp.extradata = reinterpret_cast<uint8_t*>(extradata.data());
        avcp.extradata_size = extradata.size();

        return avcp;
    }

    friend QDataStream &operator<<(QDataStream &out, const GoalAVCodecParameters &wrapper) {
        out << wrapper.codec_type;
        out << wrapper.codec_id;
        out << wrapper.codec_tag;
        out << wrapper.format;
        out << wrapper.bit_rate;
        out << wrapper.bits_per_coded_sample;
        out << wrapper.bits_per_raw_sample;
        out << wrapper.profile;
        out << wrapper.level;

        out << wrapper.width;
        out << wrapper.height;
        out << wrapper.sample_aspect_ratio_num;
        out << wrapper.sample_aspect_ratio_den;
        out << wrapper.field_order;

        out << wrapper.color_range;
        out << wrapper.color_primaries;
        out << wrapper.color_trc;
        out << wrapper.color_space;
        out << wrapper.chroma_location;

        out << wrapper.video_delay;
        out << wrapper.channel_layout;
        out << wrapper.channels;
        out << wrapper.sample_rate;
        out << wrapper.block_align;
        out << wrapper.frame_size;
        out << wrapper.initial_padding;
        out << wrapper.trailing_padding;
        out << wrapper.seek_preroll;

        out << wrapper.extradata; // QByteArray can be serialized directly

        return out;
    }

    friend QDataStream &operator>>(QDataStream &in, GoalAVCodecParameters &wrapper) {
        int codec_type;
        int codec_id;
        in >> codec_type;
        in >> codec_id;
        wrapper.codec_type = static_cast<AVMediaType>(codec_type);
        wrapper.codec_id = static_cast<AVCodecID>(codec_id);
        in >> wrapper.codec_tag;
        in >> wrapper.format;
        in >> wrapper.bit_rate;
        in >> wrapper.bits_per_coded_sample;
        in >> wrapper.bits_per_raw_sample;
        in >> wrapper.profile;
        in >> wrapper.level;

        in >> wrapper.width;
        in >> wrapper.height;
        in >> wrapper.sample_aspect_ratio_num;
        in >> wrapper.sample_aspect_ratio_den;
        int field_order;
        in >> field_order;
        wrapper.field_order = static_cast<AVFieldOrder>(field_order);

        int color_range;
        int color_primaries;
        int color_trc;
        int color_space;
        int chroma_location;
        in >> color_range;
        in >> color_primaries;
        in >> color_trc;
        in >> color_space;
        in >> chroma_location;
        wrapper.color_range = static_cast<AVColorRange>(color_range);
        wrapper.color_primaries = static_cast<AVColorPrimaries>(color_primaries);
        wrapper.color_trc = static_cast<AVColorTransferCharacteristic>(color_trc);
        wrapper.color_space = static_cast<AVColorSpace>(color_space);
        wrapper.chroma_location = static_cast<AVChromaLocation>(chroma_location);

        in >> wrapper.video_delay;
        in >> wrapper.channel_layout;
        in >> wrapper.channels;
        in >> wrapper.sample_rate;
        in >> wrapper.block_align;
        in >> wrapper.frame_size;
        in >> wrapper.initial_padding;
        in >> wrapper.trailing_padding;
        in >> wrapper.seek_preroll;

        in >> wrapper.extradata; // QByteArray can be deserialized directly

        return in;
    }


    AVMediaType codec_type{AVMediaType::AVMEDIA_TYPE_UNKNOWN};
    AVCodecID codec_id{AVCodecID::AV_CODEC_ID_FIRST_UNKNOWN};
    quint32 codec_tag{0};
    QByteArray extradata;
    qint32 format{0};
    qint64 bit_rate{0};
    qint32 bits_per_coded_sample{0};
    qint32 bits_per_raw_sample{0};
    qint32 profile{0};
    qint32 level{0};
    qint32 width{0};
    qint32 height{0};
    qint32 sample_aspect_ratio_num{0};
    qint32 sample_aspect_ratio_den{0};
    AVFieldOrder field_order{AVFieldOrder::AV_FIELD_PROGRESSIVE};
    AVColorRange color_range{AVColorRange::AVCOL_RANGE_MPEG};
    AVColorPrimaries color_primaries{AVColorPrimaries::AVCOL_PRI_BT709};
    AVColorTransferCharacteristic color_trc{AVColorTransferCharacteristic::AVCOL_TRC_BT709};
    AVColorSpace color_space{AVColorSpace::AVCOL_SPC_BT709};
    AVChromaLocation chroma_location{AVChromaLocation::AVCHROMA_LOC_TOPLEFT};
    qint32 video_delay{0};
    quint64 channel_layout{0};
    qint32 channels{0};
    qint32 sample_rate{0};
    qint32 block_align{0};
    qint32 frame_size{0};
    qint32 initial_padding{0};
    qint32 trailing_padding{0};
    qint32 seek_preroll{0};
};

struct MediaCommon {
    //common for both, no need to copy
    QString url;

    //reader only
    AVFormatContext* fmtctx{nullptr}; //reader only?
    QVector<AVStream*> avs; //reader only? ... R = r_frame_rate, Timebase = time_base (r_frame_rate * time_base = 1s, time_base*pts = second position of frame)
    QVector<qint64> highestPacketPTS; //reader only. We should introduce EoFpts variable for the only use that should be shared with Decoder
    AVProgram* avprogram{nullptr}; //reader only, WE NEED TO CREATE QMap for the dictionary inside!!!

    QVector<const AVCodec*> avc; //decoder only
    QVector<AVCodecContext*> avcc; //decoder only
    SwsContext *videoSWSContext{nullptr}; //decoder only
    SwrContext *audioSWRContext{nullptr}; //decoder only
    std::list<int> pts_buffer; //decoder only ... differences between frames in PTS, average is then computed and sent to reader
    QMap<QString,QString> avprogram_dict; //decoder only ... simpler representation of AVProgram

    //things from reader to decoder are sent only once
    double avg_fps{30}; //reader -> decoder BUT THEN WRITTEN TO ONLY BY DECODER
    QVector<qint64> firstPts; //reader -> decoder ... first PTS of file.
    qint32 videoStream = -1; //reader -> decoder
    qint32 binaryStream = -1; //reader -> decoder
    qint32 audioStream = -1; //reader -> decoder
    qint64 timebase_den; //reader -> decoder
    qint64 timebase_num; //reader -> decoder
    bool haveAudioOnly{false}; //reader -> decoder
    QVector<qint64> lastPts; // reader -> decoder at the beginning ... last played PTS, important for decisionmaking - if we should seek or not... will this work if we use it in reading only?
    double avg_pts{0}; //decoder -> reader, but reader makes initial guess based on probe
    qint32 max_gop{0}; //decoder -> reader, but reader makes initial guess based on probe
    QVector<qint64> referenceStartPts; //decoder only ... pts of first frame that was played in this session... IMPORTANT FOR BACKWARDS!, because of this we need some seek processing also in decoder!!
    QVector<GoalAVCodecParameters> avcp; //reader -> decoder

    //reader->decoder separately
    qint64 eofPTS{-1};



    QByteArray getReaderData() { //only data which are reader -> decoder
        QByteArray ba;
        QDataStream ds(&ba, QIODevice::WriteOnly);
        //AVDictionary to QMap
        if(avprogram && avprogram->metadata) {
            AVDictionaryEntry *t = NULL;
            while ((t = av_dict_get(avprogram->metadata, "", t, AV_DICT_IGNORE_SUFFIX))) {
                avprogram_dict.insert(QString(t->key), QString(t->value));
            }
        }

        //begin
        ds << avg_fps;
        ds << avg_pts;
        ds << max_gop;
        ds << firstPts;
        ds << videoStream;
        ds << binaryStream;
        ds << audioStream;
        ds << timebase_den;
        ds << timebase_num;
        ds << haveAudioOnly;
        ds << lastPts;
        ds << referenceStartPts;

        ds << avprogram_dict;
        ds << avcp;

        return ba;
    }
    void processReaderData(QByteArray ba) {
        QDataStream ds(ba);
        ds >> avg_fps;
        ds >> avg_pts;
        ds >> max_gop;
        ds >> firstPts;
        ds >> videoStream;
        ds >> binaryStream;
        ds >> audioStream;
        ds >> timebase_den;
        ds >> timebase_num;
        ds >> haveAudioOnly;
        ds >> lastPts;
        ds >> referenceStartPts;

        ds >> avprogram_dict;
        ds >> avcp;
    }

    QByteArray getDecoderData() {
        QByteArray ba;
        QDataStream ds(&ba, QIODevice::WriteOnly);

        ds << avg_pts;
        ds << max_gop;


        return ba;
    }
    void processDecoderData(QByteArray ba) {
        QDataStream ds(ba);

        ds >> avg_pts;
        ds >> max_gop;
    }

};

struct GoalFrameEncoded {
    /**
     * @brief timebase_* is a timebase of either pts/dts included within the GoalAVPacket, or timecode_rewrite
     */
    AVCodecID codecID{AV_CODEC_ID_NONE};
    int timebase_den{0};
    int timebase_num{0};
    /**
     * @brief if timecode_rewrite is set (especially because of ElapsedTimeReference timecode normalizer)
     *        it is an indication that the pts/dts should be rewritten according to that
     */
    int64_t timecode_rewrite{AV_NOPTS_VALUE};
    FrameMetadata fm;
    GoalAVPacket packet;
};

}
#endif // LIBAVPROTOCOLCOMMONS_H
