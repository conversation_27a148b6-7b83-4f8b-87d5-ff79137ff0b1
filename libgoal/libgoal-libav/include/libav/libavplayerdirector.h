#ifndef LIBAVPLAYERDIRECTOR_H
#define LIBAVPLAYERDIRECTOR_H

#include <QObject>
#include <QTimer>
#include "include/elapsedtimereference.h"
#include "include/libav/libavbuffer.h"
#include "libavhelpers.h"
#include "include/universalplayer.h"
#include "include/qthreadedobject.h"
#include "imageloader.h"
#include <QQueue>
#include <QImage>
///
/// \brief The libavPlayerDirector class abstracts two libavbuffers and allows fluent waitless switch from one video to another (if planned)
///
class libavPlayerDirector : public QThreadedObject
{
    Q_OBJECT
public:

    explicit libavPlayerDirector(int mediaID, std::function<void(VideoFrame)> consumer = nullptr, std::function<void (int, QByteArray)> consumerAudio = nullptr, bool backwardPlay = false);
    ~libavPlayerDirector();

    void addMedia(QString path, SecsNanosecs startTime, uint64_t seekOffsetNS = 0, bool isImage = false, bool startPaused = false); // these media NEED start realtime and have priority over enqueued ones
    //    void enqueueMedia(QString path, int64_t seekOffsetNS); //these media will start after current list is completed and will switch after true length of media...

    void preloadMedia(QString path, uint64_t seekOffsetNS = 0, bool isImage = false);
    void setPaused(SecsNanosecs startTime, bool paused = true); //
    void stop(); //destroy everything - clears any media and stops player

    void setPosponedSendAfterSwitch(bool postponed = true);

    void setColorCorrection(ColorCorrection cc, SecsNanosecs startTime);

    void setPlaybackSpeed(SecsNanosecs startTime, float speed = 1.0f, bool moveQueued = false);


    //settings
    void setNonCopy(bool noncopy = false); //If true, it will not copy data from AVFrame to our own VideoFrame structure, rather the AVFrame will be embedded into the VideoFrame
                                           //It is considerably faster, but can cause problems
    void setUseHWaccel(AVHWDeviceType type = AV_HWDEVICE_TYPE_NONE); //can enable HW acceleration with copy-back (VA, VDPAU, DX11...) not recommended until you know what are you doing
    void setSupportedFormats(QList<AVPixelFormat> supportedFormats = {AV_PIX_FMT_RGBA}); //If the decoded video is in one of these formats, it will just forward it.
                                                                                         //If not, it will be converted into the first specified format in the list
    void setWantAudio(bool wantAudio); //do we want the audio to be decoded and sent to our consumer?
    void setCustomCodec(AVCodecID c, std::string codecName); //you can specify custom decoder
    void setCustomCodecOption(std::string name, std::string value);
    void setMaxResolution(QSize res); //if invalid, not taken into consideration
    void setMediaID(int mediaID);
    void setMediaUUID(QUuid uuid);
    void setOverrideProbeFrames(int noOfFrames); //do not touch this unless you know what are you doing
    void setBlockForwardJumps(bool blockFWJumps);
    void setOptimizeLoad(bool optimizeLoad); // can cause problems with loading if the media is at the end and you seek to the beginning, use only when sure it works

    QUuid getMediaUUID();

    float getCurrentFPS() const;

signals:
    void playbackStarted();
    void playbackStopped();
    void probeFinished(QString filepath);
    void decodingFinished();
protected:
    //UniversalPlayer's thread
    void onPlayerSsinceEpochNS(SecsNanosecs sns_fromepoch, int64_t ns_increment, double fps);
protected slots:
    void priv_init() override;
    void priv_destroy() override;

private slots:
    //slot for buffer to inform us that we are going beyond the EOF of our active media
    void onAfterEOF();

    //determine actions
    void subDeterminePause(SecsNanosecs sns_fromepoch, SecsNanosecs &minTime);
    void subDetermineCorrections(SecsNanosecs sns_fromepoch, SecsNanosecs &minTime);
    void subDetermineMediaPlayback(SecsNanosecs sns_fromepoch, SecsNanosecs &minTime);
    void subDetermineSpeed(SecsNanosecs sns_fromepoch, SecsNanosecs &minTime);
    void determineAction(SecsNanosecs sns_fromepoch);


    //playback
    void onBufferProbeFinished(int buffno);

    //change recation
    void distributeChanges();


private:



    struct MediaPlaybackDefinition {
        SecsNanosecs startSNS_fromepoch{0,0};
        uint64_t seekOffsetNS{0}; // the file will be seeked to this offset first. Default is 0
        QString path{""};
        bool isImage{false};
        bool ignorePause{false}; //if true, its starttime will not be increased while the state of player is Paused.
                                 //this should be set to true if media has been added while the state is Paused together with unpause for the same starttime
                                 //in the end, the addMedia should override the pause at the right time
        bool isInSafePeriod{false}; //This states that the medium is in safe period

        bool operator==(const MediaPlaybackDefinition& lhs) const
        {
            return (startSNS_fromepoch == lhs.startSNS_fromepoch && seekOffsetNS == lhs.seekOffsetNS && path == lhs.path);
        }
    };


    struct CorrectionsDefinition {
        SecsNanosecs startSNS_fromepoch{0,0};
        ColorCorrection colorc;
    };

    struct PauseActionDefinition {
        SecsNanosecs startSNS_fromepoch{0,0};
        bool paused{false};
    };

    struct SpeedActionDefinition {
        SecsNanosecs startSNS_fromepoch{0,0};
        float playbackSpeed{1.0};
        bool moveQueuedMedia{false};
    };

    //helpers
    void seekInPrepareMediaAndPlay_priv(const MediaPlaybackDefinition &i);
    void playImmediately_priv(const MediaPlaybackDefinition &i, SecsNanosecs sns_fromepoch);
    void loadMediaToPrepareBuffer_priv(const MediaPlaybackDefinition &preloadMedia);

    void addMedia_priv(QString path, SecsNanosecs startTime, uint64_t seekOffsetNS = 0, bool isImage = false, bool startPaused = false);
    void preloadMedia_priv(QString path, uint64_t seekOffsetNS = 0, bool isImage = false);
    void setColorCorrection_priv(ColorCorrection cc, SecsNanosecs startTime);
    void addPauseAction_priv(SecsNanosecs startTime, bool paused = true);
    void setPlaybackSpeed_priv(SecsNanosecs startTime, float speed = 1, bool moveQueued = false);

    void loadImage(QString path);
    void imageLoaded(VideoFrame f);

    //medias
    std::list<MediaPlaybackDefinition> mediaList;

    //corrections
    std::list<CorrectionsDefinition> correctionsList;
    CorrectionsDefinition nextCorrection; //this one is locked
    SecsNanosecs nextCorrectionSwitch;
    CorrectionsDefinition currentCorrection_Priv;
    void incrementMediaStart(int64_t ns, MediaPlaybackDefinition &m);

    //pause events
    std::list<PauseActionDefinition> pauseList;
    SecsNanosecs lastPauseEvent;
    SecsNanosecs nextPauseEvent;
    bool lastPausePaused{false};
    bool nextPausePaused{false};

    //speed events
    std::list<SpeedActionDefinition> speedList;
    SpeedActionDefinition nextSpeed;
    SecsNanosecs nextSpeedSwitch;
    SpeedActionDefinition currentSpeed_Priv;


    //timeref
    ElapsedTimeReference tref;
    SecsNanosecs lastSecsNanosecs; //needed only to compute change with different speed
    QTimer checkNextActionTimer;
    int safePreloadNsecs {1000000000};


    //settings
    int mediaID;
    QUuid uuid;
    bool backwardPlay{false}; // default disabled - enable to set buffer limits and buffer hints when changing negative speed
    bool optimizeLoad{false}; //if true, the player will not load the media if it is already prepared in buffer


    //-----
    QMutex mediaMutex;
    QMutex corrMutex; //corrections Mutexs
    QMutex pauseMutex;
    QMutex speedMutex;

    //atomic progress
    std::atomic<int64_t> progressMediaActiveNS;

    //below this touched by foreign thread (UniversalPlayer)!
    //playingworkers
    QVector<libavBuffer*> buffers;
    QVector<int64_t> buffer_ns_seek;
    QVector<float> buffer_fps;
    UniversalPlayer player;

    MediaPlaybackDefinition mediaActive;
    MediaPlaybackDefinition mediaPrepare;
    //created for optimization - we do not need to lock the mutex for every frame. Changed only in foreign thread when signaled by flag
    MediaPlaybackDefinition mediaActive_Priv;

    QSize maxResolution{QSize()};

    int bufferActiveIndex{0};
    int bufferPrepareIndex{1};


    SecsNanosecs nextAction_sns_fromepoch{0,0};
    SecsNanosecs nextBufferSwitch_sns_fromepoch{0,0};

    ImageLoader imageLoader;


    enum Flags{
        BufferSwitchPlanned = 1, //All atomic values are set correctly, we can check if we need to set buffer
        NextActionPlanned = 1 << 1, //All atomic values are set correctly, we can check for next action determination
        ListEmpty = 1 << 2, //TBD: We have epmtied the list. This means that we can now prepare next media when switching buffers and switch buffers when we're at the EOF
        FPSChanged = 1 << 3, //fps has changed after probe on ACTIVE BUFFER!
        Paused = 1 << 4, //Here we know that we have currently paused this
        ChangeFlag = 1 << 5, //if raised, we need to update something - TBD - maybe not needed
        PostponedSendAfterSwitch = 1 << 6, //If raised, the next frame after switch will be postponed - thus keeps some time for the app to react to playbackStarted signal
        CorrectionsSwitchPlanned = 1 <<7, //Correction is planned, check out
        CatchingUpWaitingForLoad = 1 <<8, //We couldnt load the file in time, therefore we need to wait until the file is loaded to emit PlaybackStarted
        PauseActionPlanned = 1 << 9, //Pause or un-pause action is planned somewhere in the future
        Destroying = 1 << 10,       //the object is being destroyed block any signal that could have been timed from the outside
        BlockSendingToBuffer = 1 << 11, //Block sending requests to buffer - we want to raise it by default and after stop. set it to false after buffer exchange...
        SpeedActionPlanned = 1 << 12, //Speed change is planned, check it!
        ImageBeingLoaded = 1 << 13, //This means that image is being loaded
        ReplayFlag = 1 << 14, //Skip loading media if we have already loaded the current file
    };
    std::atomic<uint16_t> flagsAtomic{0};
    void setAtomicFlag(Flags f, bool t = true) {t ? flagsAtomic.fetch_or(f) : flagsAtomic.fetch_and(~f);}
    bool checkAtomicFlag(Flags f, uint16_t var) {return (f & var) == f;}

    //changed and touched only by the foreign thread
    libavBuffer *activeBuffer_priv{nullptr};

    //Image
    VideoFrame imageFrame;
    VideoFrame prepareFrame;
    bool imagePlayingNow = false;
    std::function<void (VideoFrame)> consumer;
    std::function<void (int, QByteArray)> consumerAudio;

};

#endif // LIBAVPLAYERDIRECTOR_H
