#include "libavencoder.h"

libavEncoder::libavEncoder() : QObject(nullptr)
{
    t = new QThread();
    this->moveToThread(t);
    t->start();
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, &libavEncoder::initClassPriv, Qt::BlockingQueuedConnection);
#else
    QMetaObject::invokeMethod(this, "initPriv", Qt::BlockingQueuedConnection);
#endif
}

libavEncoder::~libavEncoder()
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, &libavEncoder::destroyClassPriv, Qt::BlockingQueuedConnection);
#else
    QMetaObject::invokeMethod(this, "destroyClassPriv", Qt::QueuedConnection);
#endif
    t->quit();
    if(!t->wait(1000))
    {
        std::cerr << "[WARN]\tlibAV encoder thread didn't stop within 1000 milliseconds -> terminated." << std::endl;
        t->terminate();
        t->wait(1000);
    }
    delete t;
}

void libavEncoder::consumerFunction(VideoFrame f)
{
    if(!(q.enqueue(f))) {
        std::cerr << "[libgoal-libav encoder WARNING]\tCould not enqueue frame. Dropped." << " line " << __LINE__ << std::endl;
    }
    if(!(q.isEmpty()) && checkAtomicFlag(QueueFinished)) { //invoke only if the loop finished
        setAtomicFlag(QueueFinished, false);

#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
        QMetaObject::invokeMethod(this, std::bind(&libavEncoder::processLoop, this), Qt::QueuedConnection);
#else
        QMetaObject::invokeMethod(this, "processLoop", Qt::QueuedConnection);
#endif
    }
}

void libavEncoder::setFilePath(std::string path)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::priv_setFilePath, this, path), Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "priv_setFilePath", Qt::QueuedConnection, Q_ARG(std::string, path));
#endif
}

void libavEncoder::setFormatName(std::string format)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::priv_setFormatName, this, format), Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "priv_setFormatName", Qt::QueuedConnection, Q_ARG(std::string, format));
#endif
}

void libavEncoder::initializeEncoder()
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::initStreamsAndFile, this), Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "initStreamsAndFile", Qt::QueuedConnection);
#endif
}

void libavEncoder::finishAndDeinit(bool blocking)
{
//this is queued, so it will wait for all the frames in the queue to finish and then finishes the file

#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::finishStreamsAndFile, this), blocking ? Qt::BlockingQueuedConnection : Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "finishStreamsAndFile", blocking ? Qt::BlockingQueuedConnection : Qt::QueuedConnection);
#endif
}

void libavEncoder::stop()
{
    setAtomicFlag(StopFlag);
}

void libavEncoder::flush(bool blocking)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::priv_flushQueue, this), blocking ? Qt::BlockingQueuedConnection : Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "priv_flushQueue", blocking ? Qt::BlockingQueuedConnection : Qt::QueuedConnection);
#endif
}

void libavEncoder::setEncoderOutputSettingsFromFrame(const VideoFrame &f, bool forcePixelFMT)
{
    AVPixelFormat pixfmt;
    if(f.type() == VideoFrame::TypeAVFrameHW) {
        pixfmt = static_cast<AVPixelFormat>(f.avFrame()->format);
        forcePixelFMT = true;
    }
    else {
        pixfmt = libavHelpers::pixformatFromVideoframeToAV(f.type());
    }

    if(f.avFrame() && f.avFrame()->hw_frames_ctx) {
        hw_frame_ctx = av_buffer_ref(f.avFrame()->hw_frames_ctx);
    }

    setVideoOutputFormat(QSize(f.width(), f.height()),
                         forcePixelFMT ? pixfmt : videoOutputFormat
                         , f.standard(), f.range());
    setVideoCodecFPSHint(f.fps());
}

void libavEncoder::setVideoEnabled(bool video, bool metadata)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::priv_setVideoEnabled, this, video, metadata), Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "priv_setVideoEnabled", Qt::QueuedConnection, Q_ARG(bool, video), Q_ARG(bool, metadata));
#endif
}

void libavEncoder::setVideoOutputFormat(QSize resolution, AVPixelFormat pixfmt, VideoFrame::ColorStandard cs, VideoFrame::Range rng)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::priv_setVideoOutputFormat, this, resolution, pixfmt, cs, rng), Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "priv_setVideoOutputFormat", Qt::QueuedConnection, Q_ARG(QSize, resolution), Q_ARG(AVPixelFormat, pixfmt), Q_ARG(VideoFrame::ColorStandard, cs), Q_ARG(VideoFrame::Range, rng));
#endif
}

void libavEncoder::setVideoCodec(AVCodecID codec, std::string encoderName)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::priv_setVideoCodec, this, codec, encoderName), Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "priv_setVideoCodec", Qt::QueuedConnection, Q_ARG(AVCodecID, codec), Q_ARG(std::string, encoderName));
#endif
}

void libavEncoder::setVideoCodecGOP(int gop)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::priv_setVideoCodecGOP, this, gop), Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "priv_setVideoCodecGOP", Qt::QueuedConnection, Q_ARG(int, gop));
#endif
}

void libavEncoder::addVideoCodecCustomOption(std::pair<std::string, std::string> encoderOption)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::priv_addVideoCodecCustomOption, this, encoderOption), Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "priv_addVideoCodecCustomOption", Qt::QueuedConnection, QArgument<std::pair<std::string,std::string>>("std::pair<std::string,std::string>", encoderOption));
#endif
}

void libavEncoder::setVideoCodecFPSHint(double fps)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::priv_setVideoCodecFPSHint, this, fps), Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "priv_setVideoCodecFPSHint", Qt::QueuedConnection, Q_ARG(double, fps));
#endif
}

void libavEncoder::setAudioEnabled(bool enabled)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::priv_setAudioEnabled, this, enabled), Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "priv_setAudioEnabled", Qt::QueuedConnection, Q_ARG(bool, enabled));
#endif
}

void libavEncoder::setAudioOutputFormat(AVSampleFormat fmt, int64_t sample_rate, int64_t bitrate)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::priv_setAudioOutputFormat, this, fmt, sample_rate, bitrate), Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "priv_setAudioOutputFormat", Qt::QueuedConnection, Q_ARG(AVSampleFormat, fmt), Q_ARG(int64_t, sample_rate), Q_ARG(int64_t, bitrate));
#endif
}

void libavEncoder::setAudioCodec(AVCodecID codec, std::string encoderName)
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, std::bind(&libavEncoder::priv_setAudioCodec, this, codec, encoderName), Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "priv_setAudioCodec", Qt::QueuedConnection, Q_ARG(AVCodecID, codec), Q_ARG(std::string, encoderName));
#endif
}

void libavEncoder::priv_setVideoOutputFormat(QSize resolution, AVPixelFormat pixfmt, VideoFrame::ColorStandard cs, VideoFrame::Range rng)
{
    videoOutputResolution = resolution;
    videoOutputFormat = pixfmt;
    videoOutputColorStandard = cs;
    videoOutputRange = rng;
}

void libavEncoder::priv_setVideoCodec(AVCodecID codec, std::string encoderName)
{
    videoOutputCodec = codec; videoEncoderName = encoderName;
    if(videoEncoderName.find("qsv") != std::string::npos) {
        videoOutputFormat = AV_PIX_FMT_NV12; //QSV encoder can only do NV12
    }
    else if(videoEncoderName.find("vaapi") != std::string::npos) {
        videoOutputFormat = AV_PIX_FMT_VAAPI;
    }
    else if(videoEncoderName.find("nvenc") != std::string::npos) {
        videoOutputFormat = AV_PIX_FMT_YUV420P;
    }
}

void libavEncoder::priv_setAudioOutputFormat(AVSampleFormat fmt, int64_t sample_rate, int64_t bitrate)
{
    preferredAudioOutputSampleFormat = fmt;
    audioOutputSampleRate = sample_rate;
    audioOutputBitrate = bitrate;
}

void libavEncoder::priv_setAudioCodec(AVCodecID codec, std::string encoderName)
{
    audioOutputCodec = codec;
    audioEncoderName = encoderName;
}

void libavEncoder::initClassPriv()
{

    setAtomicFlag(Initialized, false);
    setAtomicFlag(StopFlag, true);
    setAtomicFlag(QueueFinished, true);
}

void libavEncoder::destroyClassPriv()
{
    stop();
    flush();
    finishAndDeinit();
    //now it should be safe to delete!
}

void libavEncoder::resetPriv()
{

}

void libavEncoder::processLoop()
{
    //TBD libswscale

    while (!(checkAtomicFlag(StopFlag) && checkAtomicFlag(Initialized))) {

        if (q.isEmpty()) {
            setAtomicFlag(QueueFinished, true);
            break;
        }

        VideoFrame f = q.dequeue();

        //PROCESS VIDEO --------------------------------------------------------------------------------------------------------
        if (f.type() != VideoFrame::TypeNULL && f.type() != VideoFrame::TypeAudioOnly && encode_video) {
            //we probably have some video so let's work with it!

            //get this so we can use it without converting multiple times
            if(f.type() == VideoFrame::TypeAVFrameHW) {
                //just do it
                if(!process_video_frame_avframe(f.avFrame())) {
                    continue;
                }
            }
            else {
                if (!process_video_frame_native(f)) { //copy data, take scale, have fun
                    continue;
                }
            }



            //The correct PTS!!!
            //So both the encoders have their own timebases, the container has it's own timebase and also VideoFrame has it's own!!
            //Let's make it tidy here...

            //Compute the timestamp for encoder
            int64_t encoderTimestamp = av_rescale(f.getTimecode(), video_st.enc->time_base.den, f.getTimeBase());
            if(firstVideoEncoderTimestamp < 0) {
                firstVideoEncoderTimestamp = encoderTimestamp;
            }

            encoderTimestamp = encoderTimestamp - firstVideoEncoderTimestamp;

            video_st.frame->pts = encoderTimestamp;

            //OK we have the frame. We need to feed it to encoder
            int64_t resulting_pts = 0, resulting_dts = 0;
            priv_write_frame(video_st.enc, video_st.st, video_st.frame, video_st.pkt, &resulting_pts, &resulting_dts); // I want the packet to have the same timing data as the video!!!
//            qDebug() << resulting_pts << "video";

            if(encode_metadata) {
                //include metadata --------------------------------------------------------------
                //write these manually
                metadata_st.pkt->pts = av_rescale(encoderTimestamp, video_st.st->time_base.den, video_st.enc->time_base.den);
                metadata_st.pkt->dts = metadata_st.pkt->pts;
                metadata_st.pkt->stream_index = metadata_st.st->index;
                QByteArray ar = QJsonDocument(f.metadata().toJson()).toJson();
                metadata_st.pkt->data = (uint8_t*)ar.data();
                metadata_st.pkt->size = ar.size();

                //qDebug() << "METADATA WRITTEN FOR PTS" << metadata_st.pkt->pts << "DTS" << metadata_st.pkt->dts << "Frame timestamp" << encoderTimestamp;

                ret = av_interleaved_write_frame(oc, metadata_st.pkt);
                av_packet_unref(metadata_st.pkt);

                if(ret < 0) {
                    av_strerror(ret, strerror_buffer, 512);
                    std::cerr << "[libgoal-libav encoder ERROR!]\tError while writing metadata packet!"
                                     << " Error: " << strerror_buffer <<  ". line:" << __LINE__ << std::endl;
                }
                // ------------------------------------------------------------------------------
            }


        }

        //PROCESS AUDIO---------------------------------------------------------------------------------
        if(!encode_audio) {
            continue;
        }
        const QAudioBuffer &ab = f.audioBuffer();

        if(ab.frameCount() <= 0) {
            continue;
        }

        QAudioFormat af = ab.format();
        AVSampleFormat infmt = libavHelpers::avSampleFmtFromQAudioFormat(af);
        //Important note! Samples in QAudioBuffer are samples for all channels! (stereo has 2*nb_channels)
        //but in libAV nb_samples is always per channel!
        //to get samples per one channel from QAudioBuffer we need frameCount()

        //first fill the arrays so we have a reference of incoming QAudioBuffer that libAV understands:
        av_samples_fill_arrays(audio_st.data_tmp, NULL, ab.constData<uint8_t>(), af.channelCount(), ab.frameCount(), infmt, 0);

        //prepare
        int ret = av_frame_make_writable(audio_st.tmp_frame);
        if (ret < 0) {
            av_strerror(ret, strerror_buffer, 512);
            std::cerr << "[libgoal-libav encoder ERROR!]\tAVFrame could not be maked to be writeable (audio)! Dropping frame!"
                         << " Error: " << strerror_buffer <<  ". line:" << __LINE__ << std::endl;
            continue;
        }

        //we also need the timestamp that corresponds with our timebase (sample rate)
        int64_t inputFrameTimestamp = f.getAudioTimecode();
        if(firstAudioTimestamp < 0) {
            firstAudioTimestamp = inputFrameTimestamp;
        }
        inputFrameTimestamp -= firstAudioTimestamp;

        if(infmt != audio_st.enc->sample_fmt || af.sampleRate() != audio_st.enc->sample_rate) {

#if LIBAVCODEC_VERSION_MAJOR >= 61
            // For FFmpeg 7+, use the new channel layout API
            AVChannelLayout src_ch_layout, dst_ch_layout;
            av_channel_layout_from_mask(&src_ch_layout, audioOutputChannelsLayout);
            av_channel_layout_from_mask(&dst_ch_layout, audioOutputChannelsLayout);

            swr_alloc_set_opts2(&audio_st.swr_ctx,
                               &dst_ch_layout, audio_st.enc->sample_fmt, audio_st.enc->sample_rate,
                               &src_ch_layout, infmt, af.sampleRate(),
                               0, NULL);

            av_channel_layout_uninit(&src_ch_layout);
            av_channel_layout_uninit(&dst_ch_layout);
#else
            // For FFmpeg 6 and earlier, use the old channel layout API
            audio_st.swr_ctx = swr_alloc_set_opts(audio_st.swr_ctx,
                                                  audioOutputChannelsLayout, audio_st.enc->sample_fmt, audio_st.enc->sample_rate,
                                                  audioOutputChannelsLayout, infmt, af.sampleRate(),
                                                  0, NULL);
#endif

            if(!swr_is_initialized(audio_st.swr_ctx)) {
                swr_init(audio_st.swr_ctx);
            }


            ret = swr_convert(audio_st.swr_ctx,
                                      audio_st.tmp_frame->data, audio_st.enc->sample_rate,
                                      (const uint8_t **)audio_st.data_tmp, ab.frameCount());

            if(ret < 0) {
                av_strerror(ret, strerror_buffer, 512);
                std::cerr << "[libgoal-libav encoder ERROR!]\tError when converting audio: "
                             << " Error: " << strerror_buffer <<  ". line:" << __LINE__ << std::endl;
                continue;
            }

            audio_st.tmp_frame->nb_samples = ret;
        }
        else {
            //otherwise just copy :)
            av_samples_copy(audio_st.tmp_frame->data, audio_st.data_tmp, 0, 0, ab.frameCount(), af.channelCount(), infmt);
            audio_st.tmp_frame->nb_samples = ab.frameCount();
        }

        //timestamp is computed the same in both cases:
        audio_st.tmp_frame->pts = av_rescale(inputFrameTimestamp, audio_st.enc->time_base.den, f.getAudioTimeBase());
        priv_write_audio_automatic(); //we have to piece the frame correctly, this is done here
    }

}

void libavEncoder::priv_flushQueue()
{
    //all of these are stack objects, we can remove whenever we wish so!
    while(!q.isEmpty()) {
        q.tryDequeue(); //remove everything
    }
}

void libavEncoder::priv_convert_video_frame(const VideoFrame &f, AVPixelFormat framePixfmt, libavHelpers::ColorCharacteristicsValues &vals)
{
    video_st.sws_ctx = sws_getCachedContext(video_st.sws_ctx, f.width(), f.height(), framePixfmt,
                                            video_st.enc->width, video_st.enc->height, video_st.enc->pix_fmt,
                                            0, nullptr, nullptr, nullptr);

    //If we need to set the advanced colorspace details...
    if(vals.colorrange_av != video_st.enc->color_range || vals.colorspace_av != video_st.enc->colorspace) {

        int ret = sws_setColorspaceDetails(video_st.sws_ctx, sws_getCoefficients(vals.colorspace_sws),
                                           vals.colorrange_sws,
                                           sws_getCoefficients(video_st.enc->colorspace),
                                           video_st.enc->color_range == 1 ? 0 : 1,
                                           0, FIXED_1_0, FIXED_1_0);
    }

    if(ret < 0) {
        std::cerr << "[libgoal-libav encoder WARNING]\tThe requested colorspace transformation can not be done!" << " line " << __LINE__ << std::endl;
    }

    //convert!
    sws_scale(video_st.sws_ctx, video_st.data_tmp, video_st.linesizes_tmp, 0, f.height(), video_st.frame->data, video_st.frame->linesize);
}

bool libavEncoder::priv_write_frame(AVCodecContext *c, AVStream *st, AVFrame *frame, AVPacket *pkt, int64_t *pts, int64_t *dts)
{
    int ret = avcodec_send_frame(c, frame);
    if(ret < 0) {
        av_strerror(ret, strerror_buffer, 512);
        std::cerr << "[libgoal-libav encoder ERROR!]\tEncoder could not receive the frame! Dropping!"
                     << " Error: " << strerror_buffer <<  ". line:" << __LINE__ << std::endl;
    }
    while (ret >= 0) {
        ret = avcodec_receive_packet(c, pkt);
        if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
            break;
        }
        else if (ret < 0) {
            av_strerror(ret, strerror_buffer, 512);
            std::cerr << "[libgoal-libav encoder ERROR!]\tThe packet could not be retrieved!"
                         << " Error: " << strerror_buffer <<  ". line:" << __LINE__ << std::endl;
            return false;
        }

        //now rescale again! compute the timestamp for the container (stream)
        av_packet_rescale_ts(pkt, c->time_base, st->time_base);
        //qDebug() << c->time_base.den << st->time_base.den << pkt->pts << frame->pts;
        pkt->stream_index = st->index;

        //yet before we unref the packet:
        if(pts) {
            *pts = pkt->pts;
        }
        if(dts) {
            *dts = pkt->dts;
        }

        ret = av_interleaved_write_frame(oc, pkt);


        av_packet_unref(pkt);


        if(ret < 0) {
            av_strerror(ret, strerror_buffer, 512);
            std::cerr << "[libgoal-libav encoder ERROR!]\tError while writing packet!"
                         << " Error: " << strerror_buffer <<  ". line:" << __LINE__ << std::endl;
        }
    }

    return true;
}

void libavEncoder::priv_write_audio_automatic()
{
    //Ok this is mindfuck as fuck
    //So right now, our tmp_frame has converted data (same size as input frame) And we basically want to cut it or buffer it so that the output
    //frames have all the correct size.... IDK why this has not been done automatically in libav.

    //So we start with a converted frame in audio_st.tmp_frame of any size. Always when we get here we should have a fresh one so:
    audio_st.input_tmp_frame_position_samples = 0;

    //let's begin

    while (audio_st.input_tmp_frame_position_samples < audio_st.tmp_frame->nb_samples) {

        //now we have two possibilities: Either we start with a fresh AVFrame or we have left some data before
        if(audio_st.output_frame_position_samples == 0) { //if we are working on a new frame, we make it writable
            int ret = av_frame_make_writable(audio_st.frame);
            if (ret < 0) {
                av_strerror(ret, strerror_buffer, 512);
                std::cerr << "[libgoal-libav encoder ERROR!]\tAVFrame could not be maked to be writeable (audio)! Dropping frame!"
                             << " Error: " << strerror_buffer <<  ". line:" << __LINE__ << std::endl;
                continue;
            }
        }

        // now we can copy
        //how much?
        int nb_samples = audio_st.enc->frame_size - audio_st.output_frame_position_samples; //base size based on position in current output Frame
        //but maybe we do not have enough
        int samples_left = audio_st.tmp_frame->nb_samples - audio_st.input_tmp_frame_position_samples;
        if(samples_left < nb_samples) {
            nb_samples = samples_left;
        }

        av_samples_copy(audio_st.frame->data, audio_st.tmp_frame->data,
                        audio_st.output_frame_position_samples, //dst offset
                        audio_st.input_tmp_frame_position_samples, //src offset
                        audio_st.enc->frame_size - audio_st.output_frame_position_samples, //how much
#if LIBAVCODEC_VERSION_MAJOR >= 61
                        audio_st.enc->ch_layout.nb_channels, audio_st.enc->sample_fmt);
#else
                        audio_st.enc->channels, audio_st.enc->sample_fmt);
#endif

        //update pts (remember, in audio this is always based on the samplerate!)
        //so we need the start pts + input position but also - output position
        audio_st.frame->pts = audio_st.tmp_frame->pts + audio_st.input_tmp_frame_position_samples - audio_st.output_frame_position_samples;


        //Where are we in tmp_frame:
        audio_st.input_tmp_frame_position_samples += nb_samples; //this ends the loop if we use whole tmp_frame

        //Where are we in frame:
        audio_st.output_frame_position_samples += nb_samples;
        if(audio_st.output_frame_position_samples >= audio_st.enc->frame_size) {
            audio_st.output_frame_position_samples = 0;
            //only write the wrame if it is fully completed
            priv_write_frame(audio_st.enc, audio_st.st, audio_st.frame, audio_st.pkt);
        }

    }

}

bool libavEncoder::process_video_frame_native(VideoFrame &f)
{
    AVPixelFormat framePixfmt = libavHelpers::pixformatFromVideoframeToAV(f.type());
    libavHelpers::ColorCharacteristicsValues vals = libavHelpers::values(f.standard(), f.range());

    //Because the encoder may reference the AVFrame's buffers internally, we need to make sure that the buffers are writable
    int ret = av_frame_make_writable(video_st.frame);
    if (ret < 0) {
        av_strerror(ret, strerror_buffer, 512);
        std::cerr << "[libgoal-libav encoder ERROR!]\tAVFrame could not be maked to be writeable! Dropping frame!"
                     << " Error: " << strerror_buffer <<  ". line:" << __LINE__ << std::endl;
        return false;
    }

    //Ok, we can write to the frame. So let's map our frame to the pointers libav understands:
    av_image_fill_arrays(video_st.data_tmp, video_st.linesizes_tmp, f.data(), framePixfmt, f.width(), f.height(), 1);
    if(f.getPlanesCount() > 1) {
        for(int i = 0; i < f.getPlanesCount(); ++i) {
            video_st.data_tmp[i] = f.planeData(i);
        }
    }

    //now check if our frame is in the expected format (probably not most of the time)
    if(framePixfmt != video_st.enc->pix_fmt || f.width() != video_st.enc->width || f.height() != video_st.enc->height
            || vals.colorrange_av != video_st.enc->color_range || vals.colorspace_av != video_st.enc->colorspace) {

        //It is different, so we need to convert it. SWS will also write the data to the buffer.
        priv_convert_video_frame(f, framePixfmt, vals);
    }
        //If it is the same, let's just copy the data directly
    else {
//                av_image_copy(video_st.frame->data, video_st.frame->linesize, (const uint8_t **)(video_st.data_tmp), video_st.linesizes_tmp, video_st.enc->pix_fmt,
//                              video_st.enc->width, video_st.enc->height);

        av_image_fill_arrays(video_st.frame->data, video_st.frame->linesize, f.data(), framePixfmt, f.width(), f.height(), 1);
        if(f.getPlanesCount() > 1) {
            for(int i = 0; i < f.getPlanesCount(); ++i) {
                video_st.frame->data[i] = f.planeData(i);
            }
        }
    }

    return true;
}

bool libavEncoder::process_video_frame_avframe(AVFrame *f)
{
    video_st.frame = f;
    return true;
}


void libavEncoder::initStreamsAndFile()
{

    //first of all check if we are initialized already and then deinit:
    if(checkAtomicFlag(Initialized)) {
        finishStreamsAndFile();
    }

    //First check if we have filename filled. We can't do much without that
    if(filename.empty()) {
        std::cerr << "[libgoal-libav encoder ERROR]\tFilename is empty, line " << __LINE__ << std::endl;
        return;
    }
    //Now allocate the context
    avformat_alloc_output_context2(&oc, NULL, formatname.empty() ? NULL : formatname.c_str(), filename.c_str()); //this should select the correct muxer according to thefilename extension
    //but if not...
    if(!oc) {
        std::cerr << "[libgoal-libav encoder ERROR]\tCould not determine the format (muxer) from provided " << (formatname.empty() ? "filename" : "format name") << std::endl;
        return;
    }

    fmt = oc->oformat;

    //If the format is not TS, we can not add metadata!
    if(strcmp(oc->oformat->name, "mpegts") != 0) {
        videoMetadataEnabled = false;
    }

    //now we can add the streams. First the video:
    if(videoEnabled) { //only if video is allowed
         if(!priv_add_stream(&video_st, oc, &video_codec, videoOutputCodec, videoEncoderName)) {
             return;
         }
    }

    if(audioEnabled) {
        if(!priv_add_stream(&audio_st, oc, &audio_codec, audioOutputCodec, audioEncoderName)) {
            return;
        }
    }

    if(subtitlesEnabled) {
        if(!priv_add_stream(&subtitle_st, oc, &subtitle_codec, AV_CODEC_ID_DVB_SUBTITLE, "")) {
            return;
        }
    }

    if(videoMetadataEnabled) {
        if(!priv_add_stream(&metadata_st, oc, &metadata_codec, AV_CODEC_ID_BIN_DATA, "")) {
            return;
        }
    }


    //now open codecs

    if(videoEnabled) {
        for (const std::pair<std::string, std::string> &opt : videoEncoderCustomOptions) {
            int ret = av_opt_set(video_st.enc, opt.first.c_str(), opt.second.c_str(), AV_OPT_SEARCH_CHILDREN);
            if(ret != 0) {
                av_strerror(ret, strerror_buffer, 512);
                std::cerr << "[libgoal-libav encoder WARNING]\tCould not set a custom option " << opt.first << " with value " << opt.second
                          << ". Error: " << strerror_buffer <<  ". line:" << __LINE__ << std::endl;
            }
        }
        if (priv_open_video(oc, video_codec, &video_st, NULL)) {
            encode_video = true;
        }
    }

    if(audioEnabled) {
        if(priv_open_audio(oc, audio_codec, &audio_st, NULL)) {
            encode_audio = true;
        }
    }

    if(subtitlesEnabled) {
        if(priv_open_subtitles(oc, subtitle_codec, &subtitle_st, NULL)) {
            encode_subtitles = true;
        }
    }

    if(videoMetadataEnabled) {
        if(priv_open_metadata(&metadata_st)) {
            encode_metadata = true;
        }
    }

    std::cout << "[libgoal-libav encoder INFO]\tOUTPUT FORMAT INFORMATION FROM FFMPEG FOLLOWS:" << std::endl;
    av_dump_format(oc, 0, filename.c_str(), 1); //this prints out detailed information about the output format
    std::cout << "-END" << std::endl;

    audio_st.output_frame_position_samples = 0; //zero the number of samples in output, forcing the rewrite


    //Open the output file (if needed?)

    if (!(fmt->flags & AVFMT_NOFILE)) {
            ret = avio_open(&oc->pb, filename.c_str(), AVIO_FLAG_WRITE);
            if (ret < 0) {
                av_strerror(ret, strerror_buffer, 512);
                std::cerr << "[libgoal-libav encoder ERROR]\tCould not open the file "
                          << ". Error: " << strerror_buffer <<  ". line:" << __LINE__ << std::endl;
                return;
            }
        }



    ret = avformat_write_header(oc, &opt);
    if(ret < 0) {
        std::cerr << "[libgoal-libav encoder ERROR]\tCould not open the output file." << __LINE__ << std::endl;
        return;
    }

    setAtomicFlag(Initialized, true);
    setAtomicFlag(StopFlag, false);
    setAtomicFlag(QueueFinished, true);

    firstVideoEncoderTimestamp = -1;
    firstAudioTimestamp = -1;

    if(!q.isEmpty()) {
        setAtomicFlag(QueueFinished, false);
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
        QMetaObject::invokeMethod(this, std::bind(&libavEncoder::processLoop, this), Qt::QueuedConnection);
#else
        QMetaObject::invokeMethod(this, "processLoop", Qt::QueuedConnection);
#endif
    }

}

void libavEncoder::finishStreamsAndFile()
{
    if(!checkAtomicFlag(Initialized)) {
        return;
    }

    setAtomicFlag(Initialized, false);
    setAtomicFlag(StopFlag, true);
    setAtomicFlag(QueueFinished, true);

    av_write_trailer(oc);

    if(encode_video) {
        priv_close_stream(oc, &video_st);
    }

    if(encode_audio) {
        priv_close_stream(oc, &audio_st);
    }
    if(encode_metadata) {
        priv_close_stream(oc, &metadata_st);
    }

    if(!(fmt->flags & AVFMT_NOFILE)) {
        avio_closep(&oc->pb);
    }

    avformat_free_context(oc);
}


bool libavEncoder::priv_add_stream(libavEncoder::OutputStream *ost, AVFormatContext *oc, const AVCodec **codec, AVCodecID codec_id, std::string codec_name)
{
    AVCodecContext *c;
    int i;

    ost->st = avformat_new_stream(oc, NULL);
    if (!ost->st) {
        std::cerr << "[libgoal-libav encoder ERROR]\tCould not create stream, line " << __LINE__ << std::endl;
        return false;
    }

    if(codec_id != AV_CODEC_ID_BIN_DATA) {

        //find encoder - the specified one or default to codec_id if not specified
        *codec = codec_name.empty() ? avcodec_find_encoder(codec_id) : avcodec_find_encoder_by_name(codec_name.c_str());
        if(!(*codec)) {
            std::cerr << "[libgoal-libav encoder ERROR]\tCould not create encoder " << (codec_name.empty() ? avcodec_get_name(codec_id) : codec_name) <<  ", line:" << __LINE__ << std::endl;
            return false;
        }

        //allocate encoder context
        ost->st->id = oc->nb_streams-1;
        c = avcodec_alloc_context3(*codec);
        if(!c) {
            std::cerr << "[libgoal-libav encoder ERROR]\tCould not create AVCodecContext, line " << __LINE__ << std::endl;
            return false;
        }

        ost->enc = c;
    }
    else { //in case of our tags just set the AVStream manually
        ost->st->codecpar->codec_type = AVMEDIA_TYPE_DATA;
        ost->st->codecpar->codec_id = AV_CODEC_ID_BIN_DATA;
        return true;
    }

    //VIDEO/AUDIO?
    switch ((*codec)->type) {

    case AVMEDIA_TYPE_AUDIO: {
        c->sample_fmt = AV_SAMPLE_FMT_FLTP;
        c->bit_rate = audioOutputBitrate;
        c->sample_rate = audioOutputSampleRate;

        /* select other audio parameters supported by the encoder */
        if(audioOutputChannelsLayout == 0) {
            avcodec_free_context(&c);
            return false;
        }

#if LIBAVCODEC_VERSION_MAJOR >= 61
        // For FFmpeg 7+, use the new avcodec_get_supported_config API
        av_channel_layout_from_mask(&c->ch_layout, AV_CH_LAYOUT_STEREO);

        // Check and set supported sample formats
        const AVCodecConfig *config = NULL;
        while ((config = avcodec_get_supported_config(*codec, config, AV_CODEC_CONFIG_SAMPLE_FORMAT))) {
            const enum AVSampleFormat *sample_fmts = (const enum AVSampleFormat *)config->u.sample_fmt;
            if (sample_fmts) {
                c->sample_fmt = sample_fmts[0]; // Use first supported format as default
                // Try to find our preferred format
                for (int i = 0; sample_fmts[i] != AV_SAMPLE_FMT_NONE; i++) {
                    if (sample_fmts[i] == preferredAudioOutputSampleFormat) {
                        c->sample_fmt = preferredAudioOutputSampleFormat;
                        break;
                    }
                }
                break;
            }
        }

        // Check and set supported sample rates
        config = NULL;
        while ((config = avcodec_get_supported_config(*codec, config, AV_CODEC_CONFIG_SAMPLE_RATE))) {
            const int *sample_rates = (const int *)config->u.sample_rate;
            if (sample_rates) {
                c->sample_rate = sample_rates[0]; // Use first supported rate as default
                // Try to find our preferred rate
                for (int i = 0; sample_rates[i] != 0; i++) {
                    if (sample_rates[i] == audioOutputSampleRate) {
                        c->sample_rate = audioOutputSampleRate;
                        break;
                    }
                }
                break;
            }
        }
#else
        // For FFmpeg 6 and earlier, use the old API
        c->channel_layout = AV_CH_LAYOUT_STEREO;
        c->channels = av_get_channel_layout_nb_channels(c->channel_layout);

        if((*codec)->sample_fmts) {
            c->sample_fmt = (*codec)->sample_fmts[0];
            int i = 0;
            int a = (*codec)->sample_fmts[i];
            while(a > 0) {
                if(a == preferredAudioOutputSampleFormat) {
                    c->sample_fmt = preferredAudioOutputSampleFormat;
                    break;
                }
                ++i;
                a = (*codec)->sample_fmts[i];
            }
        }

        if ((*codec)->supported_samplerates) {
            c->sample_rate = (*codec)->supported_samplerates[0];
            for(i = 0; (*codec)->supported_samplerates[i]; ++i) {
                if((*codec)->supported_samplerates[i] == audioOutputSampleRate) {
                    c->sample_rate = audioOutputSampleRate;
                    break;
                }
            }
        }
#endif

        if(!(c->sample_rate)) {
            std::cerr << "[libgoal-libav encoder ERROR]\tAudio codec does not support selected sample rate and no sample rate can be set " << ", line:" << __LINE__ << std::endl;
            avcodec_free_context(&c);
            return false;
        }

        c->time_base = av_make_q(1, c->sample_rate);
        ost->st->time_base = c->time_base;
    }
        break;
    case AVMEDIA_TYPE_VIDEO: {
        //We won't use bitrates as default! we will use crf in options
        c->width = videoOutputResolution.width();
        c->height = videoOutputResolution.height();


        //this should not be needed, but for example nvenc_h264 encoder will default to 29.97fps all the time...
        if(videoFPS > 0) {
            c->framerate = av_d2q(videoFPS, MAX_NUMERATOR_DENOMINATOR);;
        }

        ost->st->time_base = av_make_q(1, timeBaseDenom);/*av_d2q(videoFPS, MAX_NUMERATOR_DENOMINATOR);*/
        c->time_base       = ost->st->time_base;

        c->gop_size = videoGOP;

        c->pix_fmt = videoOutputFormat;

        if(hw_frame_ctx) {
            c->hw_frames_ctx = av_buffer_ref(hw_frame_ctx);
        }

        libavHelpers::ColorCharacteristicsValues colorVals = libavHelpers::values(videoOutputColorStandard, videoOutputRange);
        c->colorspace = colorVals.colorspace_av;
        c->color_primaries = colorVals.colorprimaries_av;
        c->color_range = colorVals.colorrange_av;
        c->color_trc = colorVals.colortrc_av;

    }
        break;

    case AVMEDIA_TYPE_SUBTITLE:
        ost->st->time_base = av_make_q(1, timeBaseDenom );
        c->time_base = ost->st->time_base;
        break;

    default:
        break;
    }

    return true;
}

bool libavEncoder::priv_open_video(AVFormatContext *oc, const AVCodec *codec, libavEncoder::OutputStream *ost, AVDictionary *opt_arg)
{
    int ret;
    AVCodecContext *c = ost->enc;
    AVDictionary *opt = NULL;

    av_dict_copy(&opt, opt_arg, 0);

    ret = avcodec_open2(c, codec, &opt);
    av_dict_free(&opt);
    if (ret < 0) {
        std::cerr << "[libgoal-libav encoder ERROR]\tCould not open the video codec " << ret << ". line:" << __LINE__ << std::endl;
        return false;
    }

    //allocate frames
    if(c->pix_fmt != AV_PIX_FMT_VAAPI && c->pix_fmt != AV_PIX_FMT_QSV && c->pix_fmt!= AV_PIX_FMT_CUDA && c->pix_fmt != AV_PIX_FMT_VDPAU) {
        ost->frame = priv_alloc_picture(c->pix_fmt, c->width, c->height);
        if (!ost->frame) {
            std::cerr << "[libgoal-libav encoder ERROR]\tCould not allocate the video frame" << ". line:" << __LINE__ << std::endl;
            return false;
            }
    }

    ost->pkt = av_packet_alloc();
    if(!ost->pkt) {
        std::cerr << "[libgoal-libav encoder ERROR]\tCould not allocate the video packet" << ". line:" << __LINE__ << std::endl;
        return false;
    }

    //The tmp_frame as in example is pointless to use, we do not have the whole structure allocated, we only change the correct pointers on the fly
    //in the big loop


    /* copy the stream parameters to the muxer */
        ret = avcodec_parameters_from_context(ost->st->codecpar, c);
        if (ret < 0) {
            std::cerr << "[libgoal-libav encoder ERROR]\tCould not copy the parameters from context" << ". line:" << __LINE__ << std::endl;
            return false;

        }

    return true;
}

bool libavEncoder::priv_open_audio(AVFormatContext *oc, const AVCodec *codec, libavEncoder::OutputStream *ost, AVDictionary *opt_arg)
{
    AVCodecContext *c;
        int nb_samples;
        int ret;
        AVDictionary *opt = NULL;

        c = ost->enc;

        /* open it */
        av_dict_copy(&opt, opt_arg, 0);
        ret = avcodec_open2(c, codec, &opt);
        av_dict_free(&opt);
        if (ret < 0) {
            std::cerr << "[libgoal-libav encoder ERROR]\tCould not open the audio codec" << ". line:" << __LINE__ << std::endl;
            return false;
        }

        //Alloc audio frame
        if (c->codec->capabilities & AV_CODEC_CAP_VARIABLE_FRAME_SIZE) {
            nb_samples = c->sample_rate; //let's allocate whole second, not 10000 as per example. Because why not? We can send whole frame in this size
            std::cout << "[libgoal-libav encoder INFO]\tSelected audio codec supports variable frame size" << ". line:" << __LINE__ << std::endl;
        }
        else {
            nb_samples = c->frame_size;
        }

#if LIBAVCODEC_VERSION_MAJOR >= 61
        // For FFmpeg 7+, use the new channel layout API
        uint64_t channel_layout_mask = 0;
        if (c->ch_layout.order == AV_CHANNEL_ORDER_NATIVE) {
            channel_layout_mask = c->ch_layout.u.mask;
        } else {
            channel_layout_mask = AV_CH_LAYOUT_STEREO; // fallback
        }
        ost->frame     = priv_alloc_audio_frame(c->sample_fmt, channel_layout_mask,
                                                c->sample_rate, nb_samples);

        ost->tmp_frame = priv_alloc_audio_frame(c->sample_fmt, channel_layout_mask,
                                                    c->sample_rate,c->sample_rate);
#else
        // For FFmpeg 6 and earlier, use the old channel layout API
        ost->frame     = priv_alloc_audio_frame(c->sample_fmt, c->channel_layout,
                                                c->sample_rate, nb_samples);

        ost->tmp_frame = priv_alloc_audio_frame(c->sample_fmt, c->channel_layout,
                                                    c->sample_rate,c->sample_rate);
#endif

        ost->pkt = av_packet_alloc();
        if(!ost->pkt) {
            std::cerr << "[libgoal-libav encoder ERROR]\tCould not allocate the audio packet" << ". line:" << __LINE__ << std::endl;
            return false;
        }

        /* copy the stream parameters to the muxer */
           ret = avcodec_parameters_from_context(ost->st->codecpar, c);
           if (ret < 0) {
               std::cerr << "[libgoal-libav encoder ERROR]\tCould not copy the parameters from context" << ". line:" << __LINE__ << std::endl;
               return false;
           }


           return true;
}

bool libavEncoder::priv_open_subtitles(AVFormatContext *oc, const AVCodec *codec, libavEncoder::OutputStream *ost, AVDictionary *opt_arg)
{
    int ret;
    AVCodecContext *c = ost->enc;
    AVDictionary *opt = NULL;

    av_dict_copy(&opt, opt_arg, 0);

    ret = avcodec_open2(c, codec, &opt);
    av_dict_free(&opt);
    if (ret < 0) {
        av_strerror(ret, strerror_buffer, 512);
        std::cerr << "[libgoal-libav encoder ERROR]\tCould not open the subtitle codec. Err: " << strerror_buffer << ". line:" << __LINE__ << std::endl;
        return false;
    }

    //allocate subs


    ost->pkt = av_packet_alloc();
    if(!ost->pkt) {
        std::cerr << "[libgoal-libav encoder ERROR]\tCould not allocate the subtitle packet" << ". line:" << __LINE__ << std::endl;
        return false;
    }


    /* copy the stream parameters to the muxer */
        ret = avcodec_parameters_from_context(ost->st->codecpar, c);
        if (ret < 0) {
            std::cerr << "[libgoal-libav encoder ERROR]\tCould not copy the parameters from context" << ". line:" << __LINE__ << std::endl;
            return false;

        }

    return true;
}

bool libavEncoder::priv_open_metadata(libavEncoder::OutputStream *ost)
{
    ost->pkt = av_packet_alloc();
    if(!ost->pkt) {
        std::cerr << "[libgoal-libav encoder ERROR]\tCould not allocate the metadata packet" << ". line:" << __LINE__ << std::endl;
        return false;
    }

    //The tmp_frame as in example is pointless to use, we do not have the whole structure allocated, we only change the correct pointers on the fly
    //in the big loop

    return true;
}


void libavEncoder::priv_close_stream(AVFormatContext *oc, libavEncoder::OutputStream *ost)
{
    if(ost->enc) {
        avcodec_free_context(&ost->enc);
    }

    if(ost->frame) {
        av_frame_free(&ost->frame);
        ost->frame = NULL;
    }

    if(ost->tmp_frame) {
        av_frame_free(&ost->tmp_frame);
        ost->tmp_frame = NULL;
    }

    av_packet_free(&ost->pkt); //NULL is no-op, no need to check
    ost->pkt = NULL;

    if(ost->swr_ctx) {
        swr_free(&ost->swr_ctx);
        ost->swr_ctx = NULL;
    }

    if(ost->sws_ctx) {
        sws_freeContext(ost->sws_ctx);
        ost->sws_ctx = NULL;
    }
}

AVFrame *libavEncoder::priv_alloc_picture(AVPixelFormat pix_fmt, int width, int height)
{
    AVFrame *picture;
    int ret;

    picture = av_frame_alloc();
    if(!picture) {
        std::cerr << "[libgoal-libav encoder ERROR]\tThe AVFrame could not be allocated" <<  ". line:" << __LINE__ << std::endl;
        return NULL;
    }

    picture->format = pix_fmt;
    picture->width = width;
    picture->height = height;

    ret = av_frame_get_buffer(picture, 0);
    if(ret < 0) {
        std::cerr << "[libgoal-libav encoder ERROR]\tThe AVFrame is allocated but buffer can not be obtained" <<  ". line:" << __LINE__ << std::endl;
        return NULL;
    }

    return picture;
}

AVFrame *libavEncoder::priv_alloc_audio_frame(AVSampleFormat sample_fmt, uint64_t channel_layout, int sample_rate, int nb_samples)
{
    AVFrame *frame = av_frame_alloc();
        int ret;

        if (!frame) {
            fprintf(stderr, "Error allocating an audio frame\n");
            exit(1);
        }

        frame->format = sample_fmt;
#if LIBAVCODEC_VERSION_MAJOR >= 61
        // For FFmpeg 7+, use the new channel layout API
        av_channel_layout_from_mask(&frame->ch_layout, channel_layout);
#else
        // For FFmpeg 6 and earlier, use the old channel layout API
        frame->channel_layout = channel_layout;
#endif
        frame->sample_rate = sample_rate;
        frame->nb_samples = nb_samples;

        if(nb_samples) {
            ret = av_frame_get_buffer(frame, 0);
            if (ret < 0) {
                std::cerr << "[libgoal-libav encoder ERROR]\tThe AVFrame for audio could not be allocated" <<  ". line:" << __LINE__ << std::endl;
                return NULL;
            }
        }
        return frame;
}

AVFrame *libavEncoder::priv_get_cached_frame(AVFrame *fr, AVPixelFormat pix_fmt, int width, int height)
{
    if(fr) {
        if(fr->format == pix_fmt && fr->width == width && fr->height == height) {
            return fr;
        }

        //if this is not the one we are looking for, then let's free it
        av_frame_free(&fr);
    }

    return priv_alloc_picture(pix_fmt, width, height);

}
