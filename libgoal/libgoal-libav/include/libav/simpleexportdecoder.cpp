#include "simpleexportdecoder.h"
#include "QtCore/qdatetime.h"

SimpleExportDecoder::SimpleExportDecoder() : QObject{nullptr}
{
    t = new QThread();
    this->moveToThread(t);
    t->start();
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, &SimpleExportDecoder::initPriv, Qt::BlockingQueuedConnection);
#else
    QMetaObject::invokeMethod(this, "initPriv", Qt::BlockingQueuedConnection);
#endif
}

SimpleExportDecoder::~SimpleExportDecoder()
{
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, &SimpleExportDecoder::destroyPriv, Qt::BlockingQueuedConnection);
#else
    QMetaObject::invokeMethod(this, "destroyPriv", Qt::BlockingQueuedConnection);
#endif
    t->quit();
    if(!t->wait(1000))
    {
        std::cerr << "[WARN]\tlibAV player thread didn't stop within 1000 milliseconds -> terminated." << std::endl;
        t->terminate();
        t->wait(1000);
    }
    delete t;
}

void SimpleExportDecoder::loadFile(std::string file, QDateTime startTime)
{
    if(file.empty()) {
        return;
    }

    lastMedia = file;

    mutex.lock();
    toLoad = file;
    mutex.unlock();

    mCurrentVideoStart = startTime;

#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    QMetaObject::invokeMethod(this, &SimpleExportDecoder::loadFilePrivate, Qt::QueuedConnection);
#else
    QMetaObject::invokeMethod(this, "loadFilesPrivate", Qt::QueuedConnection);
#endif
}

void SimpleExportDecoder::processReaderData(QByteArray ba, int exporterID)
{
    QDataStream ds(ba);
    libavProtocolCommons::MessageTypeReaderToDecoder header;
    int headerint;
    ds >> headerint;
    header = static_cast<libavProtocolCommons::MessageTypeReaderToDecoder>(headerint);

    switch(header) {

    case libavProtocolCommons::MCReaderData: {
        QByteArray ba2;
        ds >> ba2;
        m.processReaderData(ba2);
        QMetaObject::invokeMethod(this, std::bind(&::SimpleExportDecoder::processLoadDataFromReader, this, exporterID), Qt::QueuedConnection);
        break;
    }
    case libavProtocolCommons::Packets: {
        bool transferFinished;
        QList<libavProtocolCommons::GoalAVPacket> newPackets;
        ds >> transferFinished;
        ds >> newPackets;
        packetsBufferLock.lockForWrite();
        packetsBuffer.append(newPackets);
        packetsBufferLock.unlock();
        // qDebug() << "PACKETS ARRIVED FROM READER! TO EXPORT " << packetsBuffer.size();
        this->saveExportFramesPrivate(exporterID);
        break;
    }
    case libavProtocolCommons::ErrorMessage: {
        QString errmsg;
        ds >> errmsg;
        qWarning() << "NETWORK READER ERROR:" << errmsg;
        break;
    }
    case libavProtocolCommons::EOFDetected:
        break;
    }
}

void SimpleExportDecoder::onExportFrames(int exporterID, int secsBefore, int secsAfter, QDateTime midTime, QString url)
{
    if(url.isEmpty()){
        emit exportError(exporterID, m_exportUrl, "No URL provided !");
        return;
    }

    m_exportUrl = url;

    QDateTime temp = midTime.addSecs(-secsBefore);
    int64_t mediaPositionMS = mCurrentVideoStart.msecsTo(temp);
    qDebug() << "MID" << midTime << "START" << temp << "PosNS" << mediaPositionMS;
    if(!probeOK){
        return;
    }

    int64_t pts = (mediaPositionMS * m.timebase_den) / 1000;
//    int64_t pts = (static_cast<double>(mediaPositionNS)/static_cast<double>(1000000000)) * m.videoStream > -1 ? m.timebase_den : 0;
    int distanceToDown = pts % static_cast<int>(m.avg_pts);
    int distanceToUp = static_cast<int>(m.avg_pts) - distanceToDown;

    qDebug() << "PTS" << pts << "DISTANCE DOWN" << distanceToDown << "DISTANCE UP" << distanceToUp;
    pts -= distanceToDown;
    qDebug() << "rounded to" << pts;

    std::vector<int> streams;
    if(m.videoStream > -1) {
        streams.push_back(m.videoStream);
    }
    if(m.audioStream > -1) {
        streams.push_back(m.audioStream);
    }

    pts += m.firstPts[m.haveAudioOnly ? m.audioStream : m.videoStream];

    int numOfFrames = (secsBefore + secsAfter)*m.avg_fps;

    if(m.videoStream < 0 && m.audioStream < 0) {
        std::cerr << "libavDecoder2 ERROR! No video or audio stream found in provided file!" << std::endl;
        return;
    }

    int64_t seekPTS = getSeekPTSPrivate(pts);

    //if we are indeed seeking, we want to clear our packets buffer
    packetsBufferLock.lockForWrite();
    packetsBuffer.clear();
    packetsBufferLock.unlock();

    readPacketsRequest(seekPTS, pts, numOfFrames, 20, 0);

}

void SimpleExportDecoder::initPriv()
{

}

void SimpleExportDecoder::destroyPriv()
{

}

void SimpleExportDecoder::loadFilePrivate()
{
    mutex.lock();
    std::string f = toLoad;
    mutex.unlock();
    if(f.empty()) {
        return;
    }

    m.url = QString::fromStdString(f);

    openURLRequest(m.url, -1);
}

void SimpleExportDecoder::processLoadDataFromReader(int exporterID)
{
    probeOK = true;
    emit probeFinished(exporterID);
}

void SimpleExportDecoder::openURLRequest(QString url, int probeFrames)
{
    QByteArray ba;
    QDataStream ds(&ba, QIODevice::WriteOnly);
    ds << libavProtocolCommons::LoadURL;
    ds << url << probeFrames;
    emit sendDataToReader(ba);
}

void SimpleExportDecoder::readPacketsRequest(int64_t seekPTS, int64_t startPTS, int noOfFrames, int paddingFrames, int maxFramesInOneDatagram)
{
    QByteArray ba;
    QDataStream ds(&ba, QIODevice::WriteOnly);
    ds << libavProtocolCommons::ReadRequest;
    ds << (qint64)seekPTS << (qint64)startPTS << noOfFrames << paddingFrames << maxFramesInOneDatagram;
    emit sendDataToReader(ba);
}

int64_t SimpleExportDecoder::getSeekPTSPrivate(int64_t referencePts)
{
    //God forgive me for I have sinned
    //probably the most complicated part of what we try to accomplish

    int used_gop = m.max_gop; //GOP is easy

    //are we looking for the next frame? :)
    //the multiplication by 1.1 should be for an apparent reason: We do not want to miss the right PTS of next frame
    bool lookingForTheNext = (referencePts > m.lastPts[m.videoStream] && referencePts <= m.lastPts[m.videoStream] + m.avg_pts*1.1);

    if(lookingForTheNext) {
        return AV_NOPTS_VALUE; //noo seek
    }

    if(referencePts >=0) {

        int64_t seekPts = referencePts - used_gop*m.avg_pts;

        if(seekPts<0) {
            seekPts = 0;
        }
        qDebug() << "SEEKING THESE" << seekPts;
        return seekPts;

    }

    return AV_NOPTS_VALUE; //no seek
}

void SimpleExportDecoder::saveExportFramesPrivate(int exporterID)
{
    if(m_exportUrl.isEmpty()){
        return;
    }

    if(writer == nullptr){
        writer = new libavWriter();
    }

    writer->setURL(m_exportUrl);

    writer->setVideoProperties(m.avcp[m.videoStream].width,m.avcp[m.videoStream].height,m.avcp[m.videoStream].codec_id,m.timebase_num,m.timebase_den,m.videoStream);
    if(exportAudio && m.audioStream > -1) {
        writer->setSaveAudio(true);
        //Note: when reading TS, libavformat improperly reads the format and sample_rate. We set it to value that is being used by libavEncoder...
#if LIBAVCODEC_VERSION_MAJOR >= 61
        // For FFmpeg 7+, use the new channel layout API
        uint64_t channel_layout_mask = 0;
        int channels = 2; // default stereo
        if (m.avcp[m.audioStream].ch_layout.order == AV_CHANNEL_ORDER_NATIVE) {
            channel_layout_mask = m.avcp[m.audioStream].ch_layout.u.mask;
            channels = m.avcp[m.audioStream].ch_layout.nb_channels;
        } else {
            channel_layout_mask = AV_CH_LAYOUT_STEREO; // fallback
        }
        writer->setAudioProperties(m.avcp[m.audioStream].codec_id, channel_layout_mask, channels, 48000, AV_SAMPLE_FMT_FLTP,
                                   m.avcp[m.audioStream].bits_per_raw_sample, m.avcp[m.audioStream].bits_per_coded_sample, m.timebase_num, m.timebase_den, m.audioStream);
#else
        // For FFmpeg 6 and earlier, use the old channel layout API
        writer->setAudioProperties(m.avcp[m.audioStream].codec_id, m.avcp[m.audioStream].channel_layout, m.avcp[m.audioStream].channels, 48000, AV_SAMPLE_FMT_FLTP,
                                   m.avcp[m.audioStream].bits_per_raw_sample, m.avcp[m.audioStream].bits_per_coded_sample, m.timebase_num, m.timebase_den, m.audioStream);
#endif
    }

    if(writer->takePackets(packetsBuffer)){
            writer->finish();
            emit exportFinished(exporterID);
    }else{
            emit exportError(exporterID, m_exportUrl, "Failed to set frames to writer !");
    }
}
