#include "libavplayerdirector.h"

libavPlayerDirector::libavPlayerDirector(int mediaID, std::function<void (VideoFrame)> consumer, std::function<void (int, QByteArray)> consumerAudio, bool backwardPlay) : QThreadedObject()
{
    this->consumer = consumer;
    this->consumerAudio = consumerAudio;
    this->mediaID = mediaID;
    this->backwardPlay = backwardPlay;


    QThreadedObject::t_init();

    //add some random time to avoid peaks in usage whe lot of media players are running in synchronized way (0..500ms range)
    quint32 randMS = QRandomGenerator::global()->bounded(500);
    safePreloadNsecs += (randMS * 1000000);
}

libavPlayerDirector::~libavPlayerDirector()
{
    setAtomicFlag(Destroying, true);
    QThreadedObject::t_destroy();
}

void libavPlayerDirector::addMedia(QString path, SecsNanosecs startTime, uint64_t seekOffsetNS, bool isImage, bool startPaused)
{
    QMetaObject::invokeMethod(this, std::bind(&libavPlayerDirector::addMedia_priv, this, path, startTime, seekOffsetNS, isImage, startPaused));
}

void libavPlayerDirector::preloadMedia(QString path, uint64_t seekOffsetNS, bool isImage)
{
    QMetaObject::invokeMethod(this, std::bind(&libavPlayerDirector::preloadMedia_priv, this, path, seekOffsetNS, isImage));
}

void libavPlayerDirector::setPaused(SecsNanosecs startTime, bool paused)
{
    QMetaObject::invokeMethod(this, std::bind(&libavPlayerDirector::addPauseAction_priv, this, startTime, paused));
}

void libavPlayerDirector::stop()
{
    mediaMutex.lock();
    mediaActive = MediaPlaybackDefinition();
    mediaList.clear();
    mediaMutex.unlock();

    corrMutex.lock();
    correctionsList.clear();
    corrMutex.unlock();

    setAtomicFlag(NextActionPlanned, false);
    setAtomicFlag(BufferSwitchPlanned, false);
    setAtomicFlag(CorrectionsSwitchPlanned, false);
    setAtomicFlag(PauseActionPlanned, false);
    setAtomicFlag(SpeedActionPlanned, false);
    setAtomicFlag(ImageBeingLoaded, false);
    setAtomicFlag(BlockSendingToBuffer, true);

    player.pause();
    setAtomicFlag(Paused, false);
    emit playbackStopped();

    //safe to call anyway, UNLESS SOMEBODY MAKES count of buffers DYNAMIC!!!
//    for(libavBuffer *b : buffers) {
//        b->clear();
//    }
}

void libavPlayerDirector::setPosponedSendAfterSwitch(bool postponed)
{
    setAtomicFlag(PostponedSendAfterSwitch, postponed);
}

void libavPlayerDirector::setColorCorrection(ColorCorrection cc, SecsNanosecs startTime)
{
    QMetaObject::invokeMethod(this, std::bind(&libavPlayerDirector::setColorCorrection_priv,this, cc, startTime));
}

void libavPlayerDirector::setPlaybackSpeed(SecsNanosecs startTime, float speed, bool moveQueued)
{
    QMetaObject::invokeMethod(this, std::bind(&libavPlayerDirector::setPlaybackSpeed_priv, this, startTime, speed, moveQueued));
}


void libavPlayerDirector::setNonCopy(bool noncopy)
{
    //threadsafe unless buffer count is dynamic
    for(libavBuffer *b : buffers) {
        b->getIntegralDecoderPtr()->setNonCopy(noncopy);
    }
}

void libavPlayerDirector::setUseHWaccel(AVHWDeviceType type)
{
    //threadsafe unless buffer count is dynamic
    for(libavBuffer *b : buffers) {
        b->getIntegralDecoderPtr()->setUseHwaccel(type);
    }
}

void libavPlayerDirector::setSupportedFormats(QList<AVPixelFormat> supportedFormats)
{
    //threadsafe unless buffer count is dynamic
    for(libavBuffer *b : buffers) {
        b->getIntegralDecoderPtr()->setSupportedFormats(supportedFormats);
    }
}

void libavPlayerDirector::setWantAudio(bool wantAudio)
{
    //threadsafe unless buffer count is dynamic
    for(libavBuffer *b : buffers) {
        b->getIntegralDecoderPtr()->setWantAudio(wantAudio);
    }
}

void libavPlayerDirector::setCustomCodec(AVCodecID c, std::string codecName)
{
    //threadsafe unless buffer count is dynamic
    for(libavBuffer *b : buffers) {
        b->getIntegralDecoderPtr()->setCustomCodec(c, codecName);
    }
}

void libavPlayerDirector::setCustomCodecOption(std::string name, std::string value)
{
    //threadsafe unless buffer count is dynamic
    for(libavBuffer *b : buffers) {
        b->getIntegralDecoderPtr()->setCustomCodecOption(name, value);
    }
}

void libavPlayerDirector::setMaxResolution(QSize res)
{
//    maxResolution = res;
    //threadsafe, because imageLoader is created only once and is threadsafe on itself
    imageLoader.setMaxResolution(res);
    //do not do that for players as the performance in this case is quite poor...

    //threadsafe unless buffer count is dynamic
    for(libavBuffer *b : buffers) {
        b->getIntegralDecoderPtr()->setMaxResolution(res);
    }
}

void libavPlayerDirector::setMediaID(int mediaID)
{
    this->mediaID = mediaID;
    for(libavBuffer *b : buffers) {
        b->setMediaID(mediaID);
    }
}

void libavPlayerDirector::setMediaUUID(QUuid uuid)
{
    this->uuid = uuid;
    for(libavBuffer *b : buffers) {
        b->setMediaUUID(uuid);
    }
}

void libavPlayerDirector::setOverrideProbeFrames(int noOfFrames)
{
    for(libavBuffer *b : buffers) {
        b->getIntegralDecoderPtr()->setOverrideProbeFrames(noOfFrames);
    }
}

void libavPlayerDirector::setBlockForwardJumps(bool blockFWJumps)
{
    for(libavBuffer *b : buffers) {
        b->setBlockForwardJumps(blockFWJumps);
    }
}

void libavPlayerDirector::setOptimizeLoad(bool optimizeLoad)
{
    this->optimizeLoad = optimizeLoad;
}

QUuid libavPlayerDirector::getMediaUUID()
{
    return this->uuid;
}

void libavPlayerDirector::onPlayerSsinceEpochNS(SecsNanosecs sns_fromepoch, int64_t ns_increment, double fps)
{
    /*
     * this method is accessed from foreign thread!
    */

    int64_t tmp_flag = flagsAtomic.load();

    if(checkAtomicFlag(Destroying, tmp_flag)) {
        return;
    }

    bool pauseImmediately = false;
    if(checkAtomicFlag(PauseActionPlanned, tmp_flag)) {
        // qDebug() << "Pause Action is planned and we know it, checking time!!!" << sns_fromepoch.toNS() << nextPauseEvent.toNS();
        pauseMutex.lock();
        if(nextPauseEvent <= sns_fromepoch) {
            setAtomicFlag(Paused, nextPausePaused);
            pauseImmediately = true;
            if(nextPauseEvent == sns_fromepoch) {
                pauseImmediately = false;
                //specific situation when we the pause starts exactly when this tick is
                //and we still want to play the frame which is around
            }
            lastPauseEvent = nextPauseEvent;
            lastPausePaused = nextPausePaused;
            setAtomicFlag(PauseActionPlanned, false);
            if(!lastPausePaused) { //disable that we want to have our media pauseable again
                mediaMutex.lock();
                mediaActive_Priv.ignorePause = false;
                mediaActive.ignorePause = false;
                mediaPrepare.ignorePause = false;
                for(MediaPlaybackDefinition &m : mediaList) {
                    m.ignorePause = false;
                }
                mediaMutex.unlock();
            }
            // qDebug() << "Pause Action started!!!";
        }
        pauseMutex.unlock();
    }

    if(checkAtomicFlag(Paused, tmp_flag) || pauseImmediately) {
        //move configs of all media
        incrementMediaStart(ns_increment, mediaActive_Priv);

        mediaMutex.lock();

        mediaActive = mediaActive_Priv;
        incrementMediaStart(ns_increment, mediaPrepare);
        for(MediaPlaybackDefinition & m : mediaList) {
            incrementMediaStart(ns_increment, m);
        }

        //the simplest solution is to move everything
        if(!mediaPrepare.ignorePause) {
            nextBufferSwitch_sns_fromepoch = nextBufferSwitch_sns_fromepoch + SecsNanosecs(0,ns_increment);
        }

        mediaMutex.unlock();
    }

    //if we needed to play something immediately, we need to change the playback FPS according to this new media
    if(checkAtomicFlag(FPSChanged, tmp_flag)) {
        setAtomicFlag(FPSChanged, false);
        mediaMutex.lock();
        player.align(mediaActive_Priv.startSNS_fromepoch);
        player.setFramerate(buffer_fps.at(bufferActiveIndex));
        mediaMutex.unlock();
        setAtomicFlag(BlockSendingToBuffer, false);
//        qDebug() << "weve changed the FPS";
    }

    //if we have a buffer switch planned, we need to take it now
    if(checkAtomicFlag(CorrectionsSwitchPlanned, tmp_flag)) {
        corrMutex.lock();
        if(sns_fromepoch >= nextCorrectionSwitch) {
            currentCorrection_Priv = nextCorrection;
            mediaMutex.lock();
            for(libavBuffer *b : buffers) {
                b->setColorCorrection(currentCorrection_Priv.colorc);
            }
            imageFrame.setColorCorrection(currentCorrection_Priv.colorc);
            mediaMutex.unlock();
            setAtomicFlag(CorrectionsSwitchPlanned, false);
        }
        corrMutex.unlock();
    }

    if(checkAtomicFlag(SpeedActionPlanned, tmp_flag)) {
        speedMutex.lock();
        if(nextSpeedSwitch <= sns_fromepoch) {
            currentSpeed_Priv = nextSpeed;
            if (backwardPlay) {
                for (libavBuffer *buf : buffers) {
                    if (nextSpeed.playbackSpeed < 0) {
                        buf->setPlayingTypeHint(libavBuffer::PlayingTypeHint::BackwardVariable);
                    } else if (nextSpeed.playbackSpeed >= 0) {
                        buf->setPlayingTypeHint(libavBuffer::PlayingTypeHint::ForwardVariable);
                    }
                }
            }
            qDebug() << "SWITCHING PLAYBACK SPEED";
            setAtomicFlag(SpeedActionPlanned, false);
        }
        speedMutex.unlock();
    }

    if(currentSpeed_Priv.playbackSpeed != 1.0f) {
        //The easiest way to preserve different changes of speed during lifetime of one media
        //is to change its start time. Once the media is active and buffer switched, its star time is only
        //being used for current time computation.
        //It is also important that this code is BEFORE the block which is below (that is switching buffers and media)
        //because if switch from previous media happens in exactly the same cycle, we do not want to move the start.
        //of next media.
        int64_t ns_change = ns_increment - (ns_increment * currentSpeed_Priv.playbackSpeed);
        incrementMediaStart(ns_change, mediaActive_Priv);

        //Did we expect this speed change to move already queued media?
        if(currentSpeed_Priv.moveQueuedMedia) {
            mediaMutex.lock();
            incrementMediaStart(ns_change, mediaPrepare);
            for(MediaPlaybackDefinition & m : mediaList) {
                incrementMediaStart(ns_change, m);
            }
            mediaMutex.unlock();
        }
    }

    // Just seek if we skipped loading a new file in prepared buffer
    if(checkAtomicFlag(ReplayFlag, tmp_flag)) {
        qDebug() << "REPLAY FLAG";
        seekInPrepareMediaAndPlay_priv(mediaPrepare);
        setAtomicFlag(ReplayFlag, false);
    }

    //If we have successfully prepared a media in prepareBuffer, we need to exchange the buffers now
    if(checkAtomicFlag(BufferSwitchPlanned, tmp_flag)) {
//        qDebug() << "We plan buffer switch at " << nextBufferSwitch_sns_fromepoch.s << nextBufferSwitch_sns_fromepoch.ns << "actual time: " << sns_fromepoch.s << sns_fromepoch.ns;
        QMutexLocker l(&mediaMutex);
        if(sns_fromepoch >= nextBufferSwitch_sns_fromepoch) {
            if(!mediaPrepare.path.isEmpty()) {
                emit playbackStarted();
            }
            // qDebug() << "Buffers EXCHANGED at" << sns_fromepoch.s << sns_fromepoch.ns << mediaPrepare.path << mediaActive.path;
            setAtomicFlag(BufferSwitchPlanned, false);
            bufferActiveIndex = (bufferActiveIndex + 1) % 2;
            bufferPrepareIndex = (bufferPrepareIndex + 1) % 2;
            activeBuffer_priv = buffers.at(bufferActiveIndex);

            mediaActive = mediaActive_Priv = mediaPrepare;

            if(mediaActive_Priv.isImage) {
                if(checkAtomicFlag(ImageBeingLoaded, flagsAtomic)) { //need to check flagsAtomic instead of tmp_flag
                    setAtomicFlag(CatchingUpWaitingForLoad, true);
                    setAtomicFlag(BlockSendingToBuffer, true);
                    player.pause();
                }
                else {
                    imageFrame = prepareFrame;
                }
            }

            player.setFramerate(buffer_fps.at(bufferActiveIndex));
            player.align(mediaActive_Priv.startSNS_fromepoch);
            setAtomicFlag(BlockSendingToBuffer, false);
            mediaActive_Priv.ignorePause = false;
            //this is when we start playing            
            if(mediaActive.path.isEmpty()) {
                player.pause();
                emit playbackStopped();
            }

            if(checkAtomicFlag(PostponedSendAfterSwitch, tmp_flag)) {
                return;
            }
        }

    }
//    else {
//        qDebug() << "NOT PLANNING A BUFFER SWITCH!";
//    }


    if(checkAtomicFlag(NextActionPlanned, tmp_flag)) {
//        qDebug() << "We have another action planned, checking if we should already act";
        if(sns_fromepoch >= nextAction_sns_fromepoch) {
            setAtomicFlag(NextActionPlanned, false);
//            qDebug() << "WE SHOULD ACT NOW, determining action!";
            QMetaObject::invokeMethod(this, std::bind(&libavPlayerDirector::determineAction, this, sns_fromepoch));
        }
    }

    if(checkAtomicFlag(ChangeFlag, tmp_flag)) {
        distributeChanges();
    }

    if(checkAtomicFlag(CatchingUpWaitingForLoad, tmp_flag)) {
        emit playbackStarted();
        setAtomicFlag(CatchingUpWaitingForLoad, false);
    }

     if(checkAtomicFlag(BlockSendingToBuffer, flagsAtomic)) {
         return;
     }

    if(mediaActive_Priv.isImage) {
        consumer(imageFrame);
    }
    else {
        //compute the ns we need to set to the buffer and send it
        int64_t ns_to_set  = (sns_fromepoch - mediaActive_Priv.startSNS_fromepoch).toNS() + mediaActive_Priv.seekOffsetNS;
        activeBuffer_priv->setNS(ns_to_set, true, fps);
        lastSecsNanosecs = sns_fromepoch;
    }
}

void libavPlayerDirector::priv_init()
{
    //init buffers and decoders
    buffers.append(new libavBuffer(this));
    buffers.append(new libavBuffer(this));

    int buffno = 0;
    for(libavBuffer *b : buffers) {
        //these we probably want universally for all the uses:
        b->setPlayingTypeHint(libavBuffer::PlayingTypeHint::ForwardVariable);
        b->setFileIsBeingRecorder(false);
        if (backwardPlay) { //set bigger buffer limits, ignore fixed forward playing buffer size
            b->setParameters(200, 100, 100, 10, 10);
        } else {
            b->setFixedForwardPlayingBufferSize(10); //use probed fps for buffer size
            b->setParameters(30, 10, 0, 10, 0);
        }
        b->setWantRushFrames(false); //we do not want that!
        b->setWantFramesBeforeExplicitRequest(false); //we don't want that either
        b->addConsumerFunction(consumer);
        b->addConsumerFunction(consumerAudio);
        b->setMediaID(mediaID);
        b->setWantAlignment(true);

        //can and should be customized
        b->getIntegralDecoderPtr()->setNonCopy(false);
        b->getIntegralDecoderPtr()->setUseHwaccel(AV_HWDEVICE_TYPE_NONE); //explicitly
        b->getIntegralDecoderPtr()->setSupportedFormats({AV_PIX_FMT_RGBA});
        b->getIntegralDecoderPtr()->setWantAudio(false);
        b->getIntegralDecoderPtr()->setOverrideProbeFrames(1);

        connect(b, &libavBuffer::probeFinished, this, std::bind(&libavPlayerDirector::onBufferProbeFinished, this, buffno));
        connect(b, &libavBuffer::afterEOF, this, &libavPlayerDirector::onAfterEOF); //add no?
        connect(b, &libavBuffer::decodingFinished, this, &libavPlayerDirector::decodingFinished);
        buffer_ns_seek.append(0);
        buffer_fps.append(50);
        ++buffno;
    }
    player.setFramerate(30);
    player.addConsumerSEpochNS(std::bind(&libavPlayerDirector::onPlayerSsinceEpochNS, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
    activeBuffer_priv = buffers.at(bufferActiveIndex);

    imageLoader.setMediaID(mediaID);
    connect(&imageLoader, &ImageLoader::frameFinished, this, &libavPlayerDirector::imageLoaded);

    setAtomicFlag(BufferSwitchPlanned, false);
    setAtomicFlag(NextActionPlanned, false);
    setAtomicFlag(CorrectionsSwitchPlanned, false);
    setAtomicFlag(PauseActionPlanned, false);
    setAtomicFlag(SpeedActionPlanned, false);
    setAtomicFlag(ListEmpty, true);
    setAtomicFlag(FPSChanged, false);
    setAtomicFlag(Paused, false);
    setAtomicFlag(ChangeFlag, false);
    setAtomicFlag(BlockSendingToBuffer, true);
    setAtomicFlag(ImageBeingLoaded, false);
}

void libavPlayerDirector::priv_destroy()
{
    player.blockInputs(true);
    stop();
    player.waitForEnd();
    for(libavBuffer *&b : buffers) {
        if(b) {
            delete b;
            b = nullptr;
        }
    }
//    qDebug() << "END OF THREADED DECTRUCTOR OF PLAYER DIRECTOR";
}

void libavPlayerDirector::onAfterEOF()
{
    //simple - if we have nothing in the list, stop playback!
    if(mediaList.empty() && !checkAtomicFlag(BufferSwitchPlanned, flagsAtomic)) {
        player.pause();
        mediaActive = MediaPlaybackDefinition();
        emit playbackStopped();
//        qDebug() << "PlaybackStopped";
    }
}



void libavPlayerDirector::determineAction(SecsNanosecs sns_fromepoch)
{
    //Corrections go through

    SecsNanosecs minTime{INT64_MAX,0}; //universal for both corrections and media

    subDeterminePause(sns_fromepoch, minTime);
    subDetermineCorrections(sns_fromepoch, minTime);
    subDetermineMediaPlayback(sns_fromepoch, minTime);
    subDetermineSpeed(sns_fromepoch, minTime);


    //this is only called by the onPlayerSsinceEpochNS or in addMedia - where the flag NextActionPlanned
    //is already explicitly put down. So this is safe

    //universal fir all events
    if(minTime < SecsNanosecs(INT64_MAX,0)) {
//        qDebug() << "NexActionPlanned is true";
        nextAction_sns_fromepoch = minTime - SecsNanosecs(0,safePreloadNsecs);
        setAtomicFlag(NextActionPlanned, true);
        player.play();
    }
}

void libavPlayerDirector::seekInPrepareMediaAndPlay_priv(const MediaPlaybackDefinition &i)
{
    mediaPrepare = (i);
    mediaMutex.lock();
    //preloaded media is already loading (or finished loading) first frames.
    //it only makes sense to move to another time if seek is further away
    if((i).seekOffsetNS > 100000000) { //100ms of a second
        buffers[bufferPrepareIndex]->moveToMSTime((i).seekOffsetNS/1000000);
    }
    nextBufferSwitch_sns_fromepoch = (i).startSNS_fromepoch;
    mediaMutex.unlock();
    setAtomicFlag(BufferSwitchPlanned, true);
    player.align(i.startSNS_fromepoch, true);
    //                qDebug() << "we already have the same media in prepare, just change values! We need to play the player explicitly here";
    player.play();
}

void libavPlayerDirector::playImmediately_priv(const MediaPlaybackDefinition &i, SecsNanosecs sns_fromepoch)
{
    setAtomicFlag(BufferSwitchPlanned, false); //we do not want to do this now!

    mediaMutex.lock();

    mediaActive = (i);
    //we need to compute the correct seek, yes, this is mathematically correct ;)
    buffer_ns_seek[bufferActiveIndex] = (sns_fromepoch - mediaActive.startSNS_fromepoch).toNS();

    player.pause(); //until the file loads, we should pause the player

    setAtomicFlag(ChangeFlag, true); //media active needs to be distributed
    setAtomicFlag(CatchingUpWaitingForLoad, true);

    if(!i.path.isEmpty()) {
        if(i.isImage) {
            loadImage(i.path);
        }
        else {
            buffers.at(bufferActiveIndex)->loadFile((i).path);
        }
    }
    else {
        player.pause(); //pause now!! immediately
        emit playbackStopped();
    }

    mediaMutex.unlock();
}

void libavPlayerDirector::loadMediaToPrepareBuffer_priv(const MediaPlaybackDefinition &preloadMedia)
{
    mediaMutex.lock();

    //        qDebug() << "We have media within safe preload!";

    mediaPrepare = preloadMedia;
    buffer_ns_seek[bufferPrepareIndex] = mediaPrepare.seekOffsetNS;

    if(!preloadMedia.path.isEmpty()) {
        if(preloadMedia.isImage) {
            loadImage(preloadMedia.path);
        }
        else {
            if (optimizeLoad && preloadMedia.path == buffers.at(bufferPrepareIndex)->getPath()) {
                setAtomicFlag(ReplayFlag, true);
            } else {
                buffers.at(bufferPrepareIndex)->loadFile(preloadMedia.path);
            }
        }
    }

    buffers.at(bufferPrepareIndex)->setWantRushFrames(false);

    nextBufferSwitch_sns_fromepoch = preloadMedia.startSNS_fromepoch;

    mediaMutex.unlock();
    setAtomicFlag(BufferSwitchPlanned, true);
    player.align(preloadMedia.startSNS_fromepoch, true);
}

void libavPlayerDirector::subDetermineCorrections(SecsNanosecs sns_fromepoch, SecsNanosecs &minTime)
{
    std::list<CorrectionsDefinition> newCorrList;
    for(const CorrectionsDefinition &i : correctionsList) {
        if(/*!checkAtomicFlag(CorrectionsSwitchPlanned, flagsAtomic) && */((sns_fromepoch >= i.startSNS_fromepoch) || (sns_fromepoch >= ((i).startSNS_fromepoch - SecsNanosecs(0,safePreloadNsecs))))) {
            //within safe period or too late - introduce in the switch
            corrMutex.lock();
            nextCorrection = i;
            nextCorrectionSwitch = i.startSNS_fromepoch;
            corrMutex.unlock();
            setAtomicFlag(CorrectionsSwitchPlanned, true);
            continue;
        }
        else if((i).startSNS_fromepoch < minTime) {
            minTime = (i).startSNS_fromepoch;
//            qDebug() << "Setting MinTime for Corrections!" << minTime.s << minTime.ns;
        }
        else {
            qDebug() << "[PLAYER DIRECTOR]\tWTF Corrections, we should not get here!";
        }
        newCorrList.push_back(i);
    }

    correctionsList = newCorrList;
}

void libavPlayerDirector::subDetermineMediaPlayback(SecsNanosecs sns_fromepoch, SecsNanosecs &minTime)
{

//    qDebug() << "Determine action starts, size of medialist:" << mediaList.size();

    std::list<MediaPlaybackDefinition> newMediaList;

    for(MediaPlaybackDefinition &i : mediaList) {
        //SPECIAL CASES - When there is a media that should already be played
        if(sns_fromepoch >= (i).startSNS_fromepoch) {
           qDebug() << "it is already late... play now!";
            if(mediaActive == (i)) {
               qDebug() << "Already playing the same media correctly";
                continue; //already playing the same media correctly
            }
            //if we have the same media in prepare, change the values and plan the switch! - Skip optimization
            if(mediaPrepare.path == (i).path) {
                seekInPrepareMediaAndPlay_priv(i);
                continue;
            }
            //otherwise, play this immediately!
            playImmediately_priv(i, sns_fromepoch);
            continue;

        }
        //NORMAL CASE - we have media in list which is inside our safePreloadPeriod
        //If there are multiple, take only first one, keep other ones back until buffers switch
        else if((!checkAtomicFlag(BufferSwitchPlanned, flagsAtomic) || i.startSNS_fromepoch == mediaPrepare.startSNS_fromepoch) && sns_fromepoch >= ((i).startSNS_fromepoch - SecsNanosecs(0,safePreloadNsecs))) {
            //preload
            loadMediaToPrepareBuffer_priv(i);
            continue;
        }

        //from the rest in the list, determine when the next action should be done
        else if((i).startSNS_fromepoch < minTime) {
            minTime = (i).startSNS_fromepoch;
//            qDebug() << "Setting MinTime!" << minTime.s << minTime.ns;
        }
        else {
//            qDebug() <<"There is an item in the list that is not of an interest currently...";
        }

        newMediaList.push_back(i);

    }

    mediaList = newMediaList;
}

void libavPlayerDirector::subDetermineSpeed(SecsNanosecs sns_fromepoch, SecsNanosecs &minTime)
{
    std::list<SpeedActionDefinition> newSpeedList;
    for(const SpeedActionDefinition &sad : speedList) {
        if(!checkAtomicFlag(SpeedActionPlanned, flagsAtomic) && sns_fromepoch >= (sad.startSNS_fromepoch - SecsNanosecs(0, safePreloadNsecs))) {
            qDebug() << "SPEED ACTION PLANNED";
            speedMutex.lock();
            nextSpeed = sad;
            nextSpeedSwitch = sad.startSNS_fromepoch;
            speedMutex.unlock();
            setAtomicFlag(SpeedActionPlanned, true);
            continue;
        }
        else if(sad.startSNS_fromepoch < minTime) {
            minTime = sad.startSNS_fromepoch;
        }

        newSpeedList.push_back(sad);
    }
    speedList = newSpeedList;
}

void libavPlayerDirector::subDeterminePause(SecsNanosecs sns_fromepoch, SecsNanosecs &minTime)
{
    std::list<PauseActionDefinition> newpauselist;
    for(const PauseActionDefinition &pad : pauseList) {
        if(!checkAtomicFlag(PauseActionPlanned, flagsAtomic) && sns_fromepoch >= (pad.startSNS_fromepoch - SecsNanosecs(0, safePreloadNsecs))) {
            pauseMutex.lock();
            nextPauseEvent = pad.startSNS_fromepoch;
            nextPausePaused = pad.paused;
            pauseMutex.unlock();
            setAtomicFlag(PauseActionPlanned, true);
//            qDebug() << "PAUSE ACTION PLANNED!!!" << pad.startSNS_fromepoch.s << pad.startSNS_fromepoch.ns;
            continue;
        }
        else if(pad.startSNS_fromepoch < minTime) {
            minTime = pad.startSNS_fromepoch;
//            qDebug() << "Setting MinTime for PauseEvents!" << minTime.s << minTime.ns;
        }
        else {
            // This is now valid - when PauseActionPlanned flag is raised but we do have some event in safe preload interval.
            // We just keep it here for later
        }
        newpauselist.push_back(pad);
    }
    pauseList = newpauselist;
}

void libavPlayerDirector::onBufferProbeFinished(int buffno)
{
    qDebug() << "OnProbeFinished, moving media to time of MS:" << buffer_ns_seek.at(buffno) / 1000000;
    QMutexLocker l(&mediaMutex);
    buffers.at(buffno)->moveToMSTime(buffer_ns_seek.at(buffno) / 1000000);
    buffer_fps[buffno] = buffers.at(buffno)->getFps();
    if(bufferActiveIndex == buffno) {
        setAtomicFlag(FPSChanged, true);
    }
    player.play(); //when probe is finished, we can start up the player
    emit probeFinished(buffers.at(buffno)->getPath());
}

void libavPlayerDirector::distributeChanges()
{
    setAtomicFlag(ChangeFlag, false);
    QMutexLocker l(&mediaMutex);
    mediaActive_Priv = mediaActive;
}

void libavPlayerDirector::addMedia_priv(QString path, SecsNanosecs startTime, uint64_t seekOffsetNS, bool isImage, bool startPaused)
{
    // qDebug() << "Adding media" << path <<  "with start time" << startTime.s << startTime.ns;
    //check if we have media that start at the same time/after this one. These will be removed!
    std::list<MediaPlaybackDefinition>::iterator i = mediaList.begin();
    while(i != mediaList.end()) {
        bool removeFlag = false;

        if(startTime <= (*i).startSNS_fromepoch) {
            mediaList.erase(i++); //increase and remove as previous
            removeFlag = true;
            //            qDebug() << "removed some media because there were some in future";
        }

        if(!removeFlag) {
            ++i;
        }
    }

    //now just add this media to the list
    MediaPlaybackDefinition med;
    med.path = path; med.startSNS_fromepoch = startTime; med.seekOffsetNS = seekOffsetNS; med.isImage = isImage;
    med.ignorePause = checkAtomicFlag(Paused, flagsAtomic);
    mediaList.push_back(med);

    //if we are paused, we should also create unpause for the same time:
    if(med.ignorePause && !startPaused) {
        addPauseAction_priv(med.startSNS_fromepoch, false);
    }
    else if(startPaused) {
        addPauseAction_priv(med.startSNS_fromepoch, true);
    }
    if(med.ignorePause || startPaused) {
        return; //the determineAction will be done in above method
    }
    //aaaand determine the action based on that. we need to use the time reference here
    tref.update();
    determineAction(tref.lastSsinceEpochNS());
    //    player.play();
}

void libavPlayerDirector::preloadMedia_priv(QString path, uint64_t seekOffsetNS, bool isImage)
{
    //simply preload to prepare buffer

    MediaPlaybackDefinition preloadMedia;
    preloadMedia.isImage = isImage;
    preloadMedia.path = path;
    preloadMedia.seekOffsetNS = seekOffsetNS;

    mediaMutex.lock();

//    qDebug() << "We have media within safe preload!";

    mediaPrepare = preloadMedia;
    buffer_ns_seek[bufferPrepareIndex] = mediaPrepare.seekOffsetNS;

    if(!preloadMedia.path.isEmpty()) {
        if(preloadMedia.isImage) {
            loadImage(preloadMedia.path);
        }
        else {
            buffers.at(bufferPrepareIndex)->loadFile(preloadMedia.path);
        }
    }

    buffers.at(bufferPrepareIndex)->setWantRushFrames(false);
    mediaMutex.unlock();

    //we do not want to plan the buffer switch in this case!
}

void libavPlayerDirector::setColorCorrection_priv(ColorCorrection cc, SecsNanosecs startTime)
{
//    qDebug() << "Adding corrections with start time" << startTime.s << startTime.ns;
    //check if we have corrections that start at the same time/after this one. These will be removed!
    std::list<CorrectionsDefinition>::iterator i = correctionsList.begin();
    while(i != correctionsList.end()) {
        bool removeFlag = false;

        if(startTime <= (*i).startSNS_fromepoch) {
            correctionsList.erase(i++); //increase and remove as previous
            removeFlag = true;
//            qDebug() << "removed some corrections because there were some in future";
        }

        if(!removeFlag) {
            ++i;
        }
    }

    CorrectionsDefinition def;
    def.startSNS_fromepoch = startTime;
    def.colorc = cc;
    correctionsList.push_back(def);
    //now just add this correction to the list

    //aaaand determine the action based on that. we need to use the time reference here
    tref.update();
    determineAction(tref.lastSsinceEpochNS());
}

void libavPlayerDirector::addPauseAction_priv(SecsNanosecs startTime, bool paused)
{
//    qDebug() << "Adding pause action with start time " << startTime.s << startTime.ns;
    std::list<PauseActionDefinition> newlist;
    for(PauseActionDefinition &pad : pauseList) {
        if(startTime > pad.startSNS_fromepoch) { //in the old list keeping only previous actions. If new action is sooner than some other, remove it!
            newlist.push_back(pad);
        }
    }
    pauseList = newlist;

    PauseActionDefinition pad;
    pad.startSNS_fromepoch = startTime;
    pad.paused = paused;
    pauseList.push_back(pad);
//    qDebug() << "Pause Action appended" << pad.startSNS_fromepoch.s << pad.startSNS_fromepoch.ns;

    tref.update();
    determineAction(tref.lastSsinceEpochNS());
}

void libavPlayerDirector::setPlaybackSpeed_priv(SecsNanosecs startTime, float speed, bool moveQueued)
{
    std::list<SpeedActionDefinition> newlist;
    for(SpeedActionDefinition &sad : speedList) {
        //only keep previous actions in the old list
        if(startTime > sad.startSNS_fromepoch) {
            newlist.push_back(sad);
        }
    }
    speedList = newlist;
    SpeedActionDefinition sad;
    sad.playbackSpeed = speed;
    sad.moveQueuedMedia = moveQueued;
    sad.startSNS_fromepoch = startTime;
    speedList.push_back(sad);

    tref.update();
    determineAction(tref.lastSsinceEpochNS());
}

void libavPlayerDirector::loadImage(QString path)
{
    setAtomicFlag(ImageBeingLoaded, true);
    imageLoader.loadImage(path);
}

void libavPlayerDirector::imageLoaded(VideoFrame f)
{
    QMutexLocker l(&mediaMutex);
    setAtomicFlag(ImageBeingLoaded, false);
    if(checkAtomicFlag(CatchingUpWaitingForLoad, flagsAtomic)) {
        imageFrame = f;
        setAtomicFlag(BlockSendingToBuffer, false);
    }
    else {
        prepareFrame = f;
    }
    player.play();
}

void libavPlayerDirector::incrementMediaStart(int64_t ns, libavPlayerDirector::MediaPlaybackDefinition &m)
{
    if(!m.ignorePause) {
        m.startSNS_fromepoch = m.startSNS_fromepoch + SecsNanosecs(0,ns);
    }
}

float libavPlayerDirector::getCurrentFPS() const
{
    return buffer_fps.at(bufferActiveIndex);
}
