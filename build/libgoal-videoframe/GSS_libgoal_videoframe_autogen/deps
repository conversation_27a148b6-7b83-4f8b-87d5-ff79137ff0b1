GSS_libgoal_videoframe_autogen/timestamp: \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/CMakeLists.txt \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/audiocorrection.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/audiocorrection.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/colorcorrection.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/colorcorrection.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/framemetadata.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/framemetadata.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/qvideoframehelper.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/qvideoframehelper.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/videoframe.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/videoframe.h \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Config.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Targets.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeature.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeatureCommon.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtInstallPaths.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake \
	/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake \
	/usr/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake \
	/usr/bin/cmake
