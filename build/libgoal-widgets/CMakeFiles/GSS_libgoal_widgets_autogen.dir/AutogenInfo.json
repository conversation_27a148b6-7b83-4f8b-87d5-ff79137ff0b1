{"BUILD_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/CMakeLists.txt", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Config.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtInstallPaths.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Targets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeature.cmake", "/usr/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Config.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtInstallPaths.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Targets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeature.cmake", "/usr/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Config.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtInstallPaths.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Targets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeature.cmake", "/usr/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/splash-resources.qrc"], "CMAKE_SOURCE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing", "DEP_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/deps", "DEP_FILE_RULE_NAME": "GSS_libgoal_widgets_autogen/timestamp", "HEADERS": [["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/goalsplashscreen.h", "MU", "GGRFHCMMEE/moc_goalsplashscreen.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/manager.h", "MU", "6IPYH7RWEZ/moc_manager.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/model.h", "MU", "6IPYH7RWEZ/moc_model.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/slider.h", "MU", "6IPYH7RWEZ/moc_slider.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_WIDGETS_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "/opt/Qt/6.8.2/gcc_64/include", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "/usr/include", "/usr/include/c++/13", "/usr/include/x86_64-linux-gnu/c++/13", "/usr/include/c++/13/backward", "/usr/lib/gcc/x86_64-linux-gnu/13/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-std=gnu++17", "-dM", "-E", "-c", "/usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 16, "PARSE_CACHE_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/opt/Qt/6.8.2/gcc_64/libexec/moc", "QT_UIC_EXECUTABLE": "/opt/Qt/6.8.2/gcc_64/libexec/uic", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 8, "SETTINGS_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/goalsplashscreen.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/manager.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/model.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/slider.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "VERBOSITY": 0}