{"BUILD_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "CMAKE_SOURCE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing", "GENERATOR": "Ninja", "INCLUDE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include", "INPUTS": ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/resources/splash-rvw.png"], "LOCK_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen.dir/AutoRcc_splash-resources_GGRFHCMMEE_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["-name", "splash_resources"], "OUTPUT_CHECKSUM": "GGRFHCMMEE", "OUTPUT_NAME": "qrc_splash-resources.cpp", "RCC_EXECUTABLE": "/opt/Qt/6.8.2/gcc_64/libexec/rcc", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen.dir/AutoRcc_splash-resources_GGRFHCMMEE_Used.txt", "SOURCE": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/splash-resources.qrc", "VERBOSITY": 0}