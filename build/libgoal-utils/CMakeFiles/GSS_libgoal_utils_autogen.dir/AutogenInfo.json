{"BUILD_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/CMakeLists.txt", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Config.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtInstallPaths.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Targets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeature.cmake", "/usr/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Config.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtInstallPaths.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Targets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeature.cmake", "/usr/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake"], "CMAKE_SOURCE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing", "DEP_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/deps", "DEP_FILE_RULE_NAME": "GSS_libgoal_utils_autogen/timestamp", "HEADERS": [["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/doitlater.h", "MU", "6YEA5652QU/moc_doitlater.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/elapsedtimereference.h", "MU", "6YEA5652QU/moc_elapsedtimereference.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/freqsync.h", "MU", "6YEA5652QU/moc_freqsync.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsharedmemory.h", "MU", "6YEA5652QU/moc_goalsharedmemory.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsystemsemaphore.h", "MU", "6YEA5652QU/moc_goalsystemsemaphore.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/memorypool.h", "MU", "6YEA5652QU/moc_memorypool.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/obfuscate.h", "MU", "6YEA5652QU/moc_obfuscate.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/qthreadedobject.h", "MU", "6YEA5652QU/moc_qthreadedobject.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/runguard.h", "MU", "6YEA5652QU/moc_runguard.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/threadsafequeue.h", "MU", "6YEA5652QU/moc_threadsafequeue.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/timedqueue.h", "MU", "6YEA5652QU/moc_timedqueue.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/timesynccache.h", "MU", "6YEA5652QU/moc_timesynccache.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/universalplayer.h", "MU", "6YEA5652QU/moc_universalplayer.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CONCURRENT_LIB", "QT_CORE_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "/opt/Qt/6.8.2/gcc_64/include", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "/usr/include/c++/13", "/usr/include/x86_64-linux-gnu/c++/13", "/usr/include/c++/13/backward", "/usr/lib/gcc/x86_64-linux-gnu/13/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-std=gnu++17", "-dM", "-E", "-c", "/usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 16, "PARSE_CACHE_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/CMakeFiles/GSS_libgoal_utils_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/opt/Qt/6.8.2/gcc_64/libexec/moc", "QT_UIC_EXECUTABLE": "/opt/Qt/6.8.2/gcc_64/libexec/uic", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 8, "SETTINGS_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/CMakeFiles/GSS_libgoal_utils_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/doitlater.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/freqsync.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsharedmemory.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsystemsemaphore.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/memorypool.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/qthreadedobject.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/runguard.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/timedqueue.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/universalplayer.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "VERBOSITY": 0}