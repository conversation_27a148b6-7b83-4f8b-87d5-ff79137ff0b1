[{"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build -I/home/<USER>/PROJECTS/av-routing-and-mixing -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/include -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtQml -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtQuick -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlMeta -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlModels -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript -isystem /opt/Qt/6.8.2/gcc_64/include/QtOpenGL -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o CMakeFiles/av-routing-and-mixing.dir/av-routing-and-mixing_autogen/mocs_compilation.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/mocs_compilation.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/mocs_compilation.cpp", "output": "CMakeFiles/av-routing-and-mixing.dir/av-routing-and-mixing_autogen/mocs_compilation.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build -I/home/<USER>/PROJECTS/av-routing-and-mixing -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/include -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtQml -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtQuick -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlMeta -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlModels -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript -isystem /opt/Qt/6.8.2/gcc_64/include/QtOpenGL -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o CMakeFiles/av-routing-and-mixing.dir/main.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/main.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/main.cpp", "output": "CMakeFiles/av-routing-and-mixing.dir/main.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build -I/home/<USER>/PROJECTS/av-routing-and-mixing -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/include -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtQml -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtQuick -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlMeta -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlModels -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript -isystem /opt/Qt/6.8.2/gcc_64/include/QtOpenGL -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o CMakeFiles/av-routing-and-mixing.dir/mainwindow.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/mainwindow.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/mainwindow.cpp", "output": "CMakeFiles/av-routing-and-mixing.dir/mainwindow.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build -I/home/<USER>/PROJECTS/av-routing-and-mixing -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/include -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtQml -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtQuick -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlMeta -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlModels -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript -isystem /opt/Qt/6.8.2/gcc_64/include/QtOpenGL -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o CMakeFiles/av-routing-and-mixing.dir/build/.qt/rcc/qrc_av-routing-and-mixing.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/.qt/rcc/qrc_av-routing-and-mixing.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/.qt/rcc/qrc_av-routing-and-mixing.cpp", "output": "CMakeFiles/av-routing-and-mixing.dir/build/.qt/rcc/qrc_av-routing-and-mixing.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build -I/home/<USER>/PROJECTS/av-routing-and-mixing -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/include -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtQml -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtQuick -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlMeta -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlModels -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript -isystem /opt/Qt/6.8.2/gcc_64/include/QtOpenGL -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o CMakeFiles/av-routing-and-mixing.dir/av-routing-and-mixing_qmltyperegistrations.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_qmltyperegistrations.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_qmltyperegistrations.cpp", "output": "CMakeFiles/av-routing-and-mixing.dir/av-routing-and-mixing_qmltyperegistrations.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build -I/home/<USER>/PROJECTS/av-routing-and-mixing -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/include -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtQml -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtQuick -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlMeta -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlModels -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript -isystem /opt/Qt/6.8.2/gcc_64/include/QtOpenGL -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o CMakeFiles/av-routing-and-mixing.dir/build/.qt/rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/.qt/rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/.qt/rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp", "output": "CMakeFiles/av-routing-and-mixing.dir/build/.qt/rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/GSS_libgoal_ndi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/GSS_libgoal_ndi_autogen/mocs_compilation.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/GSS_libgoal_ndi_autogen/mocs_compilation.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/GSS_libgoal_ndi_autogen/mocs_compilation.cpp", "output": "libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/GSS_libgoal_ndi_autogen/mocs_compilation.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/GSS_libgoal_ndi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndifinder.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndifinder.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndifinder.cpp", "output": "libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndifinder.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/GSS_libgoal_ndi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndireceiver.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndireceiver.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndireceiver.cpp", "output": "libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndireceiver.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/GSS_libgoal_ndi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndisender.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndisender.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndisender.cpp", "output": "libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndisender.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/GSS_libgoal_videoframe_autogen/mocs_compilation.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/mocs_compilation.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/mocs_compilation.cpp", "output": "libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/GSS_libgoal_videoframe_autogen/mocs_compilation.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/audiocorrection.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/audiocorrection.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/audiocorrection.cpp", "output": "libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/audiocorrection.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/colorcorrection.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/colorcorrection.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/colorcorrection.cpp", "output": "libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/colorcorrection.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/framemetadata.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/framemetadata.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/framemetadata.cpp", "output": "libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/framemetadata.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/qvideoframehelper.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/qvideoframehelper.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/qvideoframehelper.cpp", "output": "libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/qvideoframehelper.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/videoframe.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/videoframe.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/videoframe.cpp", "output": "libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/videoframe.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils_autogen/mocs_compilation.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/mocs_compilation.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/mocs_compilation.cpp", "output": "libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils_autogen/mocs_compilation.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/doitlater.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/doitlater.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/doitlater.cpp", "output": "libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/doitlater.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/memorypool.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/memorypool.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/memorypool.cpp", "output": "libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/memorypool.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/qthreadedobject.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/qthreadedobject.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/qthreadedobject.cpp", "output": "libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/qthreadedobject.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/runguard.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/runguard.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/runguard.cpp", "output": "libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/runguard.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/universalplayer.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/universalplayer.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/universalplayer.cpp", "output": "libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/universalplayer.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/freqsync.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/freqsync.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/freqsync.cpp", "output": "libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/freqsync.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/timedqueue.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/timedqueue.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/timedqueue.cpp", "output": "libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/timedqueue.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/goalsharedmemory.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsharedmemory.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsharedmemory.cpp", "output": "libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/goalsharedmemory.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/goalsystemsemaphore.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsystemsemaphore.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsystemsemaphore.cpp", "output": "libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/goalsystemsemaphore.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/NDIStream_send_test_app_autogen/mocs_compilation.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/mocs_compilation.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/mocs_compilation.cpp", "output": "libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/NDIStream_send_test_app_autogen/mocs_compilation.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/include/mediaplayervlc.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/include/mediaplayervlc.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/include/mediaplayervlc.cpp", "output": "libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/include/mediaplayervlc.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/mainwindow.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/mainwindow.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/mainwindow.cpp", "output": "libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/mainwindow.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/main.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/main.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/main.cpp", "output": "libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/main.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav_autogen/mocs_compilation.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/mocs_compilation.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/mocs_compilation.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav_autogen/mocs_compilation.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/imageloader.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/imageloader.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/imageloader.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/imageloader.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavbuffer.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavbuffer.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavbuffer.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavbuffer.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdecoder.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdecoder.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdirectsaver.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdirectsaver.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdirectsaver.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdirectsaver.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavencoder.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavencoder.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavencoder.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavencoder.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavfilter.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavfilter.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavfilter.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavfilter.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavplayer.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayer.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayer.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavplayer.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavplayerdirector.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayerdirector.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayerdirector.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavplayerdirector.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libswshelper.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libswshelper.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libswshelper.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libswshelper.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/simpleexportdecoder.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/simpleexportdecoder.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/simpleexportdecoder.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/simpleexportdecoder.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/videoframeexporter.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/videoframeexporter.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/videoframeexporter.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/videoframeexporter.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/filtercomplex.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/filtercomplex.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/filtercomplex.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/filtercomplex.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdecoder2.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder2.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder2.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdecoder2.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavreader.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavreader.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavreader.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavreader.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavwriter.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavwriter.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavwriter.cpp", "output": "libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavwriter.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/GSS_libgoal_smemory-video_autogen/mocs_compilation.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/mocs_compilation.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/mocs_compilation.cpp", "output": "libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/GSS_libgoal_smemory-video_autogen/mocs_compilation.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/include/smemory-video/sharedmemoryvideoplayer.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideoplayer.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideoplayer.cpp", "output": "libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/include/smemory-video/sharedmemoryvideoplayer.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/include/smemory-video/sharedmemoryvideosender.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideosender.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideosender.cpp", "output": "libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/include/smemory-video/sharedmemoryvideosender.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/recv_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/recv_test_app_autogen/mocs_compilation.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/recv_test_app_autogen/mocs_compilation.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/recv_test_app_autogen/mocs_compilation.cpp", "output": "libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/recv_test_app_autogen/mocs_compilation.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/recv_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/mainwindow.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/mainwindow.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/mainwindow.cpp", "output": "libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/mainwindow.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/recv_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/main.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/main.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/main.cpp", "output": "libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/main.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/GSS_libgoal_sdi_autogen/mocs_compilation.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/mocs_compilation.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/mocs_compilation.cpp", "output": "libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/GSS_libgoal_sdi_autogen/mocs_compilation.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/DeckLinkAPI/DeckLinkAPIDispatch.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDispatch.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDispatch.cpp", "output": "libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/DeckLinkAPI/DeckLinkAPIDispatch.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/capture.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/capture.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/capture.cpp", "output": "libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/capture.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/decklinknotificationcallback.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/decklinknotificationcallback.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/decklinknotificationcallback.cpp", "output": "libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/decklinknotificationcallback.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/outputsdi.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/outputsdi.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/outputsdi.cpp", "output": "libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/outputsdi.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/bmdsdihelper.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/bmdsdihelper.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/bmdsdihelper.cpp", "output": "libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/bmdsdihelper.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices_autogen/mocs_compilation.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/mocs_compilation.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/mocs_compilation.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices_autogen/mocs_compilation.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/ajakumocontroler.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/ajakumocontroler.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/ajakumocontroler.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/ajakumocontroler.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/aspencontroller.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/aspencontroller.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/aspencontroller.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/aspencontroller.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/bmvideohubcontroller.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubcontroller.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubcontroller.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/bmvideohubcontroller.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/bmvideohubswitcher.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubswitcher.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubswitcher.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/bmvideohubswitcher.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novacontrollerbase.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novacontrollerbase.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novacontrollerbase.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novacontrollerbase.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novamctrl660procontroller.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl660procontroller.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl660procontroller.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novamctrl660procontroller.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novaprouhdjrcontroller.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novaprouhdjrcontroller.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novaprouhdjrcontroller.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novaprouhdjrcontroller.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novamctrl4kcontroller.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl4kcontroller.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl4kcontroller.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novamctrl4kcontroller.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novahseriescontroller.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novahseriescontroller.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novahseriescontroller.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novahseriescontroller.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novavxseries.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novavxseries.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novavxseries.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novavxseries.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/lightwaremxseriescontroller.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/lightwaremxseriescontroller.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/lightwaremxseriescontroller.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/lightwaremxseriescontroller.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/XKeys/xkeyspiecontroller.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/XKeys/xkeyspiecontroller.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/XKeys/xkeyspiecontroller.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/XKeys/xkeyspiecontroller.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/UdpMsg/udpmessagesender.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagesender.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagesender.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/UdpMsg/udpmessagesender.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_NETWORK_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/UdpMsg/udpmessagereader.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagereader.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagereader.cpp", "output": "libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/UdpMsg/udpmessagereader.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp", "output": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/avrouter.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avrouter.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avrouter.cpp", "output": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/avrouter.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/liveinputmanager.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveinputmanager.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveinputmanager.cpp", "output": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/liveinputmanager.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/liveoutputmanager.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveoutputmanager.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveoutputmanager.cpp", "output": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/liveoutputmanager.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/videoformatter.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videoformatter.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videoformatter.cpp", "output": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/videoformatter.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/fpssync.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/fpssync.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/fpssync.cpp", "output": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/fpssync.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/videooutputprocess.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videooutputprocess.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videooutputprocess.cpp", "output": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/videooutputprocess.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/openclprocessor.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/openclprocessor.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/openclprocessor.cpp", "output": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/openclprocessor.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp", "output": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/opt/Qt/6.8.2/gcc_64/include/QtCore -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/opt/Qt/6.8.2/gcc_64/include/QtMultimedia -I/opt/Qt/6.8.2/gcc_64/include/QtGui -I/opt/Qt/6.8.2/gcc_64/include/QtNetwork -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/opt/Qt/6.8.2/gcc_64/include/QtConcurrent -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -I/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -I/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/opt/Qt/6.8.2/gcc_64/include/QtWidgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir/.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp", "output": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir/.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets_autogen/mocs_compilation.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/mocs_compilation.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/mocs_compilation.cpp", "output": "libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets_autogen/mocs_compilation.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/manager.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/manager.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/manager.cpp", "output": "libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/manager.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/model.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/model.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/model.cpp", "output": "libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/model.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/slider.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/slider.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/slider.cpp", "output": "libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/slider.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/splash/goalsplashscreen.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/goalsplashscreen.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/goalsplashscreen.cpp", "output": "libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/splash/goalsplashscreen.cpp.o"}, {"directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC -o libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp", "output": "libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp.o"}]