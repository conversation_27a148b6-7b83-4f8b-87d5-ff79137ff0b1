# This is the CMakeCache file.
# For build in directory: /home/<USER>/PROJECTS/av-routing-and-mixing/build
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:STRING=Debug

//CXX compiler
C<PERSON>KE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-13

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-13

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g

//No help, variable specified on the command line.
CMAKE_CXX_FLAGS_DEBUG_INIT:STRING=-DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -O2 -g -DNDEBUG

//No help, variable specified on the command line.
CMAKE_CXX_FLAGS_RELWITHDEBINFO_INIT:STRING=-DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//No help, variable specified on the command line.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=TRUE

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/ninja

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=av-routing-and-mixing

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=0.1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//The CMake toolchain file
CMAKE_TOOLCHAIN_FILE:FILEPATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/qt.toolchain.cmake

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Enable Stream Deck native support
DEVICES_ENABLE_STREAMDECK:BOOL=OFF

//Enable XKeys support
DEVICES_ENABLE_XKEYS:BOOL=ON

//Use CUDA capabilities of Videoframe
GOALSPORT_USE_CUDA:BOOL=OFF

//Use network-based experimental decoder
GOALSPORT_USE_EXPERIMENTAL_DECODER:BOOL=OFF

//Value Computed by CMake
GSS_libgoal_avrouter_BINARY_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter

//Value Computed by CMake
GSS_libgoal_avrouter_IS_TOP_LEVEL:STATIC=OFF

//Dependencies for the target
GSS_libgoal_avrouter_LIB_DEPENDS:STATIC=general;Qt6::Core;general;Qt6::Multimedia;general;GSS::libgoal::utils;general;GSS::libgoal::videoframe;general;GSS::libgoal::ndi;general;GSS::libgoal::sdi;general;GSS::libgoal::smemory-video;general;OpenCL::OpenCL;

//Value Computed by CMake
GSS_libgoal_avrouter_SOURCE_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter

//Value Computed by CMake
GSS_libgoal_devices_BINARY_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices

//Value Computed by CMake
GSS_libgoal_devices_IS_TOP_LEVEL:STATIC=OFF

//Dependencies for the target
GSS_libgoal_devices_LIB_DEPENDS:STATIC=general;-lpiehid;general;Qt6::Network;

//Value Computed by CMake
GSS_libgoal_devices_SOURCE_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices

//Value Computed by CMake
GSS_libgoal_libav_BINARY_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav

//Value Computed by CMake
GSS_libgoal_libav_IS_TOP_LEVEL:STATIC=OFF

//Dependencies for the target
GSS_libgoal_libav_LIB_DEPENDS:STATIC=general;Qt6::Widgets;general;GSS::libgoal::utils;general;GSS::libgoal::videoframe;

//Value Computed by CMake
GSS_libgoal_libav_SOURCE_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav

//Value Computed by CMake
GSS_libgoal_ndi_BINARY_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi

//Value Computed by CMake
GSS_libgoal_ndi_IS_TOP_LEVEL:STATIC=OFF

//Dependencies for the target
GSS_libgoal_ndi_LIB_DEPENDS:STATIC=general;-lndi;general;GSS::libgoal::videoframe;general;GSS::libgoal::utils;

//Value Computed by CMake
GSS_libgoal_ndi_SOURCE_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi

//Value Computed by CMake
GSS_libgoal_sdi_BINARY_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi

//Value Computed by CMake
GSS_libgoal_sdi_IS_TOP_LEVEL:STATIC=OFF

//Dependencies for the target
GSS_libgoal_sdi_LIB_DEPENDS:STATIC=general;GSS::libgoal::libav;

//Value Computed by CMake
GSS_libgoal_sdi_SOURCE_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi

//Value Computed by CMake
GSS_libgoal_smemory-video_BINARY_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video

//Value Computed by CMake
GSS_libgoal_smemory-video_IS_TOP_LEVEL:STATIC=OFF

//Dependencies for the target
GSS_libgoal_smemory-video_LIB_DEPENDS:STATIC=general;GSS::libgoal::utils;general;GSS::libgoal::videoframe;

//Value Computed by CMake
GSS_libgoal_smemory-video_SOURCE_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video

//Value Computed by CMake
GSS_libgoal_utils_BINARY_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils

//Value Computed by CMake
GSS_libgoal_utils_IS_TOP_LEVEL:STATIC=OFF

//Dependencies for the target
GSS_libgoal_utils_LIB_DEPENDS:STATIC=general;Qt6::Concurrent;

//Value Computed by CMake
GSS_libgoal_utils_SOURCE_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils

//Value Computed by CMake
GSS_libgoal_videoframe_BINARY_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe

//Value Computed by CMake
GSS_libgoal_videoframe_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
GSS_libgoal_videoframe_SOURCE_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe

//Value Computed by CMake
GSS_libgoal_widgets_BINARY_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets

//Value Computed by CMake
GSS_libgoal_widgets_IS_TOP_LEVEL:STATIC=OFF

//Dependencies for the target
GSS_libgoal_widgets_LIB_DEPENDS:STATIC=general;Qt6::Widgets;

//Value Computed by CMake
GSS_libgoal_widgets_SOURCE_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets

//Blacklist of packages to not include in the build
LIBGOAL_PACKAGES_BLACKLIST:STRING=

//Build all libgoal libs - include them in the ALL target
LIBGOAL_STANDALONE_BUILD:BOOL=OFF

//Value Computed by CMake
LibGoal_BINARY_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal

//Value Computed by CMake
LibGoal_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
LibGoal_SOURCE_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal

//Value Computed by CMake
NDIStream_send_test_app_BINARY_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app

//Value Computed by CMake
NDIStream_send_test_app_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
NDIStream_send_test_app_SOURCE_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app

//Path to a file.
OPENGL_EGL_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_GLES2_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_GLES3_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_GLX_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
OPENGL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
OPENGL_egl_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libEGL.so

//Path to a library.
OPENGL_gles2_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGLESv2.so

//Path to a library.
OPENGL_gles3_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGLESv2.so

//Path to a library.
OPENGL_glu_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGLU.so

//Path to a library.
OPENGL_glx_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libGLX.so

//Path to a library.
OPENGL_opengl_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libOpenGL.so

//Path to a file.
OPENGL_xmesa_INCLUDE_DIR:PATH=OPENGL_xmesa_INCLUDE_DIR-NOTFOUND

//Path to a file.
OpenCL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
OpenCL_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libOpenCL.so

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Additional directories where find(Qt6 ...) host Qt components
// are searched
QT_ADDITIONAL_HOST_PACKAGES_PREFIX_PATH:STRING=

//Additional directories where find(Qt6 ...) components are searched
QT_ADDITIONAL_PACKAGES_PREFIX_PATH:STRING=

//The directory containing a CMake configuration file for QT.
QT_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6

QT_QMAKE_TARGET_MKSPEC:STRING=linux-g++

//No help, variable specified on the command line.
QT_QML_GENERATE_QMLLS_INI:STRING=ON

//The directory containing a CMake configuration file for Qt6Concurrent.
Qt6Concurrent_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent

//The directory containing a CMake configuration file for Qt6CoreTools.
Qt6CoreTools_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools

//The directory containing a CMake configuration file for Qt6Core.
Qt6Core_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core

//The directory containing a CMake configuration file for Qt6DBusTools.
Qt6DBusTools_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools

//The directory containing a CMake configuration file for Qt6DBus.
Qt6DBus_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus

//The directory containing a CMake configuration file for Qt6ExamplesAssetDownloaderPrivate.
Qt6ExamplesAssetDownloaderPrivate_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate

//The directory containing a CMake configuration file for Qt6GuiTools.
Qt6GuiTools_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools

//The directory containing a CMake configuration file for Qt6Gui.
Qt6Gui_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui

//The directory containing a CMake configuration file for Qt6Multimedia.
Qt6Multimedia_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia

//The directory containing a CMake configuration file for Qt6Network.
Qt6Network_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network

//The directory containing a CMake configuration file for Qt6OpenGL.
Qt6OpenGL_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL

//The directory containing a CMake configuration file for Qt6QmlAssetDownloader.
Qt6QmlAssetDownloader_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader

//The directory containing a CMake configuration file for Qt6QmlCompilerPlusPrivateTools.
Qt6QmlCompilerPlusPrivateTools_DIR:PATH=Qt6QmlCompilerPlusPrivateTools_DIR-NOTFOUND

//The directory containing a CMake configuration file for Qt6QmlIntegration.
Qt6QmlIntegration_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlIntegration

//The directory containing a CMake configuration file for Qt6QmlMeta.
Qt6QmlMeta_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta

//The directory containing a CMake configuration file for Qt6QmlModels.
Qt6QmlModels_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels

//The directory containing a CMake configuration file for Qt6QmlTools.
Qt6QmlTools_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools

//The directory containing a CMake configuration file for Qt6QmlWorkerScript.
Qt6QmlWorkerScript_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript

//The directory containing a CMake configuration file for Qt6Qml.
Qt6Qml_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml

//The directory containing a CMake configuration file for Qt6QuickTools.
Qt6QuickTools_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools

//The directory containing a CMake configuration file for Qt6Quick.
Qt6Quick_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick

//The directory containing a CMake configuration file for Qt6WidgetsTools.
Qt6WidgetsTools_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools

//The directory containing a CMake configuration file for Qt6Widgets.
Qt6Widgets_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets

//The directory containing a CMake configuration file for Qt6.
Qt6_DIR:PATH=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6

//Path to a program.
Vulkan_GLSLANG_VALIDATOR_EXECUTABLE:FILEPATH=Vulkan_GLSLANG_VALIDATOR_EXECUTABLE-NOTFOUND

//Path to a program.
Vulkan_GLSLC_EXECUTABLE:FILEPATH=Vulkan_GLSLC_EXECUTABLE-NOTFOUND

//Path to a file.
Vulkan_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
Vulkan_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libvulkan.so

//Path to a file.
XKB_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
XKB_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libxkbcommon.so

//Value Computed by CMake
av-routing-and-mixing_BINARY_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build

//Value Computed by CMake
av-routing-and-mixing_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
av-routing-and-mixing_SOURCE_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing

//Path to a library.
pkgcfg_lib_PKG_XKB_xkbcommon:FILEPATH=/usr/lib/x86_64-linux-gnu/libxkbcommon.so

//Path to a library.
pkgcfg_lib_libavcodec_avcodec:FILEPATH=/usr/lib/x86_64-linux-gnu/libavcodec.so

//Path to a library.
pkgcfg_lib_libavfilter_avfilter:FILEPATH=/usr/lib/x86_64-linux-gnu/libavfilter.so

//Path to a library.
pkgcfg_lib_libavformat_avformat:FILEPATH=/usr/lib/x86_64-linux-gnu/libavformat.so

//Path to a library.
pkgcfg_lib_libavutil_avutil:FILEPATH=/usr/lib/x86_64-linux-gnu/libavutil.so

//Path to a library.
pkgcfg_lib_libswresample_swresample:FILEPATH=/usr/lib/x86_64-linux-gnu/libswresample.so

//Path to a library.
pkgcfg_lib_libswscale_swscale:FILEPATH=/usr/lib/x86_64-linux-gnu/libswscale.so

//Value Computed by CMake
recv_test_app_BINARY_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app

//Value Computed by CMake
recv_test_app_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
recv_test_app_SOURCE_DIR:STATIC=/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/PROJECTS/av-routing-and-mixing/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=28
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/usr/bin/cmake-gui
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/PROJECTS/av-routing-and-mixing
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=13
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.28
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding OpenCL
FIND_PACKAGE_MESSAGE_DETAILS_OpenCL:INTERNAL=[/usr/lib/x86_64-linux-gnu/libOpenCL.so][/usr/include][v3.0()]
//Details about finding OpenGL
FIND_PACKAGE_MESSAGE_DETAILS_OpenGL:INTERNAL=[/usr/lib/x86_64-linux-gnu/libOpenGL.so][/usr/lib/x86_64-linux-gnu/libGLX.so][/usr/include][c ][v()]
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/usr/bin/pkg-config][v1.8.1()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding WrapAtomic
FIND_PACKAGE_MESSAGE_DETAILS_WrapAtomic:INTERNAL=[1][v()]
//Details about finding WrapOpenGL
FIND_PACKAGE_MESSAGE_DETAILS_WrapOpenGL:INTERNAL=[ON][v()]
//Details about finding WrapVulkanHeaders
FIND_PACKAGE_MESSAGE_DETAILS_WrapVulkanHeaders:INTERNAL=[/usr/include][v()]
//Details about finding XKB
FIND_PACKAGE_MESSAGE_DETAILS_XKB:INTERNAL=[/usr/lib/x86_64-linux-gnu/libxkbcommon.so][/usr/include][v1.6.0(0.5.0)]
//Test HAVE_STDATOMIC
HAVE_STDATOMIC:INTERNAL=1
//Have symbol CL_VERSION_3_0
OPENCL_VERSION_3_0:INTERNAL=1
//ADVANCED property for variable: OPENGL_EGL_INCLUDE_DIR
OPENGL_EGL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_GLES2_INCLUDE_DIR
OPENGL_GLES2_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_GLES3_INCLUDE_DIR
OPENGL_GLES3_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_GLX_INCLUDE_DIR
OPENGL_GLX_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_INCLUDE_DIR
OPENGL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_egl_LIBRARY
OPENGL_egl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_gles2_LIBRARY
OPENGL_gles2_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_gles3_LIBRARY
OPENGL_gles3_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glu_LIBRARY
OPENGL_glu_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glx_LIBRARY
OPENGL_glx_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_opengl_LIBRARY
OPENGL_opengl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_xmesa_INCLUDE_DIR
OPENGL_xmesa_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenCL_INCLUDE_DIR
OpenCL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenCL_LIBRARY
OpenCL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
PKG_XKB_CFLAGS:INTERNAL=-I/usr/include
PKG_XKB_CFLAGS_I:INTERNAL=
PKG_XKB_CFLAGS_OTHER:INTERNAL=
PKG_XKB_FOUND:INTERNAL=1
PKG_XKB_INCLUDEDIR:INTERNAL=/usr/include
PKG_XKB_INCLUDE_DIRS:INTERNAL=/usr/include
PKG_XKB_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lxkbcommon
PKG_XKB_LDFLAGS_OTHER:INTERNAL=
PKG_XKB_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PKG_XKB_LIBRARIES:INTERNAL=xkbcommon
PKG_XKB_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PKG_XKB_LIBS:INTERNAL=
PKG_XKB_LIBS_L:INTERNAL=
PKG_XKB_LIBS_OTHER:INTERNAL=
PKG_XKB_LIBS_PATHS:INTERNAL=
PKG_XKB_MODULE_NAME:INTERNAL=xkbcommon
PKG_XKB_PREFIX:INTERNAL=/usr
PKG_XKB_STATIC_CFLAGS:INTERNAL=-I/usr/include
PKG_XKB_STATIC_CFLAGS_I:INTERNAL=
PKG_XKB_STATIC_CFLAGS_OTHER:INTERNAL=
PKG_XKB_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include
PKG_XKB_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lxkbcommon
PKG_XKB_STATIC_LDFLAGS_OTHER:INTERNAL=
PKG_XKB_STATIC_LIBDIR:INTERNAL=
PKG_XKB_STATIC_LIBRARIES:INTERNAL=xkbcommon
PKG_XKB_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PKG_XKB_STATIC_LIBS:INTERNAL=
PKG_XKB_STATIC_LIBS_L:INTERNAL=
PKG_XKB_STATIC_LIBS_OTHER:INTERNAL=
PKG_XKB_STATIC_LIBS_PATHS:INTERNAL=
PKG_XKB_VERSION:INTERNAL=1.6.0
PKG_XKB_xkbcommon_INCLUDEDIR:INTERNAL=
PKG_XKB_xkbcommon_LIBDIR:INTERNAL=
PKG_XKB_xkbcommon_PREFIX:INTERNAL=
PKG_XKB_xkbcommon_VERSION:INTERNAL=
//Qt feature: abstractbutton (from target Qt6::Widgets)
QT_FEATURE_abstractbutton:INTERNAL=ON
//Qt feature: abstractslider (from target Qt6::Widgets)
QT_FEATURE_abstractslider:INTERNAL=ON
//Qt feature: accessibility (from target Qt6::Gui)
QT_FEATURE_accessibility:INTERNAL=ON
//Qt feature: accessibility_atspi_bridge (from target Qt6::Gui)
QT_FEATURE_accessibility_atspi_bridge:INTERNAL=ON
//Qt feature: action (from target Qt6::Gui)
QT_FEATURE_action:INTERNAL=ON
//Qt feature: aesni (from target Qt6::Core)
QT_FEATURE_aesni:INTERNAL=ON
//Qt feature: alloca (from target Qt6::Core)
QT_FEATURE_alloca:INTERNAL=ON
//Qt feature: alloca_h (from target Qt6::Core)
QT_FEATURE_alloca_h:INTERNAL=ON
//Qt feature: alloca_malloc_h (from target Qt6::Core)
QT_FEATURE_alloca_malloc_h:INTERNAL=OFF
//Qt feature: alsa (from target Qt6::Multimedia)
QT_FEATURE_alsa:INTERNAL=OFF
//Qt feature: android_style_assets (from target Qt6::Core)
QT_FEATURE_android_style_assets:INTERNAL=OFF
//Qt feature: animation (from target Qt6::Core)
QT_FEATURE_animation:INTERNAL=ON
//Qt feature: appstore_compliant (from target Qt6::Core)
QT_FEATURE_appstore_compliant:INTERNAL=OFF
//Qt feature: arm_crc32 (from target Qt6::Core)
QT_FEATURE_arm_crc32:INTERNAL=OFF
//Qt feature: arm_crypto (from target Qt6::Core)
QT_FEATURE_arm_crypto:INTERNAL=OFF
//Qt feature: avfoundation (from target Qt6::Multimedia)
QT_FEATURE_avfoundation:INTERNAL=OFF
//Qt feature: avx (from target Qt6::Core)
QT_FEATURE_avx:INTERNAL=ON
//Qt feature: avx2 (from target Qt6::Core)
QT_FEATURE_avx2:INTERNAL=ON
//Qt feature: avx512bw (from target Qt6::Core)
QT_FEATURE_avx512bw:INTERNAL=ON
//Qt feature: avx512cd (from target Qt6::Core)
QT_FEATURE_avx512cd:INTERNAL=ON
//Qt feature: avx512dq (from target Qt6::Core)
QT_FEATURE_avx512dq:INTERNAL=ON
//Qt feature: avx512er (from target Qt6::Core)
QT_FEATURE_avx512er:INTERNAL=ON
//Qt feature: avx512f (from target Qt6::Core)
QT_FEATURE_avx512f:INTERNAL=ON
//Qt feature: avx512ifma (from target Qt6::Core)
QT_FEATURE_avx512ifma:INTERNAL=ON
//Qt feature: avx512pf (from target Qt6::Core)
QT_FEATURE_avx512pf:INTERNAL=ON
//Qt feature: avx512vbmi (from target Qt6::Core)
QT_FEATURE_avx512vbmi:INTERNAL=ON
//Qt feature: avx512vbmi2 (from target Qt6::Core)
QT_FEATURE_avx512vbmi2:INTERNAL=ON
//Qt feature: avx512vl (from target Qt6::Core)
QT_FEATURE_avx512vl:INTERNAL=ON
//Qt feature: backtrace (from target Qt6::Core)
QT_FEATURE_backtrace:INTERNAL=ON
//Qt feature: broken_threadlocal_dtors (from target Qt6::Core)
QT_FEATURE_broken_threadlocal_dtors:INTERNAL=OFF
//Qt feature: brotli (from target Qt6::Network)
QT_FEATURE_brotli:INTERNAL=OFF
//Qt feature: buttongroup (from target Qt6::Widgets)
QT_FEATURE_buttongroup:INTERNAL=ON
//Qt feature: calendarwidget (from target Qt6::Widgets)
QT_FEATURE_calendarwidget:INTERNAL=ON
//Qt feature: cborstreamreader (from target Qt6::Core)
QT_FEATURE_cborstreamreader:INTERNAL=ON
//Qt feature: cborstreamwriter (from target Qt6::Core)
QT_FEATURE_cborstreamwriter:INTERNAL=ON
//Qt feature: checkbox (from target Qt6::Widgets)
QT_FEATURE_checkbox:INTERNAL=ON
//Qt feature: clipboard (from target Qt6::Gui)
QT_FEATURE_clipboard:INTERNAL=ON
//Qt feature: clock_gettime (from target Qt6::Core)
QT_FEATURE_clock_gettime:INTERNAL=ON
//Qt feature: clock_monotonic (from target Qt6::Core)
QT_FEATURE_clock_monotonic:INTERNAL=ON
//Qt feature: close_range (from target Qt6::Core)
QT_FEATURE_close_range:INTERNAL=OFF
//Qt feature: colordialog (from target Qt6::Widgets)
QT_FEATURE_colordialog:INTERNAL=ON
//Qt feature: colornames (from target Qt6::Gui)
QT_FEATURE_colornames:INTERNAL=ON
//Qt feature: columnview (from target Qt6::Widgets)
QT_FEATURE_columnview:INTERNAL=ON
//Qt feature: combobox (from target Qt6::Widgets)
QT_FEATURE_combobox:INTERNAL=ON
//Qt feature: commandlineparser (from target Qt6::Core)
QT_FEATURE_commandlineparser:INTERNAL=ON
//Qt feature: commandlinkbutton (from target Qt6::Widgets)
QT_FEATURE_commandlinkbutton:INTERNAL=ON
//Qt feature: completer (from target Qt6::Widgets)
QT_FEATURE_completer:INTERNAL=ON
//Qt feature: concatenatetablesproxymodel (from target Qt6::Core)
QT_FEATURE_concatenatetablesproxymodel:INTERNAL=ON
//Qt feature: concurrent (from target Qt6::Core)
QT_FEATURE_concurrent:INTERNAL=ON
//Qt feature: contextmenu (from target Qt6::Widgets)
QT_FEATURE_contextmenu:INTERNAL=ON
//Qt feature: coreaudio (from target Qt6::Multimedia)
QT_FEATURE_coreaudio:INTERNAL=OFF
//Qt feature: cpp_winrt (from target Qt6::Core)
QT_FEATURE_cpp_winrt:INTERNAL=OFF
//Qt feature: cross_compile (from target Qt6::Core)
QT_FEATURE_cross_compile:INTERNAL=OFF
//Qt feature: cssparser (from target Qt6::Gui)
QT_FEATURE_cssparser:INTERNAL=ON
//Qt feature: ctf (from target Qt6::Core)
QT_FEATURE_ctf:INTERNAL=OFF
//Qt feature: cursor (from target Qt6::Gui)
QT_FEATURE_cursor:INTERNAL=ON
//Qt feature: cxx11_future (from target Qt6::Core)
QT_FEATURE_cxx11_future:INTERNAL=ON
//Qt feature: cxx17_filesystem (from target Qt6::Core)
QT_FEATURE_cxx17_filesystem:INTERNAL=ON
//Qt feature: cxx20 (from target Qt6::Core)
QT_FEATURE_cxx20:INTERNAL=OFF
//Qt feature: cxx23_stacktrace (from target Qt6::Core)
QT_FEATURE_cxx23_stacktrace:INTERNAL=OFF
//Qt feature: cxx2a (from target Qt6::Core)
QT_FEATURE_cxx2a:INTERNAL=OFF
//Qt feature: cxx2b (from target Qt6::Core)
QT_FEATURE_cxx2b:INTERNAL=OFF
//Qt feature: datawidgetmapper (from target Qt6::Widgets)
QT_FEATURE_datawidgetmapper:INTERNAL=ON
//Qt feature: datestring (from target Qt6::Core)
QT_FEATURE_datestring:INTERNAL=ON
//Qt feature: datetimeedit (from target Qt6::Widgets)
QT_FEATURE_datetimeedit:INTERNAL=ON
//Qt feature: datetimeparser (from target Qt6::Core)
QT_FEATURE_datetimeparser:INTERNAL=ON
//Qt feature: dbus (from target Qt6::Core)
QT_FEATURE_dbus:INTERNAL=ON
//Qt feature: dbus_linked (from target Qt6::Core)
QT_FEATURE_dbus_linked:INTERNAL=ON
//Qt feature: debug (from target Qt6::Core)
QT_FEATURE_debug:INTERNAL=OFF
//Qt feature: debug_and_release (from target Qt6::Core)
QT_FEATURE_debug_and_release:INTERNAL=OFF
//Qt feature: desktopservices (from target Qt6::Gui)
QT_FEATURE_desktopservices:INTERNAL=ON
//Qt feature: developer_build (from target Qt6::Core)
QT_FEATURE_developer_build:INTERNAL=OFF
//Qt feature: dial (from target Qt6::Widgets)
QT_FEATURE_dial:INTERNAL=ON
//Qt feature: dialog (from target Qt6::Widgets)
QT_FEATURE_dialog:INTERNAL=ON
//Qt feature: dialogbuttonbox (from target Qt6::Widgets)
QT_FEATURE_dialogbuttonbox:INTERNAL=ON
//Qt feature: direct2d (from target Qt6::Gui)
QT_FEATURE_direct2d:INTERNAL=OFF
//Qt feature: direct2d1_1 (from target Qt6::Gui)
QT_FEATURE_direct2d1_1:INTERNAL=OFF
//Qt feature: directfb (from target Qt6::Gui)
QT_FEATURE_directfb:INTERNAL=OFF
//Qt feature: directwrite (from target Qt6::Gui)
QT_FEATURE_directwrite:INTERNAL=OFF
//Qt feature: directwrite3 (from target Qt6::Gui)
QT_FEATURE_directwrite3:INTERNAL=OFF
//Qt feature: dladdr (from target Qt6::Core)
QT_FEATURE_dladdr:INTERNAL=ON
//Qt feature: dlopen (from target Qt6::Core)
QT_FEATURE_dlopen:INTERNAL=ON
//Qt feature: dnslookup (from target Qt6::Network)
QT_FEATURE_dnslookup:INTERNAL=ON
//Qt feature: dockwidget (from target Qt6::Widgets)
QT_FEATURE_dockwidget:INTERNAL=ON
//Qt feature: doubleconversion (from target Qt6::Core)
QT_FEATURE_doubleconversion:INTERNAL=ON
//Qt feature: draganddrop (from target Qt6::Gui)
QT_FEATURE_draganddrop:INTERNAL=ON
//Qt feature: drm_atomic (from target Qt6::Gui)
QT_FEATURE_drm_atomic:INTERNAL=ON
//Qt feature: dtls (from target Qt6::Network)
QT_FEATURE_dtls:INTERNAL=ON
//Qt feature: dynamicgl (from target Qt6::Gui)
QT_FEATURE_dynamicgl:INTERNAL=OFF
//Qt feature: easingcurve (from target Qt6::Core)
QT_FEATURE_easingcurve:INTERNAL=ON
//Qt feature: effects (from target Qt6::Widgets)
QT_FEATURE_effects:INTERNAL=ON
//Qt feature: egl (from target Qt6::Gui)
QT_FEATURE_egl:INTERNAL=ON
//Qt feature: egl_x11 (from target Qt6::Gui)
QT_FEATURE_egl_x11:INTERNAL=ON
//Qt feature: eglfs (from target Qt6::Gui)
QT_FEATURE_eglfs:INTERNAL=ON
//Qt feature: eglfs_brcm (from target Qt6::Gui)
QT_FEATURE_eglfs_brcm:INTERNAL=OFF
//Qt feature: eglfs_egldevice (from target Qt6::Gui)
QT_FEATURE_eglfs_egldevice:INTERNAL=ON
//Qt feature: eglfs_gbm (from target Qt6::Gui)
QT_FEATURE_eglfs_gbm:INTERNAL=ON
//Qt feature: eglfs_mali (from target Qt6::Gui)
QT_FEATURE_eglfs_mali:INTERNAL=OFF
//Qt feature: eglfs_openwfd (from target Qt6::Gui)
QT_FEATURE_eglfs_openwfd:INTERNAL=OFF
//Qt feature: eglfs_rcar (from target Qt6::Gui)
QT_FEATURE_eglfs_rcar:INTERNAL=OFF
//Qt feature: eglfs_viv (from target Qt6::Gui)
QT_FEATURE_eglfs_viv:INTERNAL=OFF
//Qt feature: eglfs_viv_wl (from target Qt6::Gui)
QT_FEATURE_eglfs_viv_wl:INTERNAL=OFF
//Qt feature: eglfs_vsp2 (from target Qt6::Gui)
QT_FEATURE_eglfs_vsp2:INTERNAL=OFF
//Qt feature: eglfs_x11 (from target Qt6::Gui)
QT_FEATURE_eglfs_x11:INTERNAL=ON
//Qt feature: elf_private_full_version (from target Qt6::Core)
QT_FEATURE_elf_private_full_version:INTERNAL=OFF
//Qt feature: enable_new_dtags (from target Qt6::Core)
QT_FEATURE_enable_new_dtags:INTERNAL=ON
//Qt feature: errormessage (from target Qt6::Widgets)
QT_FEATURE_errormessage:INTERNAL=ON
//Qt feature: etw (from target Qt6::Core)
QT_FEATURE_etw:INTERNAL=OFF
//Qt feature: evdev (from target Qt6::Gui)
QT_FEATURE_evdev:INTERNAL=ON
//Qt feature: evr (from target Qt6::Multimedia)
QT_FEATURE_evr:INTERNAL=OFF
//Qt feature: f16c (from target Qt6::Core)
QT_FEATURE_f16c:INTERNAL=ON
//Qt feature: ffmpeg (from target Qt6::Multimedia)
QT_FEATURE_ffmpeg:INTERNAL=ON
//Qt feature: filedialog (from target Qt6::Widgets)
QT_FEATURE_filedialog:INTERNAL=ON
//Qt feature: filesystemiterator (from target Qt6::Core)
QT_FEATURE_filesystemiterator:INTERNAL=ON
//Qt feature: filesystemmodel (from target Qt6::Gui)
QT_FEATURE_filesystemmodel:INTERNAL=ON
//Qt feature: filesystemwatcher (from target Qt6::Core)
QT_FEATURE_filesystemwatcher:INTERNAL=ON
//Qt feature: fontcombobox (from target Qt6::Widgets)
QT_FEATURE_fontcombobox:INTERNAL=ON
//Qt feature: fontconfig (from target Qt6::Gui)
QT_FEATURE_fontconfig:INTERNAL=ON
//Qt feature: fontdialog (from target Qt6::Widgets)
QT_FEATURE_fontdialog:INTERNAL=ON
//Qt feature: force_asserts (from target Qt6::Core)
QT_FEATURE_force_asserts:INTERNAL=OFF
//Qt feature: force_debug_info (from target Qt6::Core)
QT_FEATURE_force_debug_info:INTERNAL=ON
//Qt feature: forkfd_pidfd (from target Qt6::Core)
QT_FEATURE_forkfd_pidfd:INTERNAL=ON
//Qt feature: formlayout (from target Qt6::Widgets)
QT_FEATURE_formlayout:INTERNAL=ON
//Qt feature: framework (from target Qt6::Core)
QT_FEATURE_framework:INTERNAL=OFF
//Qt feature: freetype (from target Qt6::Gui)
QT_FEATURE_freetype:INTERNAL=ON
//Qt feature: fscompleter (from target Qt6::Widgets)
QT_FEATURE_fscompleter:INTERNAL=ON
//Qt feature: futimens (from target Qt6::Core)
QT_FEATURE_futimens:INTERNAL=ON
//Qt feature: future (from target Qt6::Core)
QT_FEATURE_future:INTERNAL=ON
//Qt feature: gbm (from target Qt6::Gui)
QT_FEATURE_gbm:INTERNAL=ON
//Qt feature: gc_binaries (from target Qt6::Core)
QT_FEATURE_gc_binaries:INTERNAL=OFF
//Qt feature: gestures (from target Qt6::Core)
QT_FEATURE_gestures:INTERNAL=ON
//Qt feature: getauxval (from target Qt6::Core)
QT_FEATURE_getauxval:INTERNAL=ON
//Qt feature: getentropy (from target Qt6::Core)
QT_FEATURE_getentropy:INTERNAL=ON
//Qt feature: getifaddrs (from target Qt6::Network)
QT_FEATURE_getifaddrs:INTERNAL=OFF
//Qt feature: gif (from target Qt6::Gui)
QT_FEATURE_gif:INTERNAL=ON
//Qt feature: glib (from target Qt6::Core)
QT_FEATURE_glib:INTERNAL=ON
//Qt feature: glibc_fortify_source (from target Qt6::Core)
QT_FEATURE_glibc_fortify_source:INTERNAL=ON
//Qt feature: gpu_vivante (from target Qt6::Multimedia)
QT_FEATURE_gpu_vivante:INTERNAL=OFF
//Qt feature: graphicseffect (from target Qt6::Widgets)
QT_FEATURE_graphicseffect:INTERNAL=ON
//Qt feature: graphicsframecapture (from target Qt6::Gui)
QT_FEATURE_graphicsframecapture:INTERNAL=OFF
//Qt feature: graphicsview (from target Qt6::Widgets)
QT_FEATURE_graphicsview:INTERNAL=ON
//Qt feature: groupbox (from target Qt6::Widgets)
QT_FEATURE_groupbox:INTERNAL=ON
//Qt feature: gssapi (from target Qt6::Network)
QT_FEATURE_gssapi:INTERNAL=ON
//Qt feature: gstreamer (from target Qt6::Multimedia)
QT_FEATURE_gstreamer:INTERNAL=OFF
//Qt feature: gstreamer_gl (from target Qt6::Multimedia)
QT_FEATURE_gstreamer_gl:INTERNAL=OFF
//Qt feature: gstreamer_gl_egl (from target Qt6::Multimedia)
QT_FEATURE_gstreamer_gl_egl:INTERNAL=OFF
//Qt feature: gstreamer_gl_wayland (from target Qt6::Multimedia)
QT_FEATURE_gstreamer_gl_wayland:INTERNAL=OFF
//Qt feature: gstreamer_gl_x11 (from target Qt6::Multimedia)
QT_FEATURE_gstreamer_gl_x11:INTERNAL=OFF
//Qt feature: gstreamer_photography (from target Qt6::Multimedia)
QT_FEATURE_gstreamer_photography:INTERNAL=OFF
//Qt feature: gtk3 (from target Qt6::Widgets)
QT_FEATURE_gtk3:INTERNAL=ON
//Qt feature: gui (from target Qt6::Core)
QT_FEATURE_gui:INTERNAL=ON
//Qt feature: harfbuzz (from target Qt6::Gui)
QT_FEATURE_harfbuzz:INTERNAL=ON
//Qt feature: highdpiscaling (from target Qt6::Gui)
QT_FEATURE_highdpiscaling:INTERNAL=ON
//Qt feature: hijricalendar (from target Qt6::Core)
QT_FEATURE_hijricalendar:INTERNAL=ON
//Qt feature: http (from target Qt6::Network)
QT_FEATURE_http:INTERNAL=ON
//Qt feature: ico (from target Qt6::Gui)
QT_FEATURE_ico:INTERNAL=ON
//Qt feature: icu (from target Qt6::Core)
QT_FEATURE_icu:INTERNAL=ON
//Qt feature: identityproxymodel (from target Qt6::Core)
QT_FEATURE_identityproxymodel:INTERNAL=ON
//Qt feature: im (from target Qt6::Gui)
QT_FEATURE_im:INTERNAL=ON
//Qt feature: image_heuristic_mask (from target Qt6::Gui)
QT_FEATURE_image_heuristic_mask:INTERNAL=ON
//Qt feature: image_text (from target Qt6::Gui)
QT_FEATURE_image_text:INTERNAL=ON
//Qt feature: imageformat_bmp (from target Qt6::Gui)
QT_FEATURE_imageformat_bmp:INTERNAL=ON
//Qt feature: imageformat_jpeg (from target Qt6::Gui)
QT_FEATURE_imageformat_jpeg:INTERNAL=ON
//Qt feature: imageformat_png (from target Qt6::Gui)
QT_FEATURE_imageformat_png:INTERNAL=ON
//Qt feature: imageformat_ppm (from target Qt6::Gui)
QT_FEATURE_imageformat_ppm:INTERNAL=ON
//Qt feature: imageformat_xbm (from target Qt6::Gui)
QT_FEATURE_imageformat_xbm:INTERNAL=ON
//Qt feature: imageformat_xpm (from target Qt6::Gui)
QT_FEATURE_imageformat_xpm:INTERNAL=ON
//Qt feature: imageformatplugin (from target Qt6::Gui)
QT_FEATURE_imageformatplugin:INTERNAL=ON
//Qt feature: imageio_text_loading (from target Qt6::Gui)
QT_FEATURE_imageio_text_loading:INTERNAL=ON
//Qt feature: inotify (from target Qt6::Core)
QT_FEATURE_inotify:INTERNAL=ON
//Qt feature: inputdialog (from target Qt6::Widgets)
QT_FEATURE_inputdialog:INTERNAL=ON
//Qt feature: integrityfb (from target Qt6::Gui)
QT_FEATURE_integrityfb:INTERNAL=OFF
//Qt feature: integrityhid (from target Qt6::Gui)
QT_FEATURE_integrityhid:INTERNAL=OFF
//Qt feature: intelcet (from target Qt6::Core)
QT_FEATURE_intelcet:INTERNAL=ON
//Qt feature: ipv6ifname (from target Qt6::Network)
QT_FEATURE_ipv6ifname:INTERNAL=OFF
//Qt feature: islamiccivilcalendar (from target Qt6::Core)
QT_FEATURE_islamiccivilcalendar:INTERNAL=ON
//Qt feature: itemmodel (from target Qt6::Core)
QT_FEATURE_itemmodel:INTERNAL=ON
//Qt feature: itemviews (from target Qt6::Widgets)
QT_FEATURE_itemviews:INTERNAL=ON
//Qt feature: jalalicalendar (from target Qt6::Core)
QT_FEATURE_jalalicalendar:INTERNAL=ON
//Qt feature: journald (from target Qt6::Core)
QT_FEATURE_journald:INTERNAL=OFF
//Qt feature: jpeg (from target Qt6::Gui)
QT_FEATURE_jpeg:INTERNAL=ON
//Qt feature: keysequenceedit (from target Qt6::Widgets)
QT_FEATURE_keysequenceedit:INTERNAL=ON
//Qt feature: kms (from target Qt6::Gui)
QT_FEATURE_kms:INTERNAL=ON
//Qt feature: label (from target Qt6::Widgets)
QT_FEATURE_label:INTERNAL=ON
//Qt feature: largefile (from target Qt6::Core)
QT_FEATURE_largefile:INTERNAL=ON
//Qt feature: lcdnumber (from target Qt6::Widgets)
QT_FEATURE_lcdnumber:INTERNAL=ON
//Qt feature: libcpp_hardening (from target Qt6::Core)
QT_FEATURE_libcpp_hardening:INTERNAL=OFF
//Qt feature: libinput (from target Qt6::Gui)
QT_FEATURE_libinput:INTERNAL=OFF
//Qt feature: libinput_axis_api (from target Qt6::Gui)
QT_FEATURE_libinput_axis_api:INTERNAL=OFF
//Qt feature: libinput_hires_wheel_support (from target Qt6::Gui)
QT_FEATURE_libinput_hires_wheel_support:INTERNAL=OFF
//Qt feature: libproxy (from target Qt6::Network)
QT_FEATURE_libproxy:INTERNAL=OFF
//Qt feature: library (from target Qt6::Core)
QT_FEATURE_library:INTERNAL=ON
//Qt feature: libresolv (from target Qt6::Network)
QT_FEATURE_libresolv:INTERNAL=ON
//Qt feature: libstdcpp_assertions (from target Qt6::Core)
QT_FEATURE_libstdcpp_assertions:INTERNAL=ON
//Qt feature: libudev (from target Qt6::Core)
QT_FEATURE_libudev:INTERNAL=OFF
//Qt feature: lineedit (from target Qt6::Widgets)
QT_FEATURE_lineedit:INTERNAL=ON
//Qt feature: linkat (from target Qt6::Core)
QT_FEATURE_linkat:INTERNAL=ON
//Qt feature: linux_dmabuf (from target Qt6::Multimedia)
QT_FEATURE_linux_dmabuf:INTERNAL=ON
//Qt feature: linux_netlink (from target Qt6::Network)
QT_FEATURE_linux_netlink:INTERNAL=ON
//Qt feature: linux_v4l (from target Qt6::Multimedia)
QT_FEATURE_linux_v4l:INTERNAL=ON
//Qt feature: linuxfb (from target Qt6::Gui)
QT_FEATURE_linuxfb:INTERNAL=ON
//Qt feature: listview (from target Qt6::Widgets)
QT_FEATURE_listview:INTERNAL=ON
//Qt feature: listwidget (from target Qt6::Widgets)
QT_FEATURE_listwidget:INTERNAL=ON
//Qt feature: localserver (from target Qt6::Network)
QT_FEATURE_localserver:INTERNAL=ON
//Qt feature: localtime_r (from target Qt6::Core)
QT_FEATURE_localtime_r:INTERNAL=ON
//Qt feature: localtime_s (from target Qt6::Core)
QT_FEATURE_localtime_s:INTERNAL=OFF
//Qt feature: lttng (from target Qt6::Core)
QT_FEATURE_lttng:INTERNAL=OFF
//Qt feature: mainwindow (from target Qt6::Widgets)
QT_FEATURE_mainwindow:INTERNAL=ON
//Qt feature: mdiarea (from target Qt6::Widgets)
QT_FEATURE_mdiarea:INTERNAL=ON
//Qt feature: memmem (from target Qt6::Core)
QT_FEATURE_memmem:INTERNAL=ON
//Qt feature: memrchr (from target Qt6::Core)
QT_FEATURE_memrchr:INTERNAL=ON
//Qt feature: menu (from target Qt6::Widgets)
QT_FEATURE_menu:INTERNAL=ON
//Qt feature: menubar (from target Qt6::Widgets)
QT_FEATURE_menubar:INTERNAL=ON
//Qt feature: messagebox (from target Qt6::Widgets)
QT_FEATURE_messagebox:INTERNAL=ON
//Qt feature: metal (from target Qt6::Gui)
QT_FEATURE_metal:INTERNAL=OFF
//Qt feature: mimetype (from target Qt6::Core)
QT_FEATURE_mimetype:INTERNAL=ON
//Qt feature: mimetype_database (from target Qt6::Core)
QT_FEATURE_mimetype_database:INTERNAL=ON
//Qt feature: mips_dsp (from target Qt6::Core)
QT_FEATURE_mips_dsp:INTERNAL=OFF
//Qt feature: mips_dspr2 (from target Qt6::Core)
QT_FEATURE_mips_dspr2:INTERNAL=OFF
//Qt feature: mmrenderer (from target Qt6::Multimedia)
QT_FEATURE_mmrenderer:INTERNAL=OFF
//Qt feature: movie (from target Qt6::Gui)
QT_FEATURE_movie:INTERNAL=ON
//Qt feature: mtdev (from target Qt6::Gui)
QT_FEATURE_mtdev:INTERNAL=OFF
//Qt feature: multiprocess (from target Qt6::Gui)
QT_FEATURE_multiprocess:INTERNAL=ON
//Qt feature: neon (from target Qt6::Core)
QT_FEATURE_neon:INTERNAL=OFF
//Qt feature: network (from target Qt6::Core)
QT_FEATURE_network:INTERNAL=ON
//Qt feature: networkdiskcache (from target Qt6::Network)
QT_FEATURE_networkdiskcache:INTERNAL=ON
//Qt feature: networkinterface (from target Qt6::Network)
QT_FEATURE_networkinterface:INTERNAL=ON
//Qt feature: networklistmanager (from target Qt6::Network)
QT_FEATURE_networklistmanager:INTERNAL=OFF
//Qt feature: networkproxy (from target Qt6::Network)
QT_FEATURE_networkproxy:INTERNAL=ON
//Qt feature: no_direct_extern_access (from target Qt6::Core)
QT_FEATURE_no_direct_extern_access:INTERNAL=OFF
//Qt feature: ocsp (from target Qt6::Network)
QT_FEATURE_ocsp:INTERNAL=ON
//Qt feature: opengl (from target Qt6::Gui)
QT_FEATURE_opengl:INTERNAL=ON
//Qt feature: opengles2 (from target Qt6::Gui)
QT_FEATURE_opengles2:INTERNAL=OFF
//Qt feature: opengles3 (from target Qt6::Gui)
QT_FEATURE_opengles3:INTERNAL=OFF
//Qt feature: opengles31 (from target Qt6::Gui)
QT_FEATURE_opengles31:INTERNAL=OFF
//Qt feature: opengles32 (from target Qt6::Gui)
QT_FEATURE_opengles32:INTERNAL=OFF
//Qt feature: opensles (from target Qt6::Multimedia)
QT_FEATURE_opensles:INTERNAL=OFF
//Qt feature: openssl (from target Qt6::Core)
QT_FEATURE_openssl:INTERNAL=ON
//Qt feature: openssl_hash (from target Qt6::Core)
QT_FEATURE_openssl_hash:INTERNAL=OFF
//Qt feature: openssl_linked (from target Qt6::Core)
QT_FEATURE_openssl_linked:INTERNAL=OFF
//Qt feature: opensslv11 (from target Qt6::Core)
QT_FEATURE_opensslv11:INTERNAL=OFF
//Qt feature: opensslv30 (from target Qt6::Core)
QT_FEATURE_opensslv30:INTERNAL=ON
//Qt feature: openvg (from target Qt6::Gui)
QT_FEATURE_openvg:INTERNAL=OFF
//Qt feature: pcre2 (from target Qt6::Core)
QT_FEATURE_pcre2:INTERNAL=ON
//Qt feature: pdf (from target Qt6::Gui)
QT_FEATURE_pdf:INTERNAL=ON
//Qt feature: permissions (from target Qt6::Core)
QT_FEATURE_permissions:INTERNAL=ON
//Qt feature: picture (from target Qt6::Gui)
QT_FEATURE_picture:INTERNAL=ON
//Qt feature: pipewire (from target Qt6::Multimedia)
QT_FEATURE_pipewire:INTERNAL=ON
//Qt feature: pkg_config (from target Qt6::Core)
QT_FEATURE_pkg_config:INTERNAL=ON
//Qt feature: plugin_manifest (from target Qt6::Core)
QT_FEATURE_plugin_manifest:INTERNAL=ON
//Qt feature: png (from target Qt6::Gui)
QT_FEATURE_png:INTERNAL=ON
//Qt feature: poll_exit_on_error (from target Qt6::Core)
QT_FEATURE_poll_exit_on_error:INTERNAL=OFF
//Qt feature: poll_poll (from target Qt6::Core)
QT_FEATURE_poll_poll:INTERNAL=OFF
//Qt feature: poll_pollts (from target Qt6::Core)
QT_FEATURE_poll_pollts:INTERNAL=OFF
//Qt feature: poll_ppoll (from target Qt6::Core)
QT_FEATURE_poll_ppoll:INTERNAL=ON
//Qt feature: poll_select (from target Qt6::Core)
QT_FEATURE_poll_select:INTERNAL=OFF
//Qt feature: posix_fallocate (from target Qt6::Core)
QT_FEATURE_posix_fallocate:INTERNAL=ON
//Qt feature: posix_sem (from target Qt6::Core)
QT_FEATURE_posix_sem:INTERNAL=ON
//Qt feature: posix_shm (from target Qt6::Core)
QT_FEATURE_posix_shm:INTERNAL=ON
//Qt feature: precompile_header (from target Qt6::Core)
QT_FEATURE_precompile_header:INTERNAL=ON
//Qt feature: printsupport (from target Qt6::Core)
QT_FEATURE_printsupport:INTERNAL=ON
//Qt feature: private_tests (from target Qt6::Core)
QT_FEATURE_private_tests:INTERNAL=OFF
//Qt feature: process (from target Qt6::Core)
QT_FEATURE_process:INTERNAL=ON
//Qt feature: processenvironment (from target Qt6::Core)
QT_FEATURE_processenvironment:INTERNAL=ON
//Qt feature: progressbar (from target Qt6::Widgets)
QT_FEATURE_progressbar:INTERNAL=ON
//Qt feature: progressdialog (from target Qt6::Widgets)
QT_FEATURE_progressdialog:INTERNAL=ON
//Qt feature: proxymodel (from target Qt6::Core)
QT_FEATURE_proxymodel:INTERNAL=ON
//Qt feature: publicsuffix_qt (from target Qt6::Network)
QT_FEATURE_publicsuffix_qt:INTERNAL=ON
//Qt feature: publicsuffix_system (from target Qt6::Network)
QT_FEATURE_publicsuffix_system:INTERNAL=ON
//Qt feature: pulseaudio (from target Qt6::Multimedia)
QT_FEATURE_pulseaudio:INTERNAL=ON
//Qt feature: pushbutton (from target Qt6::Widgets)
QT_FEATURE_pushbutton:INTERNAL=ON
//Qt feature: qml_animation (from target Qt6::Qml)
QT_FEATURE_qml_animation:INTERNAL=ON
//Qt feature: qml_debug (from target Qt6::Qml)
QT_FEATURE_qml_debug:INTERNAL=ON
//Qt feature: qml_delegate_model (from target Qt6::QmlModels)
QT_FEATURE_qml_delegate_model:INTERNAL=ON
//Qt feature: qml_itemmodel (from target Qt6::Qml)
QT_FEATURE_qml_itemmodel:INTERNAL=ON
//Qt feature: qml_jit (from target Qt6::Qml)
QT_FEATURE_qml_jit:INTERNAL=ON
//Qt feature: qml_list_model (from target Qt6::QmlModels)
QT_FEATURE_qml_list_model:INTERNAL=ON
//Qt feature: qml_locale (from target Qt6::Qml)
QT_FEATURE_qml_locale:INTERNAL=ON
//Qt feature: qml_network (from target Qt6::Qml)
QT_FEATURE_qml_network:INTERNAL=ON
//Qt feature: qml_object_model (from target Qt6::QmlModels)
QT_FEATURE_qml_object_model:INTERNAL=ON
//Qt feature: qml_preview (from target Qt6::Qml)
QT_FEATURE_qml_preview:INTERNAL=ON
//Qt feature: qml_profiler (from target Qt6::Qml)
QT_FEATURE_qml_profiler:INTERNAL=ON
//Qt feature: qml_python (from target Qt6::Qml)
QT_FEATURE_qml_python:INTERNAL=ON
//Qt feature: qml_ssl (from target Qt6::Qml)
QT_FEATURE_qml_ssl:INTERNAL=ON
//Qt feature: qml_table_model (from target Qt6::QmlModels)
QT_FEATURE_qml_table_model:INTERNAL=ON
//Qt feature: qml_worker_script (from target Qt6::Qml)
QT_FEATURE_qml_worker_script:INTERNAL=ON
//Qt feature: qml_xml_http_request (from target Qt6::Qml)
QT_FEATURE_qml_xml_http_request:INTERNAL=ON
//Qt feature: qml_xmllistmodel (from target Qt6::Qml)
QT_FEATURE_qml_xmllistmodel:INTERNAL=ON
//Qt feature: qqnx_imf (from target Qt6::Gui)
QT_FEATURE_qqnx_imf:INTERNAL=OFF
//Qt feature: qqnx_pps (from target Qt6::Core)
QT_FEATURE_qqnx_pps:INTERNAL=OFF
//Qt feature: quick_animatedimage (from target Qt6::Quick)
QT_FEATURE_quick_animatedimage:INTERNAL=ON
//Qt feature: quick_canvas (from target Qt6::Quick)
QT_FEATURE_quick_canvas:INTERNAL=ON
//Qt feature: quick_designer (from target Qt6::Quick)
QT_FEATURE_quick_designer:INTERNAL=ON
//Qt feature: quick_draganddrop (from target Qt6::Quick)
QT_FEATURE_quick_draganddrop:INTERNAL=ON
//Qt feature: quick_flipable (from target Qt6::Quick)
QT_FEATURE_quick_flipable:INTERNAL=ON
//Qt feature: quick_gridview (from target Qt6::Quick)
QT_FEATURE_quick_gridview:INTERNAL=ON
//Qt feature: quick_itemview (from target Qt6::Quick)
QT_FEATURE_quick_itemview:INTERNAL=ON
//Qt feature: quick_listview (from target Qt6::Quick)
QT_FEATURE_quick_listview:INTERNAL=ON
//Qt feature: quick_particles (from target Qt6::Quick)
QT_FEATURE_quick_particles:INTERNAL=ON
//Qt feature: quick_path (from target Qt6::Quick)
QT_FEATURE_quick_path:INTERNAL=ON
//Qt feature: quick_pathview (from target Qt6::Quick)
QT_FEATURE_quick_pathview:INTERNAL=ON
//Qt feature: quick_pixmap_cache_threaded_download (from target
// Qt6::Quick)
QT_FEATURE_quick_pixmap_cache_threaded_download:INTERNAL=ON
//Qt feature: quick_positioners (from target Qt6::Quick)
QT_FEATURE_quick_positioners:INTERNAL=ON
//Qt feature: quick_repeater (from target Qt6::Quick)
QT_FEATURE_quick_repeater:INTERNAL=ON
//Qt feature: quick_shadereffect (from target Qt6::Quick)
QT_FEATURE_quick_shadereffect:INTERNAL=ON
//Qt feature: quick_sprite (from target Qt6::Quick)
QT_FEATURE_quick_sprite:INTERNAL=ON
//Qt feature: quick_tableview (from target Qt6::Quick)
QT_FEATURE_quick_tableview:INTERNAL=ON
//Qt feature: quick_treeview (from target Qt6::Quick)
QT_FEATURE_quick_treeview:INTERNAL=ON
//Qt feature: quick_viewtransitions (from target Qt6::Quick)
QT_FEATURE_quick_viewtransitions:INTERNAL=ON
//Qt feature: radiobutton (from target Qt6::Widgets)
QT_FEATURE_radiobutton:INTERNAL=ON
//Qt feature: raster_64bit (from target Qt6::Gui)
QT_FEATURE_raster_64bit:INTERNAL=ON
//Qt feature: raster_fp (from target Qt6::Gui)
QT_FEATURE_raster_fp:INTERNAL=ON
//Qt feature: rdrnd (from target Qt6::Core)
QT_FEATURE_rdrnd:INTERNAL=ON
//Qt feature: rdseed (from target Qt6::Core)
QT_FEATURE_rdseed:INTERNAL=ON
//Qt feature: reduce_exports (from target Qt6::Core)
QT_FEATURE_reduce_exports:INTERNAL=ON
//Qt feature: reduce_relocations (from target Qt6::Core)
QT_FEATURE_reduce_relocations:INTERNAL=ON
//Qt feature: regularexpression (from target Qt6::Core)
QT_FEATURE_regularexpression:INTERNAL=ON
//Qt feature: relocatable (from target Qt6::Core)
QT_FEATURE_relocatable:INTERNAL=ON
//Qt feature: relro_now_linker (from target Qt6::Core)
QT_FEATURE_relro_now_linker:INTERNAL=ON
//Qt feature: renameat2 (from target Qt6::Core)
QT_FEATURE_renameat2:INTERNAL=ON
//Qt feature: res_setservers (from target Qt6::Network)
QT_FEATURE_res_setservers:INTERNAL=OFF
//Qt feature: resizehandler (from target Qt6::Widgets)
QT_FEATURE_resizehandler:INTERNAL=ON
//Qt feature: rpath (from target Qt6::Core)
QT_FEATURE_rpath:INTERNAL=ON
//Qt feature: rubberband (from target Qt6::Widgets)
QT_FEATURE_rubberband:INTERNAL=ON
//Qt feature: schannel (from target Qt6::Network)
QT_FEATURE_schannel:INTERNAL=OFF
//Qt feature: scrollarea (from target Qt6::Widgets)
QT_FEATURE_scrollarea:INTERNAL=ON
//Qt feature: scrollbar (from target Qt6::Widgets)
QT_FEATURE_scrollbar:INTERNAL=ON
//Qt feature: scroller (from target Qt6::Widgets)
QT_FEATURE_scroller:INTERNAL=ON
//Qt feature: sctp (from target Qt6::Network)
QT_FEATURE_sctp:INTERNAL=OFF
//Qt feature: securetransport (from target Qt6::Network)
QT_FEATURE_securetransport:INTERNAL=OFF
//Qt feature: separate_debug_info (from target Qt6::Core)
QT_FEATURE_separate_debug_info:INTERNAL=ON
//Qt feature: sessionmanager (from target Qt6::Gui)
QT_FEATURE_sessionmanager:INTERNAL=ON
//Qt feature: settings (from target Qt6::Core)
QT_FEATURE_settings:INTERNAL=ON
//Qt feature: sha3_fast (from target Qt6::Core)
QT_FEATURE_sha3_fast:INTERNAL=ON
//Qt feature: shani (from target Qt6::Core)
QT_FEATURE_shani:INTERNAL=ON
//Qt feature: shared (from target Qt6::Core)
QT_FEATURE_shared:INTERNAL=ON
//Qt feature: sharedmemory (from target Qt6::Core)
QT_FEATURE_sharedmemory:INTERNAL=ON
//Qt feature: shortcut (from target Qt6::Core)
QT_FEATURE_shortcut:INTERNAL=ON
//Qt feature: signaling_nan (from target Qt6::Core)
QT_FEATURE_signaling_nan:INTERNAL=ON
//Qt feature: simulator_and_device (from target Qt6::Core)
QT_FEATURE_simulator_and_device:INTERNAL=OFF
//Qt feature: sizegrip (from target Qt6::Widgets)
QT_FEATURE_sizegrip:INTERNAL=ON
//Qt feature: slider (from target Qt6::Widgets)
QT_FEATURE_slider:INTERNAL=ON
//Qt feature: slog2 (from target Qt6::Core)
QT_FEATURE_slog2:INTERNAL=OFF
//Qt feature: socks5 (from target Qt6::Network)
QT_FEATURE_socks5:INTERNAL=ON
//Qt feature: sortfilterproxymodel (from target Qt6::Core)
QT_FEATURE_sortfilterproxymodel:INTERNAL=ON
//Qt feature: spatialaudio (from target Qt6::Multimedia)
QT_FEATURE_spatialaudio:INTERNAL=ON
//Qt feature: spatialaudio_quick3d (from target Qt6::Multimedia)
QT_FEATURE_spatialaudio_quick3d:INTERNAL=ON
//Qt feature: spinbox (from target Qt6::Widgets)
QT_FEATURE_spinbox:INTERNAL=ON
//Qt feature: splashscreen (from target Qt6::Widgets)
QT_FEATURE_splashscreen:INTERNAL=ON
//Qt feature: splitter (from target Qt6::Widgets)
QT_FEATURE_splitter:INTERNAL=ON
//Qt feature: sql (from target Qt6::Core)
QT_FEATURE_sql:INTERNAL=ON
//Qt feature: sse2 (from target Qt6::Core)
QT_FEATURE_sse2:INTERNAL=ON
//Qt feature: sse3 (from target Qt6::Core)
QT_FEATURE_sse3:INTERNAL=ON
//Qt feature: sse4_1 (from target Qt6::Core)
QT_FEATURE_sse4_1:INTERNAL=ON
//Qt feature: sse4_2 (from target Qt6::Core)
QT_FEATURE_sse4_2:INTERNAL=ON
//Qt feature: ssl (from target Qt6::Network)
QT_FEATURE_ssl:INTERNAL=ON
//Qt feature: sspi (from target Qt6::Network)
QT_FEATURE_sspi:INTERNAL=OFF
//Qt feature: ssse3 (from target Qt6::Core)
QT_FEATURE_ssse3:INTERNAL=ON
//Qt feature: stack_clash_protection (from target Qt6::Core)
QT_FEATURE_stack_clash_protection:INTERNAL=ON
//Qt feature: stack_protector (from target Qt6::Core)
QT_FEATURE_stack_protector:INTERNAL=ON
//Qt feature: stackedwidget (from target Qt6::Widgets)
QT_FEATURE_stackedwidget:INTERNAL=ON
//Qt feature: standarditemmodel (from target Qt6::Gui)
QT_FEATURE_standarditemmodel:INTERNAL=ON
//Qt feature: static (from target Qt6::Core)
QT_FEATURE_static:INTERNAL=OFF
//Qt feature: statusbar (from target Qt6::Widgets)
QT_FEATURE_statusbar:INTERNAL=ON
//Qt feature: statustip (from target Qt6::Widgets)
QT_FEATURE_statustip:INTERNAL=ON
//Qt feature: std_atomic64 (from target Qt6::Core)
QT_FEATURE_std_atomic64:INTERNAL=ON
//Qt feature: stdlib_libcpp (from target Qt6::Core)
QT_FEATURE_stdlib_libcpp:INTERNAL=OFF
//Qt feature: stringlistmodel (from target Qt6::Core)
QT_FEATURE_stringlistmodel:INTERNAL=ON
//Qt feature: style_android (from target Qt6::Widgets)
QT_FEATURE_style_android:INTERNAL=OFF
//Qt feature: style_fusion (from target Qt6::Widgets)
QT_FEATURE_style_fusion:INTERNAL=ON
//Qt feature: style_mac (from target Qt6::Widgets)
QT_FEATURE_style_mac:INTERNAL=OFF
//Qt feature: style_stylesheet (from target Qt6::Widgets)
QT_FEATURE_style_stylesheet:INTERNAL=ON
//Qt feature: style_windows (from target Qt6::Widgets)
QT_FEATURE_style_windows:INTERNAL=ON
//Qt feature: style_windows11 (from target Qt6::Widgets)
QT_FEATURE_style_windows11:INTERNAL=OFF
//Qt feature: style_windowsvista (from target Qt6::Widgets)
QT_FEATURE_style_windowsvista:INTERNAL=OFF
//Qt feature: syntaxhighlighter (from target Qt6::Widgets)
QT_FEATURE_syntaxhighlighter:INTERNAL=ON
//Qt feature: syslog (from target Qt6::Core)
QT_FEATURE_syslog:INTERNAL=OFF
//Qt feature: system_doubleconversion (from target Qt6::Core)
QT_FEATURE_system_doubleconversion:INTERNAL=OFF
//Qt feature: system_freetype (from target Qt6::Gui)
QT_FEATURE_system_freetype:INTERNAL=ON
//Qt feature: system_harfbuzz (from target Qt6::Gui)
QT_FEATURE_system_harfbuzz:INTERNAL=OFF
//Qt feature: system_jpeg (from target Qt6::Gui)
QT_FEATURE_system_jpeg:INTERNAL=OFF
//Qt feature: system_libb2 (from target Qt6::Core)
QT_FEATURE_system_libb2:INTERNAL=OFF
//Qt feature: system_pcre2 (from target Qt6::Core)
QT_FEATURE_system_pcre2:INTERNAL=OFF
//Qt feature: system_png (from target Qt6::Gui)
QT_FEATURE_system_png:INTERNAL=OFF
//Qt feature: system_proxies (from target Qt6::Network)
QT_FEATURE_system_proxies:INTERNAL=ON
//Qt feature: system_textmarkdownreader (from target Qt6::Gui)
QT_FEATURE_system_textmarkdownreader:INTERNAL=OFF
//Qt feature: system_xcb_xinput (from target Qt6::Gui)
QT_FEATURE_system_xcb_xinput:INTERNAL=OFF
//Qt feature: system_zlib (from target Qt6::Core)
QT_FEATURE_system_zlib:INTERNAL=ON
//Qt feature: systemsemaphore (from target Qt6::Core)
QT_FEATURE_systemsemaphore:INTERNAL=ON
//Qt feature: systemtrayicon (from target Qt6::Gui)
QT_FEATURE_systemtrayicon:INTERNAL=ON
//Qt feature: sysv_sem (from target Qt6::Core)
QT_FEATURE_sysv_sem:INTERNAL=ON
//Qt feature: sysv_shm (from target Qt6::Core)
QT_FEATURE_sysv_shm:INTERNAL=ON
//Qt feature: tabbar (from target Qt6::Widgets)
QT_FEATURE_tabbar:INTERNAL=ON
//Qt feature: tabletevent (from target Qt6::Gui)
QT_FEATURE_tabletevent:INTERNAL=ON
//Qt feature: tableview (from target Qt6::Widgets)
QT_FEATURE_tableview:INTERNAL=ON
//Qt feature: tablewidget (from target Qt6::Widgets)
QT_FEATURE_tablewidget:INTERNAL=ON
//Qt feature: tabwidget (from target Qt6::Widgets)
QT_FEATURE_tabwidget:INTERNAL=ON
//Qt feature: temporaryfile (from target Qt6::Core)
QT_FEATURE_temporaryfile:INTERNAL=ON
//Qt feature: testlib (from target Qt6::Core)
QT_FEATURE_testlib:INTERNAL=ON
//Qt feature: textbrowser (from target Qt6::Widgets)
QT_FEATURE_textbrowser:INTERNAL=ON
//Qt feature: textdate (from target Qt6::Core)
QT_FEATURE_textdate:INTERNAL=ON
//Qt feature: textedit (from target Qt6::Widgets)
QT_FEATURE_textedit:INTERNAL=ON
//Qt feature: texthtmlparser (from target Qt6::Gui)
QT_FEATURE_texthtmlparser:INTERNAL=ON
//Qt feature: textmarkdownreader (from target Qt6::Gui)
QT_FEATURE_textmarkdownreader:INTERNAL=ON
//Qt feature: textmarkdownwriter (from target Qt6::Gui)
QT_FEATURE_textmarkdownwriter:INTERNAL=ON
//Qt feature: textodfwriter (from target Qt6::Gui)
QT_FEATURE_textodfwriter:INTERNAL=ON
//Qt feature: thread (from target Qt6::Core)
QT_FEATURE_thread:INTERNAL=ON
//Qt feature: threadsafe_cloexec (from target Qt6::Core)
QT_FEATURE_threadsafe_cloexec:INTERNAL=ON
//Qt feature: timezone (from target Qt6::Core)
QT_FEATURE_timezone:INTERNAL=ON
//Qt feature: timezone_locale (from target Qt6::Core)
QT_FEATURE_timezone_locale:INTERNAL=ON
//Qt feature: toolbar (from target Qt6::Widgets)
QT_FEATURE_toolbar:INTERNAL=ON
//Qt feature: toolbox (from target Qt6::Widgets)
QT_FEATURE_toolbox:INTERNAL=ON
//Qt feature: toolbutton (from target Qt6::Widgets)
QT_FEATURE_toolbutton:INTERNAL=ON
//Qt feature: tooltip (from target Qt6::Widgets)
QT_FEATURE_tooltip:INTERNAL=ON
//Qt feature: topleveldomain (from target Qt6::Network)
QT_FEATURE_topleveldomain:INTERNAL=ON
//Qt feature: translation (from target Qt6::Core)
QT_FEATURE_translation:INTERNAL=ON
//Qt feature: transposeproxymodel (from target Qt6::Core)
QT_FEATURE_transposeproxymodel:INTERNAL=ON
//Qt feature: treeview (from target Qt6::Widgets)
QT_FEATURE_treeview:INTERNAL=ON
//Qt feature: treewidget (from target Qt6::Widgets)
QT_FEATURE_treewidget:INTERNAL=ON
//Qt feature: trivial_auto_var_init_pattern (from target Qt6::Core)
QT_FEATURE_trivial_auto_var_init_pattern:INTERNAL=OFF
//Qt feature: tslib (from target Qt6::Gui)
QT_FEATURE_tslib:INTERNAL=OFF
//Qt feature: tuiotouch (from target Qt6::Gui)
QT_FEATURE_tuiotouch:INTERNAL=ON
//Qt feature: udpsocket (from target Qt6::Network)
QT_FEATURE_udpsocket:INTERNAL=ON
//Qt feature: undocommand (from target Qt6::Gui)
QT_FEATURE_undocommand:INTERNAL=ON
//Qt feature: undogroup (from target Qt6::Gui)
QT_FEATURE_undogroup:INTERNAL=ON
//Qt feature: undostack (from target Qt6::Gui)
QT_FEATURE_undostack:INTERNAL=ON
//Qt feature: undoview (from target Qt6::Widgets)
QT_FEATURE_undoview:INTERNAL=ON
//Qt feature: use_bfd_linker (from target Qt6::Core)
QT_FEATURE_use_bfd_linker:INTERNAL=OFF
//Qt feature: use_gold_linker (from target Qt6::Core)
QT_FEATURE_use_gold_linker:INTERNAL=OFF
//Qt feature: use_lld_linker (from target Qt6::Core)
QT_FEATURE_use_lld_linker:INTERNAL=OFF
//Qt feature: use_mold_linker (from target Qt6::Core)
QT_FEATURE_use_mold_linker:INTERNAL=OFF
//Qt feature: vaapi (from target Qt6::Multimedia)
QT_FEATURE_vaapi:INTERNAL=ON
//Qt feature: vaes (from target Qt6::Core)
QT_FEATURE_vaes:INTERNAL=ON
//Qt feature: validator (from target Qt6::Gui)
QT_FEATURE_validator:INTERNAL=ON
//Qt feature: version_tagging (from target Qt6::Core)
QT_FEATURE_version_tagging:INTERNAL=ON
//Qt feature: videotoolbox (from target Qt6::Multimedia)
QT_FEATURE_videotoolbox:INTERNAL=OFF
//Qt feature: vkgen (from target Qt6::Gui)
QT_FEATURE_vkgen:INTERNAL=ON
//Qt feature: vkkhrdisplay (from target Qt6::Gui)
QT_FEATURE_vkkhrdisplay:INTERNAL=ON
//Qt feature: vnc (from target Qt6::Gui)
QT_FEATURE_vnc:INTERNAL=ON
//Qt feature: vsp2 (from target Qt6::Gui)
QT_FEATURE_vsp2:INTERNAL=OFF
//Qt feature: vulkan (from target Qt6::Gui)
QT_FEATURE_vulkan:INTERNAL=ON
//Qt feature: wasm (from target Qt6::Multimedia)
QT_FEATURE_wasm:INTERNAL=OFF
//Qt feature: wasm_exceptions (from target Qt6::Core)
QT_FEATURE_wasm_exceptions:INTERNAL=OFF
//Qt feature: wasm_simd128 (from target Qt6::Core)
QT_FEATURE_wasm_simd128:INTERNAL=OFF
//Qt feature: wayland (from target Qt6::Gui)
QT_FEATURE_wayland:INTERNAL=ON
//Qt feature: whatsthis (from target Qt6::Gui)
QT_FEATURE_whatsthis:INTERNAL=ON
//Qt feature: wheelevent (from target Qt6::Gui)
QT_FEATURE_wheelevent:INTERNAL=ON
//Qt feature: widgets (from target Qt6::Core)
QT_FEATURE_widgets:INTERNAL=ON
//Qt feature: widgettextcontrol (from target Qt6::Widgets)
QT_FEATURE_widgettextcontrol:INTERNAL=ON
//Qt feature: wizard (from target Qt6::Widgets)
QT_FEATURE_wizard:INTERNAL=ON
//Qt feature: wmf (from target Qt6::Multimedia)
QT_FEATURE_wmf:INTERNAL=OFF
//Qt feature: wmsdk (from target Qt6::Multimedia)
QT_FEATURE_wmsdk:INTERNAL=OFF
//Qt feature: x86intrin (from target Qt6::Core)
QT_FEATURE_x86intrin:INTERNAL=ON
//Qt feature: xcb (from target Qt6::Gui)
QT_FEATURE_xcb:INTERNAL=ON
//Qt feature: xcb_egl_plugin (from target Qt6::Gui)
QT_FEATURE_xcb_egl_plugin:INTERNAL=ON
//Qt feature: xcb_glx (from target Qt6::Gui)
QT_FEATURE_xcb_glx:INTERNAL=ON
//Qt feature: xcb_glx_plugin (from target Qt6::Gui)
QT_FEATURE_xcb_glx_plugin:INTERNAL=ON
//Qt feature: xcb_native_painting (from target Qt6::Gui)
QT_FEATURE_xcb_native_painting:INTERNAL=OFF
//Qt feature: xcb_sm (from target Qt6::Gui)
QT_FEATURE_xcb_sm:INTERNAL=OFF
//Qt feature: xcb_xlib (from target Qt6::Gui)
QT_FEATURE_xcb_xlib:INTERNAL=ON
//Qt feature: xkbcommon (from target Qt6::Gui)
QT_FEATURE_xkbcommon:INTERNAL=ON
//Qt feature: xkbcommon_x11 (from target Qt6::Gui)
QT_FEATURE_xkbcommon_x11:INTERNAL=ON
//Qt feature: xlib (from target Qt6::Gui)
QT_FEATURE_xlib:INTERNAL=ON
//Qt feature: xml (from target Qt6::Core)
QT_FEATURE_xml:INTERNAL=ON
//Qt feature: xmlstream (from target Qt6::Core)
QT_FEATURE_xmlstream:INTERNAL=ON
//Qt feature: xmlstreamreader (from target Qt6::Core)
QT_FEATURE_xmlstreamreader:INTERNAL=ON
//Qt feature: xmlstreamwriter (from target Qt6::Core)
QT_FEATURE_xmlstreamwriter:INTERNAL=ON
//Qt feature: xrender (from target Qt6::Gui)
QT_FEATURE_xrender:INTERNAL=OFF
//Qt feature: zstd (from target Qt6::Core)
QT_FEATURE_zstd:INTERNAL=ON
//ADVANCED property for variable: Vulkan_GLSLANG_VALIDATOR_EXECUTABLE
Vulkan_GLSLANG_VALIDATOR_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Vulkan_GLSLC_EXECUTABLE
Vulkan_GLSLC_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Vulkan_INCLUDE_DIR
Vulkan_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Vulkan_LIBRARY
Vulkan_LIBRARY-ADVANCED:INTERNAL=1
//linker supports push/pop state
_CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED:INTERNAL=TRUE
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/usr/local
__pkg_config_arguments_PKG_XKB:INTERNAL=QUIET;xkbcommon
__pkg_config_arguments_libavcodec:INTERNAL=REQUIRED;IMPORTED_TARGET;libavcodec
__pkg_config_arguments_libavfilter:INTERNAL=REQUIRED;IMPORTED_TARGET;libavfilter
__pkg_config_arguments_libavformat:INTERNAL=REQUIRED;IMPORTED_TARGET;libavformat
__pkg_config_arguments_libavutil:INTERNAL=REQUIRED;IMPORTED_TARGET;libavutil
__pkg_config_arguments_libswresample:INTERNAL=REQUIRED;IMPORTED_TARGET;libswresample
__pkg_config_arguments_libswscale:INTERNAL=REQUIRED;IMPORTED_TARGET;libswscale
__pkg_config_checked_PKG_XKB:INTERNAL=1
__pkg_config_checked_libavcodec:INTERNAL=1
__pkg_config_checked_libavfilter:INTERNAL=1
__pkg_config_checked_libavformat:INTERNAL=1
__pkg_config_checked_libavutil:INTERNAL=1
__pkg_config_checked_libswresample:INTERNAL=1
__pkg_config_checked_libswscale:INTERNAL=1
__qt_qml_macros_module_base_dir:INTERNAL=/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml
libavcodec_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
libavcodec_CFLAGS_I:INTERNAL=
libavcodec_CFLAGS_OTHER:INTERNAL=
libavcodec_FOUND:INTERNAL=1
libavcodec_INCLUDEDIR:INTERNAL=/usr/include/x86_64-linux-gnu
libavcodec_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
libavcodec_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lavcodec
libavcodec_LDFLAGS_OTHER:INTERNAL=
libavcodec_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
libavcodec_LIBRARIES:INTERNAL=avcodec
libavcodec_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
libavcodec_LIBS:INTERNAL=
libavcodec_LIBS_L:INTERNAL=
libavcodec_LIBS_OTHER:INTERNAL=
libavcodec_LIBS_PATHS:INTERNAL=
libavcodec_MODULE_NAME:INTERNAL=libavcodec
libavcodec_PREFIX:INTERNAL=/usr
libavcodec_STATIC_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
libavcodec_STATIC_CFLAGS_I:INTERNAL=
libavcodec_STATIC_CFLAGS_OTHER:INTERNAL=
libavcodec_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
libavcodec_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lavcodec;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lwebpmux;-pthread;-lm;-latomic;-llzma;-ldav1d;-lrsvg-2;-lm;-lgio-2.0;-lgdk_pixbuf-2.0;-lgobject-2.0;-lglib-2.0;-lcairo;-lzvbi;-lpthread;-lm;-lpng;-lz;-lsnappy;-lstdc++;-laom;-lcodec2;-lgsm;-ljxl;-ljxl_threads;-lmp3lame;-lm;-lopenjp2;-lopus;-lrav1e;-lm;-lshine;-lspeex;-lSvtAv1Enc;-ltheoraenc;-ltheoradec;-logg;-ltwolame;-lvorbis;-lvorbisenc;-lwebp;-lx264;-lx265;-lxvidcore;-lz;-lva;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-L/usr/lib/x86_64-linux-gnu;-lswresample;-lm;-lsoxr;-latomic;-L/usr/lib/x86_64-linux-gnu;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11
libavcodec_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread;-pthread;-pthread
libavcodec_STATIC_LIBDIR:INTERNAL=
libavcodec_STATIC_LIBRARIES:INTERNAL=avcodec;vpx;m;vpx;m;vpx;m;vpx;m;webpmux;m;atomic;lzma;dav1d;rsvg-2;m;gio-2.0;gdk_pixbuf-2.0;gobject-2.0;glib-2.0;cairo;zvbi;pthread;m;png;z;snappy;stdc++;aom;codec2;gsm;jxl;jxl_threads;mp3lame;m;openjp2;opus;rav1e;m;shine;speex;SvtAv1Enc;theoraenc;theoradec;ogg;twolame;vorbis;vorbisenc;webp;x264;x265;xvidcore;z;va;vpl;dl;stdc++;swresample;m;soxr;atomic;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11
libavcodec_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu/pkgconfig/../
libavcodec_STATIC_LIBS:INTERNAL=
libavcodec_STATIC_LIBS_L:INTERNAL=
libavcodec_STATIC_LIBS_OTHER:INTERNAL=
libavcodec_STATIC_LIBS_PATHS:INTERNAL=
libavcodec_VERSION:INTERNAL=60.31.102
libavcodec_libavcodec_INCLUDEDIR:INTERNAL=
libavcodec_libavcodec_LIBDIR:INTERNAL=
libavcodec_libavcodec_PREFIX:INTERNAL=
libavcodec_libavcodec_VERSION:INTERNAL=
libavfilter_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
libavfilter_CFLAGS_I:INTERNAL=
libavfilter_CFLAGS_OTHER:INTERNAL=
libavfilter_FOUND:INTERNAL=1
libavfilter_INCLUDEDIR:INTERNAL=/usr/include/x86_64-linux-gnu
libavfilter_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
libavfilter_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lavfilter
libavfilter_LDFLAGS_OTHER:INTERNAL=
libavfilter_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
libavfilter_LIBRARIES:INTERNAL=avfilter
libavfilter_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
libavfilter_LIBS:INTERNAL=
libavfilter_LIBS_L:INTERNAL=
libavfilter_LIBS_OTHER:INTERNAL=
libavfilter_LIBS_PATHS:INTERNAL=
libavfilter_MODULE_NAME:INTERNAL=libavfilter
libavfilter_PREFIX:INTERNAL=/usr
libavfilter_STATIC_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
libavfilter_STATIC_CFLAGS_I:INTERNAL=
libavfilter_STATIC_CFLAGS_OTHER:INTERNAL=
libavfilter_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
libavfilter_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lavfilter;-pthread;-lm;-latomic;-lpocketsphinx;-lsphinxbase;-lsphinxad;-lbs2b;-llilv-0;-lrubberband;-lfftw3;-lsamplerate;-lstdc++;-lmysofa;-lflite_cmu_time_awb;-lflite_cmu_us_awb;-lflite_cmu_us_kal;-lflite_cmu_us_kal16;-lflite_cmu_us_rms;-lflite_cmu_us_slt;-lflite_usenglish;-lflite_cmulex;-lflite;-lharfbuzz;-lfribidi;-lplacebo;-lass;-lva;-lvidstab;-lm;-lgomp;-lzmq;-lzimg;-lglslang;-lMachineIndependent;-lGenericCodeGen;-lSPVRemapper;-lSPIRV;-lSPIRV-Tools-opt;-lSPIRV-Tools;-lpthread;-lstdc++;-lm;-lOpenCL;-lfontconfig;-lfreetype;-lfreetype;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-L/usr/lib/x86_64-linux-gnu;-lswscale;-lm;-latomic;-L/usr/lib/x86_64-linux-gnu;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11;-lpostproc;-lm;-latomic;-L/usr/lib/x86_64-linux-gnu;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11;-lavformat;-lm;-latomic;-lxml2;-lbz2;-lgme;-lopenmpt;-lstdc++;-lchromaprint;-lbluray;-lz;-lgnutls;-lrabbitmq;-lrist;-lsrt-gnutls;-lssh;-lzmq;-L/usr/lib/x86_64-linux-gnu;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lwebpmux;-pthread;-lm;-latomic;-llzma;-ldav1d;-lrsvg-2;-lm;-lgio-2.0;-lgdk_pixbuf-2.0;-lgobject-2.0;-lglib-2.0;-lcairo;-lzvbi;-lpthread;-lm;-lpng;-lz;-lsnappy;-lstdc++;-laom;-lcodec2;-lgsm;-ljxl;-ljxl_threads;-lmp3lame;-lm;-lopenjp2;-lopus;-lrav1e;-lm;-lshine;-lspeex;-lSvtAv1Enc;-ltheoraenc;-ltheoradec;-logg;-ltwolame;-lvorbis;-lvorbisenc;-lwebp;-lx264;-lx265;-lxvidcore;-lz;-lva;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-L/usr/lib/x86_64-linux-gnu;-lm;-lsoxr;-latomic;-L/usr/lib/x86_64-linux-gnu;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11;-lswresample;-lm;-lsoxr;-latomic;-L/usr/lib/x86_64-linux-gnu;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11;-lavcodec;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lwebpmux;-pthread;-lm;-latomic;-llzma;-ldav1d;-lrsvg-2;-lm;-lgio-2.0;-lgdk_pixbuf-2.0;-lgobject-2.0;-lglib-2.0;-lcairo;-lzvbi;-lpthread;-lm;-lpng;-lz;-lsnappy;-lstdc++;-laom;-lcodec2;-lgsm;-ljxl;-ljxl_threads;-lmp3lame;-lm;-lopenjp2;-lopus;-lrav1e;-lm;-lshine;-lspeex;-lSvtAv1Enc;-ltheoraenc;-ltheoradec;-logg;-ltwolame;-lvorbis;-lvorbisenc;-lwebp;-lx264;-lx265;-lxvidcore;-lz;-lva;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-L/usr/lib/x86_64-linux-gnu;-lm;-lsoxr;-latomic;-L/usr/lib/x86_64-linux-gnu;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11;-lswresample;-lm;-lsoxr;-latomic;-L/usr/lib/x86_64-linux-gnu;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11
libavfilter_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread;-pthread;-pthread;-pthread;-pthread;-pthread;-pthread;-pthread;-pthread;-pthread;-pthread;-pthread;-pthread
libavfilter_STATIC_LIBDIR:INTERNAL=
libavfilter_STATIC_LIBRARIES:INTERNAL=avfilter;m;atomic;pocketsphinx;sphinxbase;sphinxad;bs2b;lilv-0;rubberband;fftw3;samplerate;stdc++;mysofa;flite_cmu_time_awb;flite_cmu_us_awb;flite_cmu_us_kal;flite_cmu_us_kal16;flite_cmu_us_rms;flite_cmu_us_slt;flite_usenglish;flite_cmulex;flite;harfbuzz;fribidi;placebo;ass;va;vidstab;m;gomp;zmq;zimg;glslang;MachineIndependent;GenericCodeGen;SPVRemapper;SPIRV;SPIRV-Tools-opt;SPIRV-Tools;pthread;stdc++;m;OpenCL;fontconfig;freetype;freetype;vpl;dl;stdc++;swscale;m;atomic;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11;postproc;m;atomic;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11;avformat;m;atomic;xml2;bz2;gme;openmpt;stdc++;chromaprint;bluray;z;gnutls;rabbitmq;rist;srt-gnutls;ssh;zmq;vpx;m;vpx;m;vpx;m;vpx;m;webpmux;m;atomic;lzma;dav1d;rsvg-2;m;gio-2.0;gdk_pixbuf-2.0;gobject-2.0;glib-2.0;cairo;zvbi;pthread;m;png;z;snappy;stdc++;aom;codec2;gsm;jxl;jxl_threads;mp3lame;m;openjp2;opus;rav1e;m;shine;speex;SvtAv1Enc;theoraenc;theoradec;ogg;twolame;vorbis;vorbisenc;webp;x264;x265;xvidcore;z;va;vpl;dl;stdc++;m;soxr;atomic;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11;swresample;m;soxr;atomic;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11;avcodec;vpx;m;vpx;m;vpx;m;vpx;m;webpmux;m;atomic;lzma;dav1d;rsvg-2;m;gio-2.0;gdk_pixbuf-2.0;gobject-2.0;glib-2.0;cairo;zvbi;pthread;m;png;z;snappy;stdc++;aom;codec2;gsm;jxl;jxl_threads;mp3lame;m;openjp2;opus;rav1e;m;shine;speex;SvtAv1Enc;theoraenc;theoradec;ogg;twolame;vorbis;vorbisenc;webp;x264;x265;xvidcore;z;va;vpl;dl;stdc++;m;soxr;atomic;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11;swresample;m;soxr;atomic;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11
libavfilter_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu/pkgconfig/../
libavfilter_STATIC_LIBS:INTERNAL=
libavfilter_STATIC_LIBS_L:INTERNAL=
libavfilter_STATIC_LIBS_OTHER:INTERNAL=
libavfilter_STATIC_LIBS_PATHS:INTERNAL=
libavfilter_VERSION:INTERNAL=9.12.100
libavfilter_libavfilter_INCLUDEDIR:INTERNAL=
libavfilter_libavfilter_LIBDIR:INTERNAL=
libavfilter_libavfilter_PREFIX:INTERNAL=
libavfilter_libavfilter_VERSION:INTERNAL=
libavformat_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
libavformat_CFLAGS_I:INTERNAL=
libavformat_CFLAGS_OTHER:INTERNAL=
libavformat_FOUND:INTERNAL=1
libavformat_INCLUDEDIR:INTERNAL=/usr/include/x86_64-linux-gnu
libavformat_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
libavformat_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lavformat
libavformat_LDFLAGS_OTHER:INTERNAL=
libavformat_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
libavformat_LIBRARIES:INTERNAL=avformat
libavformat_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
libavformat_LIBS:INTERNAL=
libavformat_LIBS_L:INTERNAL=
libavformat_LIBS_OTHER:INTERNAL=
libavformat_LIBS_PATHS:INTERNAL=
libavformat_MODULE_NAME:INTERNAL=libavformat
libavformat_PREFIX:INTERNAL=/usr
libavformat_STATIC_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
libavformat_STATIC_CFLAGS_I:INTERNAL=
libavformat_STATIC_CFLAGS_OTHER:INTERNAL=
libavformat_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
libavformat_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lavformat;-lm;-latomic;-lxml2;-lbz2;-lgme;-lopenmpt;-lstdc++;-lchromaprint;-lbluray;-lz;-lgnutls;-lrabbitmq;-lrist;-lsrt-gnutls;-lssh;-lzmq;-L/usr/lib/x86_64-linux-gnu;-lavcodec;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lwebpmux;-pthread;-lm;-latomic;-llzma;-ldav1d;-lrsvg-2;-lm;-lgio-2.0;-lgdk_pixbuf-2.0;-lgobject-2.0;-lglib-2.0;-lcairo;-lzvbi;-lpthread;-lm;-lpng;-lz;-lsnappy;-lstdc++;-laom;-lcodec2;-lgsm;-ljxl;-ljxl_threads;-lmp3lame;-lm;-lopenjp2;-lopus;-lrav1e;-lm;-lshine;-lspeex;-lSvtAv1Enc;-ltheoraenc;-ltheoradec;-logg;-ltwolame;-lvorbis;-lvorbisenc;-lwebp;-lx264;-lx265;-lxvidcore;-lz;-lva;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-L/usr/lib/x86_64-linux-gnu;-lm;-lsoxr;-latomic;-L/usr/lib/x86_64-linux-gnu;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11;-lswresample;-lm;-lsoxr;-latomic;-L/usr/lib/x86_64-linux-gnu;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11
libavformat_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread;-pthread;-pthread;-pthread;-pthread
libavformat_STATIC_LIBDIR:INTERNAL=
libavformat_STATIC_LIBRARIES:INTERNAL=avformat;m;atomic;xml2;bz2;gme;openmpt;stdc++;chromaprint;bluray;z;gnutls;rabbitmq;rist;srt-gnutls;ssh;zmq;avcodec;vpx;m;vpx;m;vpx;m;vpx;m;webpmux;m;atomic;lzma;dav1d;rsvg-2;m;gio-2.0;gdk_pixbuf-2.0;gobject-2.0;glib-2.0;cairo;zvbi;pthread;m;png;z;snappy;stdc++;aom;codec2;gsm;jxl;jxl_threads;mp3lame;m;openjp2;opus;rav1e;m;shine;speex;SvtAv1Enc;theoraenc;theoradec;ogg;twolame;vorbis;vorbisenc;webp;x264;x265;xvidcore;z;va;vpl;dl;stdc++;m;soxr;atomic;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11;swresample;m;soxr;atomic;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11
libavformat_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../;/usr/lib/x86_64-linux-gnu/pkgconfig/../
libavformat_STATIC_LIBS:INTERNAL=
libavformat_STATIC_LIBS_L:INTERNAL=
libavformat_STATIC_LIBS_OTHER:INTERNAL=
libavformat_STATIC_LIBS_PATHS:INTERNAL=
libavformat_VERSION:INTERNAL=60.16.100
libavformat_libavformat_INCLUDEDIR:INTERNAL=
libavformat_libavformat_LIBDIR:INTERNAL=
libavformat_libavformat_PREFIX:INTERNAL=
libavformat_libavformat_VERSION:INTERNAL=
libavutil_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
libavutil_CFLAGS_I:INTERNAL=
libavutil_CFLAGS_OTHER:INTERNAL=
libavutil_FOUND:INTERNAL=1
libavutil_INCLUDEDIR:INTERNAL=/usr/include/x86_64-linux-gnu
libavutil_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
libavutil_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lavutil
libavutil_LDFLAGS_OTHER:INTERNAL=
libavutil_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
libavutil_LIBRARIES:INTERNAL=avutil
libavutil_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
libavutil_LIBS:INTERNAL=
libavutil_LIBS_L:INTERNAL=
libavutil_LIBS_OTHER:INTERNAL=
libavutil_LIBS_PATHS:INTERNAL=
libavutil_MODULE_NAME:INTERNAL=libavutil
libavutil_PREFIX:INTERNAL=/usr
libavutil_STATIC_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
libavutil_STATIC_CFLAGS_I:INTERNAL=
libavutil_STATIC_CFLAGS_OTHER:INTERNAL=
libavutil_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
libavutil_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11
libavutil_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
libavutil_STATIC_LIBDIR:INTERNAL=
libavutil_STATIC_LIBRARIES:INTERNAL=avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11
libavutil_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../
libavutil_STATIC_LIBS:INTERNAL=
libavutil_STATIC_LIBS_L:INTERNAL=
libavutil_STATIC_LIBS_OTHER:INTERNAL=
libavutil_STATIC_LIBS_PATHS:INTERNAL=
libavutil_VERSION:INTERNAL=58.29.100
libavutil_libavutil_INCLUDEDIR:INTERNAL=
libavutil_libavutil_LIBDIR:INTERNAL=
libavutil_libavutil_PREFIX:INTERNAL=
libavutil_libavutil_VERSION:INTERNAL=
libswresample_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
libswresample_CFLAGS_I:INTERNAL=
libswresample_CFLAGS_OTHER:INTERNAL=
libswresample_FOUND:INTERNAL=1
libswresample_INCLUDEDIR:INTERNAL=/usr/include/x86_64-linux-gnu
libswresample_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
libswresample_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lswresample
libswresample_LDFLAGS_OTHER:INTERNAL=
libswresample_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
libswresample_LIBRARIES:INTERNAL=swresample
libswresample_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
libswresample_LIBS:INTERNAL=
libswresample_LIBS_L:INTERNAL=
libswresample_LIBS_OTHER:INTERNAL=
libswresample_LIBS_PATHS:INTERNAL=
libswresample_MODULE_NAME:INTERNAL=libswresample
libswresample_PREFIX:INTERNAL=/usr
libswresample_STATIC_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
libswresample_STATIC_CFLAGS_I:INTERNAL=
libswresample_STATIC_CFLAGS_OTHER:INTERNAL=
libswresample_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
libswresample_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lswresample;-lm;-lsoxr;-latomic;-L/usr/lib/x86_64-linux-gnu;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11
libswresample_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
libswresample_STATIC_LIBDIR:INTERNAL=
libswresample_STATIC_LIBRARIES:INTERNAL=swresample;m;soxr;atomic;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11
libswresample_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../
libswresample_STATIC_LIBS:INTERNAL=
libswresample_STATIC_LIBS_L:INTERNAL=
libswresample_STATIC_LIBS_OTHER:INTERNAL=
libswresample_STATIC_LIBS_PATHS:INTERNAL=
libswresample_VERSION:INTERNAL=4.12.100
libswresample_libswresample_INCLUDEDIR:INTERNAL=
libswresample_libswresample_LIBDIR:INTERNAL=
libswresample_libswresample_PREFIX:INTERNAL=
libswresample_libswresample_VERSION:INTERNAL=
libswscale_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
libswscale_CFLAGS_I:INTERNAL=
libswscale_CFLAGS_OTHER:INTERNAL=
libswscale_FOUND:INTERNAL=1
libswscale_INCLUDEDIR:INTERNAL=/usr/include/x86_64-linux-gnu
libswscale_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
libswscale_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lswscale
libswscale_LDFLAGS_OTHER:INTERNAL=
libswscale_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
libswscale_LIBRARIES:INTERNAL=swscale
libswscale_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
libswscale_LIBS:INTERNAL=
libswscale_LIBS_L:INTERNAL=
libswscale_LIBS_OTHER:INTERNAL=
libswscale_LIBS_PATHS:INTERNAL=
libswscale_MODULE_NAME:INTERNAL=libswscale
libswscale_PREFIX:INTERNAL=/usr
libswscale_STATIC_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
libswscale_STATIC_CFLAGS_I:INTERNAL=
libswscale_STATIC_CFLAGS_OTHER:INTERNAL=
libswscale_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
libswscale_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lswscale;-lm;-latomic;-L/usr/lib/x86_64-linux-gnu;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-L/usr/lib/x86_64-linux-gnu/pkgconfig/../;-lvpl;-ldl;-lstdc++;-lOpenCL;-lva;-latomic;-lX11
libswscale_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
libswscale_STATIC_LIBDIR:INTERNAL=
libswscale_STATIC_LIBRARIES:INTERNAL=swscale;m;atomic;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;vpl;dl;stdc++;OpenCL;va;atomic;X11
libswscale_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/pkgconfig/../
libswscale_STATIC_LIBS:INTERNAL=
libswscale_STATIC_LIBS_L:INTERNAL=
libswscale_STATIC_LIBS_OTHER:INTERNAL=
libswscale_STATIC_LIBS_PATHS:INTERNAL=
libswscale_VERSION:INTERNAL=7.5.100
libswscale_libswscale_INCLUDEDIR:INTERNAL=
libswscale_libswscale_LIBDIR:INTERNAL=
libswscale_libswscale_PREFIX:INTERNAL=
libswscale_libswscale_VERSION:INTERNAL=
//ADVANCED property for variable: pkgcfg_lib_PKG_XKB_xkbcommon
pkgcfg_lib_PKG_XKB_xkbcommon-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_libavcodec_avcodec
pkgcfg_lib_libavcodec_avcodec-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_libavfilter_avfilter
pkgcfg_lib_libavfilter_avfilter-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_libavformat_avformat
pkgcfg_lib_libavformat_avformat-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_libavutil_avutil
pkgcfg_lib_libavutil_avutil-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_libswresample_swresample
pkgcfg_lib_libswresample_swresample-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_libswscale_swscale
pkgcfg_lib_libswscale_swscale-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib/x86_64-linux-gnu

