[{"classes": [{"className": "MainWindow", "lineNumber": 23, "object": true, "qualifiedClassName": "MainWindow", "slots": [{"access": "private", "index": 0, "name": "on_findNDIbutton_clicked", "returnType": "void"}, {"access": "private", "index": 1, "name": "on_addND<PERSON>utton_clicked", "returnType": "void"}, {"access": "private", "index": 2, "name": "on_addSinkButton_clicked", "returnType": "void"}, {"access": "private", "index": 3, "name": "on_connectButton_clicked", "returnType": "void"}, {"access": "private", "index": 4, "name": "on_disconnectButton_clicked", "returnType": "void"}, {"access": "private", "index": 5, "name": "on_addSDIButton_clicked", "returnType": "void"}, {"access": "private", "index": 6, "name": "on_addSH<PERSON>utton_clicked", "returnType": "void"}, {"access": "private", "index": 7, "name": "on_removeInputButton_clicked", "returnType": "void"}, {"access": "private", "index": 8, "name": "on_addNDIOutButton_clicked", "returnType": "void"}, {"access": "private", "index": 9, "name": "on_addSDIOutButton_clicked", "returnType": "void"}, {"access": "private", "index": 10, "name": "on_addSHMOutButton_clicked", "returnType": "void"}, {"access": "private", "index": 11, "name": "on_setOutFormatButton_clicked", "returnType": "void"}, {"access": "private", "index": 12, "name": "on_addMID<PERSON>utton_clicked", "returnType": "void"}, {"access": "private", "index": 13, "name": "on_addPlayerDirectorB<PERSON>on_clicked", "returnType": "void"}, {"access": "private", "index": 14, "name": "on_playButton_clicked", "returnType": "void"}, {"access": "private", "index": 15, "name": "on_pausePlay<PERSON><PERSON>on_clicked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QMainWindow"}]}], "inputFile": "mainwindow.h", "outputRevision": 68}]