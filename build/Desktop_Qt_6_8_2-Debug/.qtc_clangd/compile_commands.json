[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avrouter.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avrouter.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveinputmanager.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveinputmanager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveoutputmanager.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveoutputmanager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videoformatter.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videoformatter.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/fpssync.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/fpssync.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videooutputprocess.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videooutputprocess.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/openclprocessor.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/openclprocessor.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avrouter.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avrouter.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveinputmanager.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveinputmanager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveoutputmanager.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveoutputmanager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videoformatter.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videoformatter.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/fpssync.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/fpssync.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avr_helper.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avr_helper.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videooutputprocess.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videooutputprocess.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/openclprocessor.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/openclprocessor.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/opt/Qt/6.8.2/gcc_64/include/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-I/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-I/opt/Qt/6.8.2/gcc_64/include/QtGui", "-I/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-I/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-I/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-I/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-I/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-I/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/ajakumocontroler.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/ajakumocontroler.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/aspencontroller.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/aspencontroller.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubcontroller.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubcontroller.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubswitcher.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubswitcher.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novacontrollerbase.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novacontrollerbase.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl660procontroller.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl660procontroller.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novaprouhdjrcontroller.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novaprouhdjrcontroller.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl4kcontroller.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl4kcontroller.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novahseriescontroller.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novahseriescontroller.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novavxseries.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novavxseries.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/lightwaremxseriescontroller.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/lightwaremxseriescontroller.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/XKeys/xkeyspiecontroller.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/XKeys/xkeyspiecontroller.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagesender.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagesender.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagereader.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagereader.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/videomatrixswitcherinterface.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/videomatrixswitcherinterface.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/ajakumocontroler.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/ajakumocontroler.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/aspencontroller.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/aspencontroller.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubcontroller.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubcontroller.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubswitcher.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubswitcher.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novacontrollerbase.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novacontrollerbase.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl660procontroller.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl660procontroller.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novaprouhdjrcontroller.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novaprouhdjrcontroller.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl4kcontroller.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl4kcontroller.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novahseriescontroller.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novahseriescontroller.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novavxseries.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novavxseries.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/lightwaremxseriescontroller.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/lightwaremxseriescontroller.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/XKeys/xkeyspiecontroller.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/XKeys/xkeyspiecontroller.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagesender.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagesender.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_NETWORK_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagereader.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagereader.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/imageloader.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/imageloader.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavbuffer.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavbuffer.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdirectsaver.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdirectsaver.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavencoder.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavencoder.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavfilter.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavfilter.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayer.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayer.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayerdirector.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayerdirector.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libswshelper.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libswshelper.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/simpleexportdecoder.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/simpleexportdecoder.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/videoframeexporter.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/videoframeexporter.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/filtercomplex.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/filtercomplex.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder2.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder2.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavreader.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavreader.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavwriter.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavwriter.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/imageloader.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/imageloader.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavbuffer.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavbuffer.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdirectsaver.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdirectsaver.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavencoder.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavencoder.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavfilter.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavfilter.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavhelpers.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavhelpers.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayerdirector.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayerdirector.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayer.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayer.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libswshelper.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libswshelper.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/simpleexportdecoder.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/simpleexportdecoder.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/videoframeexporter.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/videoframeexporter.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/filtercomplex.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/filtercomplex.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/streamreaderinterface.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/streamreaderinterface.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder2.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder2.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavreader.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavreader.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavprotocolcommons.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavprotocolcommons.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavwriter.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavwriter.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/GSS_libgoal_ndi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndifinder.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndifinder.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/GSS_libgoal_ndi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndireceiver.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndireceiver.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/GSS_libgoal_ndi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndisender.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndisender.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/GSS_libgoal_ndi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndifinder.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndifinder.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/GSS_libgoal_ndi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndireceiver.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndireceiver.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/GSS_libgoal_ndi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndisender.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndisender.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDispatch.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDispatch.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/capture.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/capture.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/decklinknotificationcallback.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/decklinknotificationcallback.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/outputsdi.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/outputsdi.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/bmdsdihelper.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/bmdsdihelper.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPI.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPI.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIConfiguration.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIConfiguration.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDeckControl.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDeckControl.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDiscovery.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDiscovery.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIModes.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIModes.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPITypes.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPITypes.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIVersion.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIVersion.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/LinuxCOM.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/LinuxCOM.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/capture.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/capture.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/decklinknotificationcallback.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/decklinknotificationcallback.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/outputsdi.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/outputsdi.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/bmdsdihelper.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/bmdsdihelper.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideoplayer.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideoplayer.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideosender.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideosender.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideoplayer.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideoplayer.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideosender.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideosender.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/doitlater.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/doitlater.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/memorypool.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/memorypool.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/qthreadedobject.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/qthreadedobject.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/runguard.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/runguard.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/universalplayer.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/universalplayer.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/freqsync.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/freqsync.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/timedqueue.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/timedqueue.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsharedmemory.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsharedmemory.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsystemsemaphore.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsystemsemaphore.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/doitlater.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/doitlater.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/elapsedtimereference.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/elapsedtimereference.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/memorypool.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/memorypool.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/qthreadedobject.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/qthreadedobject.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/runguard.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/runguard.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/threadsafequeue.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/threadsafequeue.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/timesynccache.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/timesynccache.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/universalplayer.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/universalplayer.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/freqsync.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/freqsync.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/timedqueue.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/timedqueue.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/obfuscate.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/obfuscate.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsharedmemory.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsharedmemory.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsystemsemaphore.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsystemsemaphore.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/audiocorrection.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/audiocorrection.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/colorcorrection.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/colorcorrection.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/framemetadata.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/framemetadata.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/qvideoframehelper.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/qvideoframehelper.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/videoframe.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/videoframe.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/audiocorrection.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/audiocorrection.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/colorcorrection.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/colorcorrection.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/framemetadata.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/framemetadata.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/qvideoframehelper.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/qvideoframehelper.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/videoframe.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/videoframe.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/manager.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/manager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/model.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/model.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/slider.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/slider.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/goalsplashscreen.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/goalsplashscreen.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/manager.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/manager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/model.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/model.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/slider.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/slider.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/goalsplashscreen.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/goalsplashscreen.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/include/mediaplayervlc.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/include/mediaplayervlc.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/mainwindow.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/main.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/include/mediaplayervlc.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/include/mediaplayervlc.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/mainwindow.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug", "-I/home/<USER>/PROJECTS/av-routing-and-mixing", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/av-routing-and-mixing_autogen/include", "-I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2", "-I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQml", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQuick", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQmlMeta", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQmlModels", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtOpenGL", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/main.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug", "-I/home/<USER>/PROJECTS/av-routing-and-mixing", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/av-routing-and-mixing_autogen/include", "-I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2", "-I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQml", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQuick", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQmlMeta", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQmlModels", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtOpenGL", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/mainwindow.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug", "-I/home/<USER>/PROJECTS/av-routing-and-mixing", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/av-routing-and-mixing_autogen/include", "-I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2", "-I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQml", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQuick", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQmlMeta", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQmlModels", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtOpenGL", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/mainwindow.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/recv_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/recv_test_app/recv_test_app_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/mainwindow.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/recv_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/recv_test_app/recv_test_app_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/main.cpp"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-std=gnu++17", "-fcolor-diagnostics", "-fPIC", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/recv_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/recv_test_app/recv_test_app_autogen/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "-I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "-isystem", "/opt/Qt/6.8.2/gcc_64/include", "-isystem", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtGui", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtWidgets", "-isystem", "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/opt/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-Wall", "-Wextra", "-x", "c++-header", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/mainwindow.h"], "directory": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc_clangd", "file": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/mainwindow.h"}]