{"BUILD_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/CMakeLists.txt", "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/.qtc/package-manager/auto-setup.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Config.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtInstallPaths.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Targets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeature.cmake", "/usr/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Config.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtInstallPaths.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Targets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeature.cmake", "/usr/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake"], "CMAKE_SOURCE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing", "DEP_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/deps", "DEP_FILE_RULE_NAME": "GSS_libgoal_devices_autogen/timestamp", "HEADERS": [["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagereader.h", "MU", "ZPVERCUKTT/moc_udpmessagereader.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagesender.h", "MU", "ZPVERCUKTT/moc_udpmessagesender.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/ajakumocontroler.h", "MU", "5X7J4VSBTR/moc_ajakumocontroler.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/aspencontroller.h", "MU", "5X7J4VSBTR/moc_aspencontroller.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubcontroller.h", "MU", "5X7J4VSBTR/moc_bmvideohubcontroller.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubswitcher.h", "MU", "5X7J4VSBTR/moc_bmvideohubswitcher.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/lightwaremxseriescontroller.h", "MU", "5X7J4VSBTR/moc_lightwaremxseriescontroller.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novacontrollerbase.h", "MU", "5X7J4VSBTR/moc_novacontrollerbase.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novahseriescontroller.h", "MU", "5X7J4VSBTR/moc_novahseriescontroller.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl4kcontroller.h", "MU", "5X7J4VSBTR/moc_novamctrl4kcontroller.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl660procontroller.h", "MU", "5X7J4VSBTR/moc_novamctrl660procontroller.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novaprouhdjrcontroller.h", "MU", "5X7J4VSBTR/moc_novaprouhdjrcontroller.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novavxseries.h", "MU", "5X7J4VSBTR/moc_novavxseries.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/videomatrixswitcherinterface.h", "MU", "5X7J4VSBTR/moc_videomatrixswitcherinterface.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/XKeys/xkeyspiecontroller.h", "MU", "73UE6ORXXU/moc_xkeyspiecontroller.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CORE_LIB", "QT_NETWORK_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include", "/opt/Qt/6.8.2/gcc_64/include/QtNetwork", "/opt/Qt/6.8.2/gcc_64/include", "/opt/Qt/6.8.2/gcc_64/include/QtCore", "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++", "/usr/include/c++/13", "/usr/include/x86_64-linux-gnu/c++/13", "/usr/include/c++/13/backward", "/usr/lib/llvm-18/lib/clang/18/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/clang++", "-std=gnu++17", "-dM", "-E", "-c", "/usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 16, "PARSE_CACHE_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/CMakeFiles/GSS_libgoal_devices_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/opt/Qt/6.8.2/gcc_64/libexec/moc", "QT_UIC_EXECUTABLE": "/opt/Qt/6.8.2/gcc_64/libexec/uic", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 8, "SETTINGS_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/CMakeFiles/GSS_libgoal_devices_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagereader.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagesender.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/ajakumocontroler.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/aspencontroller.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubcontroller.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubswitcher.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/lightwaremxseriescontroller.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novacontrollerbase.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novahseriescontroller.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl4kcontroller.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl660procontroller.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novaprouhdjrcontroller.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novavxseries.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/XKeys/xkeyspiecontroller.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "VERBOSITY": 0}