{"archive": {}, "artifacts": [{"path": "libgoal-sdi/libGSS_libgoal_sdi.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_include_directories"], "files": ["libgoal/libgoal-sdi/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 12, "parent": 0}, {"command": 1, "file": 0, "line": 59, "parent": 0}, {"command": 2, "file": 0, "line": 54, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fcolor-diagnostics"}, {"backtrace": 2, "fragment": "-fPIC"}], "defines": [{"backtrace": 2, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi"}, {"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi"}, {"backtrace": 0, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/include"}, {"backtrace": 3, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtNetwork"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 13, 14, 15, 16, 17]}], "dependencies": [{"backtrace": 2, "id": "GSS_libgoal_videoframe::@2bc16d3bd896b78e6cbd"}, {"backtrace": 2, "id": "GSS_libgoal_utils::@6c8cb491c3014fb511f8"}, {"id": "GSS_libgoal_sdi_autogen_timestamp_deps::@83de4cecf005b7ef38bf"}, {"backtrace": 0, "id": "GSS_libgoal_sdi_autogen::@83de4cecf005b7ef38bf"}], "id": "GSS_libgoal_sdi::@83de4cecf005b7ef38bf", "name": "GSS_libgoal_sdi", "nameOnDisk": "libGSS_libgoal_sdi.a", "paths": {"build": "libgoal-sdi", "source": "libgoal/libgoal-sdi"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 13, 14, 15, 16, 17]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}, {"name": "", "sourceIndexes": [18]}, {"name": "CMake Rules", "sourceIndexes": [19]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPI.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIConfiguration.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDeckControl.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDiscovery.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIModes.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPITypes.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIVersion.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-sdi/include/DeckLinkAPI/LinuxCOM.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-sdi/include/capture.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-sdi/include/decklinknotificationcallback.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-sdi/include/outputsdi.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-sdi/include/bmdsdihelper.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDispatch.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-sdi/include/capture.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-sdi/include/decklinknotificationcallback.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-sdi/include/outputsdi.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-sdi/include/bmdsdihelper.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-sdi/GSS_libgoal_sdi_autogen/timestamp.rule", "sourceGroupIndex": 3}], "type": "STATIC_LIBRARY"}