{"archive": {}, "artifacts": [{"path": "libgoal-libav/libGSS_libgoal_libav.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_include_directories"], "files": ["libgoal/libgoal-libav/CMakeLists.txt", "libgoal/libgoal-videoframe/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 13, "parent": 0}, {"command": 1, "file": 0, "line": 108, "parent": 0}, {"file": 1}, {"command": 2, "file": 0, "line": 66, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fcolor-diagnostics"}, {"backtrace": 2, "fragment": "-fPIC"}], "defines": [{"backtrace": 2, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav"}, {"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav"}, {"backtrace": 0, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/include"}, {"backtrace": 4, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtNetwork"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 1, 3, 5, 7, 9, 11, 14, 15, 18, 20, 22, 25, 28, 30, 33]}], "dependencies": [{"backtrace": 2, "id": "GSS_libgoal_utils::@6c8cb491c3014fb511f8"}, {"id": "GSS_libgoal_videoframe_autogen_timestamp_deps::@2bc16d3bd896b78e6cbd"}, {"backtrace": 3, "id": "GSS_libgoal_videoframe_autogen::@2bc16d3bd896b78e6cbd"}, {"id": "GSS_libgoal_libav_autogen_timestamp_deps::@d4925a3eb7b45a3a7728"}, {"backtrace": 0, "id": "GSS_libgoal_libav_autogen::@d4925a3eb7b45a3a7728"}], "id": "GSS_libgoal_libav::@d4925a3eb7b45a3a7728", "name": "GSS_libgoal_libav", "nameOnDisk": "libGSS_libgoal_libav.a", "paths": {"build": "libgoal-libav", "source": "libgoal/libgoal-libav"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 3, 5, 7, 9, 11, 14, 15, 18, 20, 22, 25, 28, 30, 33]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2, 4, 6, 8, 10, 12, 13, 16, 17, 19, 21, 23, 24, 26, 27, 29, 31, 32]}, {"name": "", "sourceIndexes": [34]}, {"name": "CMake Rules", "sourceIndexes": [35]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/imageloader.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/imageloader.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/libavbuffer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/libavbuffer.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/libavdecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/libavdecoder.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/libavdirectsaver.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/libavdirectsaver.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/libavencoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/libavencoder.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/libavfilter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/libavfilter.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/libavhelpers.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/libavplayer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/libavplayerdirector.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/libavplayerdirector.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/libavplayer.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/libswshelper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/libswshelper.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/simpleexportdecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/simpleexportdecoder.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/videoframeexporter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/videoframeexporter.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/filtercomplex.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/filtercomplex.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/streamreaderinterface.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/libavdecoder2.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/libavdecoder2.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/libavreader.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/libavreader.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/libavprotocolcommons.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-libav/include/libav/libavwriter.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-libav/include/libav/libavwriter.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-libav/GSS_libgoal_libav_autogen/timestamp.rule", "sourceGroupIndex": 3}], "type": "STATIC_LIBRARY"}