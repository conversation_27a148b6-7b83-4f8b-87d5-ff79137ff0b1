{"archive": {}, "artifacts": [{"path": "libgoal-devices/libGSS_libgoal_devices.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_include_directories", "target_sources"], "files": ["libgoal/libgoal-devices/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 12, "parent": 0}, {"command": 1, "file": 0, "line": 107, "parent": 0}, {"command": 2, "file": 0, "line": 102, "parent": 0}, {"command": 3, "file": 0, "line": 15, "parent": 0}, {"command": 3, "file": 0, "line": 62, "parent": 0}, {"command": 3, "file": 0, "line": 88, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fcolor-diagnostics"}, {"backtrace": 2, "fragment": "-fPIC"}], "defines": [{"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}], "includes": [{"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices"}, {"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices"}, {"backtrace": 0, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/include"}, {"backtrace": 3, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtNetwork"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 25, 27]}], "dependencies": [{"id": "GSS_libgoal_devices_autogen_timestamp_deps::@a4acf01ffd64b3c25b5e"}, {"backtrace": 0, "id": "GSS_libgoal_devices_autogen::@a4acf01ffd64b3c25b5e"}], "id": "GSS_libgoal_devices::@a4acf01ffd64b3c25b5e", "name": "GSS_libgoal_devices", "nameOnDisk": "libGSS_libgoal_devices.a", "paths": {"build": "libgoal-devices", "source": "libgoal/libgoal-devices"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 25, 27]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 23, 24, 26]}, {"name": "", "sourceIndexes": [28]}, {"name": "CMake Rules", "sourceIndexes": [29]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "libgoal/libgoal-devices/include/VideoSwitcher/videomatrixswitcherinterface.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "libgoal/libgoal-devices/include/VideoSwitcher/ajakumocontroler.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "libgoal/libgoal-devices/include/VideoSwitcher/aspencontroller.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubcontroller.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubswitcher.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "libgoal/libgoal-devices/include/VideoSwitcher/novacontrollerbase.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "libgoal/libgoal-devices/include/VideoSwitcher/novamctrl660procontroller.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "libgoal/libgoal-devices/include/VideoSwitcher/novaprouhdjrcontroller.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "libgoal/libgoal-devices/include/VideoSwitcher/novamctrl4kcontroller.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "libgoal/libgoal-devices/include/VideoSwitcher/novahseriescontroller.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "libgoal/libgoal-devices/include/VideoSwitcher/novavxseries.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "libgoal/libgoal-devices/include/VideoSwitcher/ajakumocontroler.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "libgoal/libgoal-devices/include/VideoSwitcher/aspencontroller.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubcontroller.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubswitcher.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "libgoal/libgoal-devices/include/VideoSwitcher/novacontrollerbase.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "libgoal/libgoal-devices/include/VideoSwitcher/novamctrl660procontroller.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "libgoal/libgoal-devices/include/VideoSwitcher/novaprouhdjrcontroller.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "libgoal/libgoal-devices/include/VideoSwitcher/novamctrl4kcontroller.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "libgoal/libgoal-devices/include/VideoSwitcher/novahseriescontroller.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "libgoal/libgoal-devices/include/VideoSwitcher/novavxseries.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "path": "libgoal/libgoal-devices/include/XKeys/xkeyspiecontroller.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "path": "libgoal/libgoal-devices/include/XKeys/xkeyspiecontroller.h", "sourceGroupIndex": 1}, {"backtrace": 6, "path": "libgoal/libgoal-devices/include/UdpMsg/udpmessagesender.h", "sourceGroupIndex": 1}, {"backtrace": 6, "compileGroupIndex": 0, "path": "libgoal/libgoal-devices/include/UdpMsg/udpmessagesender.cpp", "sourceGroupIndex": 0}, {"backtrace": 6, "path": "libgoal/libgoal-devices/include/UdpMsg/udpmessagereader.h", "sourceGroupIndex": 1}, {"backtrace": 6, "compileGroupIndex": 0, "path": "libgoal/libgoal-devices/include/UdpMsg/udpmessagereader.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-devices/GSS_libgoal_devices_autogen/timestamp.rule", "sourceGroupIndex": 3}], "type": "STATIC_LIBRARY"}