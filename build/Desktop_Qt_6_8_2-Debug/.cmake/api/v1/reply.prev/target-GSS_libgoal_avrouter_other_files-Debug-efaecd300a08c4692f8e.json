{"backtrace": 5, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_expose_source_file_to_ide", "_qt_internal_process_resource", "qt6_add_resources", "qt_add_resources", "set_property"], "files": ["/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "libgoal/libgoal-avrouter/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 4, "file": 1, "line": 36, "parent": 0}, {"command": 3, "file": 0, "line": 458, "parent": 1}, {"command": 2, "file": 0, "line": 406, "parent": 2}, {"command": 1, "file": 0, "line": 2310, "parent": 3}, {"command": 0, "file": 0, "line": 2145, "parent": 4}, {"command": 1, "file": 0, "line": 2364, "parent": 3}, {"command": 5, "file": 0, "line": 2155, "parent": 6}]}, "id": "GSS_libgoal_avrouter_other_files::@610245c7e5910b70db46", "name": "GSS_libgoal_avrouter_other_files", "paths": {"build": "libgoal-avrouter", "source": "libgoal/libgoal-avrouter"}, "sourceGroups": [{"name": "Resources", "sourceIndexes": [0]}, {"name": "", "sourceIndexes": [1]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-avrouter/.qt/rcc/GSS_libgoal_avrouter.qrc", "sourceGroupIndex": 0}, {"backtrace": 7, "path": "libgoal/libgoal-avrouter/resources/kernels.cl", "sourceGroupIndex": 1}], "type": "UTILITY"}