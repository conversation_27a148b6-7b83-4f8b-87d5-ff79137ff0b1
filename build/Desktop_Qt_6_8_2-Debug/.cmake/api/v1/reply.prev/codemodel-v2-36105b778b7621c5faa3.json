{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 9, 10, 11, 12], "hasInstallRule": true, "jsonFile": "directory-.-Debug-e2383e491dce564cb0c2.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]}, {"build": "libgoal", "jsonFile": "directory-libgoal-Debug-9d0ff3bccd0d8d1a246d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "libgoal"}, {"build": "libgoal-ndi", "childIndexes": [3, 4, 5, 8], "jsonFile": "directory-libgoal-ndi-Debug-34859441f47e3f43c708.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 2, "source": "libgoal/libgoal-ndi", "targetIndexes": [11, 12, 13]}, {"build": "libgoal-videoframe", "jsonFile": "directory-libgoal-videoframe-Debug-1309eb80b927c2445157.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 3, "source": "libgoal/libgoal-videoframe", "targetIndexes": [23, 24, 25]}, {"build": "libgoal-utils", "jsonFile": "directory-libgoal-utils-Debug-07b608341c359a3a3d67.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 4, "source": "libgoal/libgoal-utils", "targetIndexes": [20, 21, 22]}, {"build": "libgoal-ndi/NDIStream_send_test_app", "childIndexes": [6, 7], "jsonFile": "directory-libgoal-ndi.NDIStream_send_test_app-Debug-85e6ec79151aafa8b63a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 5, "source": "libgoal/libgoal-ndi/NDIStream_send_test_app", "targetIndexes": [29, 30, 31]}, {"build": "libgoal-libav", "jsonFile": "directory-libgoal-libav-Debug-52662cca0608a1863458.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 5, "projectIndex": 6, "source": "libgoal/libgoal-libav", "targetIndexes": [8, 9, 10]}, {"build": "libgoal-smemory-video", "jsonFile": "directory-libgoal-smemory-video-Debug-e223693788a3ecf2c7ac.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 5, "projectIndex": 7, "source": "libgoal/libgoal-smemory-video", "targetIndexes": [17, 18, 19]}, {"build": "libgoal-ndi/recv_test_app", "jsonFile": "directory-libgoal-ndi.recv_test_app-Debug-ad708c96f26ff926da08.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 8, "source": "libgoal/libgoal-ndi/recv_test_app", "targetIndexes": [45, 46, 47]}, {"build": "libgoal-sdi", "jsonFile": "directory-libgoal-sdi-Debug-5118cf0663713067f8ae.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 9, "source": "libgoal/libgoal-sdi", "targetIndexes": [14, 15, 16]}, {"build": "libgoal-devices", "jsonFile": "directory-libgoal-devices-Debug-014f5eff0ea2a37927db.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 10, "source": "libgoal/libgoal-devices", "targetIndexes": [5, 6, 7]}, {"build": "libgoal-avrouter", "jsonFile": "directory-libgoal-avrouter-Debug-2050b8a657a7bedb4072.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 11, "source": "libgoal/libgoal-avrouter", "targetIndexes": [0, 1, 2, 3, 4]}, {"build": "libgoal-widgets", "jsonFile": "directory-libgoal-widgets-Debug-3ff3cb590b0cfa5796e7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 12, "source": "libgoal/libgoal-widgets", "targetIndexes": [26, 27, 28]}], "name": "Debug", "projects": [{"childIndexes": [1, 2, 9, 10, 11, 12], "directoryIndexes": [0], "name": "av-routing-and-mixing", "targetIndexes": [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]}, {"directoryIndexes": [1], "name": "LibGoal", "parentIndex": 0}, {"childIndexes": [3, 4, 5, 8], "directoryIndexes": [2], "name": "GSS_libgoal_ndi", "parentIndex": 0, "targetIndexes": [11, 12, 13]}, {"directoryIndexes": [3], "name": "GSS_libgoal_videoframe", "parentIndex": 2, "targetIndexes": [23, 24, 25]}, {"directoryIndexes": [4], "name": "GSS_libgoal_utils", "parentIndex": 2, "targetIndexes": [20, 21, 22]}, {"childIndexes": [6, 7], "directoryIndexes": [5], "name": "NDIStream_send_test_app", "parentIndex": 2, "targetIndexes": [29, 30, 31]}, {"directoryIndexes": [6], "name": "GSS_libgoal_libav", "parentIndex": 5, "targetIndexes": [8, 9, 10]}, {"directoryIndexes": [7], "name": "GSS_libgoal_smemory-video", "parentIndex": 5, "targetIndexes": [17, 18, 19]}, {"directoryIndexes": [8], "name": "recv_test_app", "parentIndex": 2, "targetIndexes": [45, 46, 47]}, {"directoryIndexes": [9], "name": "GSS_libgoal_sdi", "parentIndex": 0, "targetIndexes": [14, 15, 16]}, {"directoryIndexes": [10], "name": "GSS_libgoal_devices", "parentIndex": 0, "targetIndexes": [5, 6, 7]}, {"directoryIndexes": [11], "name": "GSS_libgoal_avrouter", "parentIndex": 0, "targetIndexes": [0, 1, 2, 3, 4]}, {"directoryIndexes": [12], "name": "GSS_libgoal_widgets", "parentIndex": 0, "targetIndexes": [26, 27, 28]}], "targets": [{"directoryIndex": 11, "id": "GSS_libgoal_avrouter::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter-Debug-408772904069ccd91686.json", "name": "GSS_libgoal_avrouter", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_autogen::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_autogen-Debug-2d22965568d577a3ef39.json", "name": "GSS_libgoal_avrouter_autogen", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_autogen_timestamp_deps::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_autogen_timestamp_deps-Debug-5dcc82b028cc32078c4e.json", "name": "GSS_libgoal_avrouter_autogen_timestamp_deps", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_other_files::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_other_files-Debug-efaecd300a08c4692f8e.json", "name": "GSS_libgoal_avrouter_other_files", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_resources_1::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_resources_1-Debug-b0e97fc7ce0bcb070e0d.json", "name": "GSS_libgoal_avrouter_resources_1", "projectIndex": 11}, {"directoryIndex": 10, "id": "GSS_libgoal_devices::@a4acf01ffd64b3c25b5e", "jsonFile": "target-GSS_libgoal_devices-Debug-c046691f8aede0665f67.json", "name": "GSS_libgoal_devices", "projectIndex": 10}, {"directoryIndex": 10, "id": "GSS_libgoal_devices_autogen::@a4acf01ffd64b3c25b5e", "jsonFile": "target-GSS_libgoal_devices_autogen-Debug-a829d8827a5adc152395.json", "name": "GSS_libgoal_devices_autogen", "projectIndex": 10}, {"directoryIndex": 10, "id": "GSS_libgoal_devices_autogen_timestamp_deps::@a4acf01ffd64b3c25b5e", "jsonFile": "target-GSS_libgoal_devices_autogen_timestamp_deps-Debug-d64f74971a974d2c5522.json", "name": "GSS_libgoal_devices_autogen_timestamp_deps", "projectIndex": 10}, {"directoryIndex": 6, "id": "GSS_libgoal_libav::@d4925a3eb7b45a3a7728", "jsonFile": "target-GSS_libgoal_libav-Debug-135e395777ae19205297.json", "name": "GSS_libgoal_libav", "projectIndex": 6}, {"directoryIndex": 6, "id": "GSS_libgoal_libav_autogen::@d4925a3eb7b45a3a7728", "jsonFile": "target-GSS_libgoal_libav_autogen-Debug-4f50254c67330d3a5eff.json", "name": "GSS_libgoal_libav_autogen", "projectIndex": 6}, {"directoryIndex": 6, "id": "GSS_libgoal_libav_autogen_timestamp_deps::@d4925a3eb7b45a3a7728", "jsonFile": "target-GSS_libgoal_libav_autogen_timestamp_deps-Debug-2adaae3fa3235c715218.json", "name": "GSS_libgoal_libav_autogen_timestamp_deps", "projectIndex": 6}, {"directoryIndex": 2, "id": "GSS_libgoal_ndi::@e8405e6f8fa3070b957e", "jsonFile": "target-GSS_libgoal_ndi-Debug-30cfe09ae3e6757fcc74.json", "name": "GSS_libgoal_ndi", "projectIndex": 2}, {"directoryIndex": 2, "id": "GSS_libgoal_ndi_autogen::@e8405e6f8fa3070b957e", "jsonFile": "target-GSS_libgoal_ndi_autogen-Debug-a080e735b8a9c3d06d82.json", "name": "GSS_libgoal_ndi_autogen", "projectIndex": 2}, {"directoryIndex": 2, "id": "GSS_libgoal_ndi_autogen_timestamp_deps::@e8405e6f8fa3070b957e", "jsonFile": "target-GSS_libgoal_ndi_autogen_timestamp_deps-Debug-149889d35618f7d074c7.json", "name": "GSS_libgoal_ndi_autogen_timestamp_deps", "projectIndex": 2}, {"directoryIndex": 9, "id": "GSS_libgoal_sdi::@83de4cecf005b7ef38bf", "jsonFile": "target-GSS_libgoal_sdi-Debug-01a7fa37bacb2a9b8d14.json", "name": "GSS_libgoal_sdi", "projectIndex": 9}, {"directoryIndex": 9, "id": "GSS_libgoal_sdi_autogen::@83de4cecf005b7ef38bf", "jsonFile": "target-GSS_libgoal_sdi_autogen-Debug-50526e4092677b0bb7c6.json", "name": "GSS_libgoal_sdi_autogen", "projectIndex": 9}, {"directoryIndex": 9, "id": "GSS_libgoal_sdi_autogen_timestamp_deps::@83de4cecf005b7ef38bf", "jsonFile": "target-GSS_libgoal_sdi_autogen_timestamp_deps-Debug-882dc793c5adb5d14da1.json", "name": "GSS_libgoal_sdi_autogen_timestamp_deps", "projectIndex": 9}, {"directoryIndex": 7, "id": "GSS_libgoal_smemory-video::@ce80f594daf5fb6bfe42", "jsonFile": "target-GSS_libgoal_smemory-video-Debug-38611ad98ad724b8feae.json", "name": "GSS_libgoal_smemory-video", "projectIndex": 7}, {"directoryIndex": 7, "id": "GSS_libgoal_smemory-video_autogen::@ce80f594daf5fb6bfe42", "jsonFile": "target-GSS_libgoal_smemory-video_autogen-Debug-465d22bddb6b604775f6.json", "name": "GSS_libgoal_smemory-video_autogen", "projectIndex": 7}, {"directoryIndex": 7, "id": "GSS_libgoal_smemory-video_autogen_timestamp_deps::@ce80f594daf5fb6bfe42", "jsonFile": "target-GSS_libgoal_smemory-video_autogen_timestamp_deps-Debug-f5d98e6a2cb802d92f39.json", "name": "GSS_libgoal_smemory-video_autogen_timestamp_deps", "projectIndex": 7}, {"directoryIndex": 4, "id": "GSS_libgoal_utils::@6c8cb491c3014fb511f8", "jsonFile": "target-GSS_libgoal_utils-Debug-43c23c7ad8efb5b37473.json", "name": "GSS_libgoal_utils", "projectIndex": 4}, {"directoryIndex": 4, "id": "GSS_libgoal_utils_autogen::@6c8cb491c3014fb511f8", "jsonFile": "target-GSS_libgoal_utils_autogen-Debug-d9450ad60703dc08746f.json", "name": "GSS_libgoal_utils_autogen", "projectIndex": 4}, {"directoryIndex": 4, "id": "GSS_libgoal_utils_autogen_timestamp_deps::@6c8cb491c3014fb511f8", "jsonFile": "target-GSS_libgoal_utils_autogen_timestamp_deps-Debug-134a8a03c6c8ebc996d4.json", "name": "GSS_libgoal_utils_autogen_timestamp_deps", "projectIndex": 4}, {"directoryIndex": 3, "id": "GSS_libgoal_videoframe::@2bc16d3bd896b78e6cbd", "jsonFile": "target-GSS_libgoal_videoframe-Debug-5fd16a5059eccd4a0c28.json", "name": "GSS_libgoal_videoframe", "projectIndex": 3}, {"directoryIndex": 3, "id": "GSS_libgoal_videoframe_autogen::@2bc16d3bd896b78e6cbd", "jsonFile": "target-GSS_libgoal_videoframe_autogen-Debug-3a03805924b970cbbbc1.json", "name": "GSS_libgoal_videoframe_autogen", "projectIndex": 3}, {"directoryIndex": 3, "id": "GSS_libgoal_videoframe_autogen_timestamp_deps::@2bc16d3bd896b78e6cbd", "jsonFile": "target-GSS_libgoal_videoframe_autogen_timestamp_deps-Debug-7f4fb778f1330fdc5ecb.json", "name": "GSS_libgoal_videoframe_autogen_timestamp_deps", "projectIndex": 3}, {"directoryIndex": 12, "id": "GSS_libgoal_widgets::@1a6ca22365c285a707f8", "jsonFile": "target-GSS_libgoal_widgets-Debug-2d166af1f8e843731cf2.json", "name": "GSS_libgoal_widgets", "projectIndex": 12}, {"directoryIndex": 12, "id": "GSS_libgoal_widgets_autogen::@1a6ca22365c285a707f8", "jsonFile": "target-GSS_libgoal_widgets_autogen-Debug-3424f1ecdec8b949923a.json", "name": "GSS_libgoal_widgets_autogen", "projectIndex": 12}, {"directoryIndex": 12, "id": "GSS_libgoal_widgets_autogen_timestamp_deps::@1a6ca22365c285a707f8", "jsonFile": "target-GSS_libgoal_widgets_autogen_timestamp_deps-Debug-ed55f684ac9597d60cdf.json", "name": "GSS_libgoal_widgets_autogen_timestamp_deps", "projectIndex": 12}, {"directoryIndex": 5, "id": "NDIStream_send_test_app::@6a6cc8600da1c3617a31", "jsonFile": "target-NDIStream_send_test_app-Debug-4cfa256c517ce44db9e3.json", "name": "NDIStream_send_test_app", "projectIndex": 5}, {"directoryIndex": 5, "id": "NDIStream_send_test_app_autogen::@6a6cc8600da1c3617a31", "jsonFile": "target-NDIStream_send_test_app_autogen-Debug-e539954975cd27df63cb.json", "name": "NDIStream_send_test_app_autogen", "projectIndex": 5}, {"directoryIndex": 5, "id": "NDIStream_send_test_app_autogen_timestamp_deps::@6a6cc8600da1c3617a31", "jsonFile": "target-NDIStream_send_test_app_autogen_timestamp_deps-Debug-5fc97c63d53eb6e6f3d2.json", "name": "NDIStream_send_test_app_autogen_timestamp_deps", "projectIndex": 5}, {"directoryIndex": 0, "id": "all_aotstats::@6890427a1f51a3e7e1df", "jsonFile": "target-all_aotstats-Debug-4f3315b7dff24f46f217.json", "name": "all_aotstats", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmllint::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmllint-Debug-efda6f05689f1ff1ab59.json", "name": "all_qmllint", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmllint_json::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmllint_json-Debug-68cb92b60c1718f95547.json", "name": "all_qmllint_json", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmllint_module::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmllint_module-Debug-ed8ee1953c9a46c9ec8c.json", "name": "all_qmllint_module", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmltyperegistrations::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmltyperegistrations-Debug-aaa2d996e084cf49d667.json", "name": "all_qmltyperegistrations", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing-Debug-0d3a783d8dddde3cc492.json", "name": "av-routing-and-mixing", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_autogen-Debug-2b47665adc6d7c5f8666.json", "name": "av-routing-and-mixing_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_autogen_timestamp_deps-Debug-e82dab6e08b950b1c1e3.json", "name": "av-routing-and-mixing_autogen_timestamp_deps", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmlimportscan::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmlimportscan-Debug-dcd2931112512e5c9abd.json", "name": "av-routing-and-mixing_qmlimportscan", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmllint::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmllint-Debug-b0438cc30e2f598f7ec6.json", "name": "av-routing-and-mixing_qmllint", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmllint_json::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmllint_json-Debug-dd2bed397be28bdf8172.json", "name": "av-routing-and-mixing_qmllint_json", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmllint_module::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmllint_module-Debug-ea304e251665b0b0c40f.json", "name": "av-routing-and-mixing_qmllint_module", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmltyperegistration::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmltyperegistration-Debug-4747427a085f3c9e3634.json", "name": "av-routing-and-mixing_qmltyperegistration", "projectIndex": 0}, {"directoryIndex": 8, "id": "recv_test_app::@899e4ac02e105eff13be", "jsonFile": "target-recv_test_app-Debug-8773ee3e8ff5bc530939.json", "name": "recv_test_app", "projectIndex": 8}, {"directoryIndex": 8, "id": "recv_test_app_autogen::@899e4ac02e105eff13be", "jsonFile": "target-recv_test_app_autogen-Debug-ddb7d16ab8875a8c7ffe.json", "name": "recv_test_app_autogen", "projectIndex": 8}, {"directoryIndex": 8, "id": "recv_test_app_autogen_timestamp_deps::@899e4ac02e105eff13be", "jsonFile": "target-recv_test_app_autogen_timestamp_deps-Debug-36a24640ea73f395b27d.json", "name": "recv_test_app_autogen_timestamp_deps", "projectIndex": 8}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug", "source": "/home/<USER>/PROJECTS/av-routing-and-mixing"}, "version": {"major": 2, "minor": 6}}