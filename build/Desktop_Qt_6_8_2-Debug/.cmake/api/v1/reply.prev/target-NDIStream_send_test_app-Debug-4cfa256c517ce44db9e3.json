{"artifacts": [{"path": "libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_property", "_pkg_create_imp_target", "_pkg_recalculate", "pkg_check_modules", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "_qt_internal_find_third_party_dependencies"], "files": ["libgoal/libgoal-ndi/NDIStream_send_test_app/CMakeLists.txt", "libgoal/libgoal-ndi/CMakeLists.txt", "libgoal/libgoal-libav/CMakeLists.txt", "/usr/share/cmake-3.28/Modules/FindPkgConfig.cmake", "libgoal/libgoal-utils/CMakeLists.txt", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Config.cmake", "CMakeLists.txt", "libgoal/libgoal-videoframe/CMakeLists.txt", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "/usr/share/cmake-3.28/Modules/FindOpenGL.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/FindWrapOpenGL.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiDependencies.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaTargets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 1, "file": 0, "line": 29, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 41, "parent": 3}, {"file": 2}, {"command": 1, "file": 2, "line": 108, "parent": 5}, {"command": 5, "file": 2, "line": 91, "parent": 5}, {"command": 4, "file": 3, "line": 849, "parent": 7}, {"command": 3, "file": 3, "line": 339, "parent": 8}, {"command": 2, "file": 3, "line": 320, "parent": 9}, {"command": 5, "file": 2, "line": 92, "parent": 5}, {"command": 4, "file": 3, "line": 849, "parent": 11}, {"command": 3, "file": 3, "line": 339, "parent": 12}, {"command": 2, "file": 3, "line": 320, "parent": 13}, {"command": 5, "file": 2, "line": 93, "parent": 5}, {"command": 4, "file": 3, "line": 849, "parent": 15}, {"command": 3, "file": 3, "line": 339, "parent": 16}, {"command": 2, "file": 3, "line": 320, "parent": 17}, {"command": 5, "file": 2, "line": 94, "parent": 5}, {"command": 4, "file": 3, "line": 849, "parent": 19}, {"command": 3, "file": 3, "line": 339, "parent": 20}, {"command": 2, "file": 3, "line": 320, "parent": 21}, {"command": 5, "file": 2, "line": 95, "parent": 5}, {"command": 4, "file": 3, "line": 849, "parent": 23}, {"command": 3, "file": 3, "line": 339, "parent": 24}, {"command": 2, "file": 3, "line": 320, "parent": 25}, {"command": 5, "file": 2, "line": 96, "parent": 5}, {"command": 4, "file": 3, "line": 849, "parent": 27}, {"command": 3, "file": 3, "line": 339, "parent": 28}, {"command": 2, "file": 3, "line": 320, "parent": 29}, {"file": 4}, {"command": 1, "file": 4, "line": 51, "parent": 31}, {"file": 12}, {"command": 8, "file": 12, "line": 15, "parent": 33}, {"file": 11, "parent": 34}, {"command": 8, "file": 11, "line": 190, "parent": 35}, {"file": 10, "parent": 36}, {"command": 7, "file": 10, "line": 43, "parent": 37}, {"file": 9, "parent": 38}, {"command": 10, "file": 9, "line": 45, "parent": 39}, {"command": 9, "file": 8, "line": 145, "parent": 40}, {"command": 8, "file": 7, "line": 76, "parent": 41}, {"file": 6, "parent": 42}, {"command": 7, "file": 6, "line": 55, "parent": 43}, {"file": 5, "parent": 44}, {"command": 6, "file": 5, "line": 100, "parent": 45}, {"file": 13}, {"command": 1, "file": 13, "line": 54, "parent": 47}, {"command": 7, "file": 10, "line": 55, "parent": 37}, {"file": 14, "parent": 49}, {"command": 6, "file": 14, "line": 61, "parent": 50}, {"command": 7, "file": 6, "line": 43, "parent": 43}, {"file": 17, "parent": 52}, {"command": 11, "file": 17, "line": 37, "parent": 53}, {"command": 9, "file": 8, "line": 36, "parent": 54}, {"command": 8, "file": 7, "line": 76, "parent": 55}, {"file": 16, "parent": 56}, {"command": 8, "file": 16, "line": 13, "parent": 57}, {"file": 15, "parent": 58}, {"command": 2, "file": 15, "line": 673, "parent": 59}, {"command": 6, "file": 15, "line": 671, "parent": 59}, {"command": 8, "file": 11, "line": 190, "parent": 35}, {"file": 19, "parent": 62}, {"command": 7, "file": 19, "line": 55, "parent": 63}, {"file": 18, "parent": 64}, {"command": 6, "file": 18, "line": 61, "parent": 65}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fcolor-diagnostics"}, {"backtrace": 2, "fragment": "-fPIC"}], "defines": [{"backtrace": 2, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app"}, {"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app"}, {"backtrace": 0, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtNetwork"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 2, 4, 6]}], "dependencies": [{"backtrace": 2, "id": "GSS_libgoal_ndi::@e8405e6f8fa3070b957e"}, {"backtrace": 2, "id": "GSS_libgoal_videoframe::@2bc16d3bd896b78e6cbd"}, {"backtrace": 2, "id": "GSS_libgoal_utils::@6c8cb491c3014fb511f8"}, {"backtrace": 2, "id": "GSS_libgoal_smemory-video::@ce80f594daf5fb6bfe42"}, {"id": "NDIStream_send_test_app_autogen_timestamp_deps::@6a6cc8600da1c3617a31"}, {"backtrace": 0, "id": "NDIStream_send_test_app_autogen::@6a6cc8600da1c3617a31"}], "id": "NDIStream_send_test_app::@6a6cc8600da1c3617a31", "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/Qt/6.8.2/gcc_64/lib", "role": "libraries"}, {"backtrace": 2, "fragment": "libgoal-libav/libGSS_libgoal_libav.a", "role": "libraries"}, {"backtrace": 2, "fragment": "libgoal-ndi/libGSS_libgoal_ndi.a", "role": "libraries"}, {"backtrace": 2, "fragment": "libgoal-smemory-video/libGSS_libgoal_smemory-video.a", "role": "libraries"}, {"backtrace": 2, "fragment": "-lvlc", "role": "libraries"}, {"backtrace": 2, "fragment": "-lvlccore", "role": "libraries"}, {"backtrace": 2, "fragment": "-lX11", "role": "libraries"}, {"backtrace": 4, "fragment": "-lndi", "role": "libraries"}, {"backtrace": 2, "fragment": "libgoal-libav/libGSS_libgoal_libav.a", "role": "libraries"}, {"backtrace": 6, "fragment": "libgoal-videoframe/libGSS_libgoal_videoframe.a", "role": "libraries"}, {"backtrace": 2, "fragment": "libgoal-libav/libGSS_libgoal_libav.a", "role": "libraries"}, {"backtrace": 6, "fragment": "libgoal-videoframe/libGSS_libgoal_videoframe.a", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/Qt/6.8.2/gcc_64/lib/libQt6Widgets.so.6.8.2", "role": "libraries"}, {"backtrace": 10, "fragment": "/usr/lib/x86_64-linux-gnu/libavformat.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/usr/lib/x86_64-linux-gnu/libavfilter.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/usr/lib/x86_64-linux-gnu/libswscale.so", "role": "libraries"}, {"backtrace": 22, "fragment": "/usr/lib/x86_64-linux-gnu/libswresample.so", "role": "libraries"}, {"backtrace": 26, "fragment": "/usr/lib/x86_64-linux-gnu/libavcodec.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/usr/lib/x86_64-linux-gnu/libavutil.so", "role": "libraries"}, {"backtrace": 6, "fragment": "libgoal-utils/libGSS_libgoal_utils.a", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/Qt/6.8.2/gcc_64/lib/libQt6Concurrent.so.6.8.2", "role": "libraries"}, {"backtrace": 46, "fragment": "/usr/lib/x86_64-linux-gnu/libxkbcommon.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/Qt/6.8.2/gcc_64/lib/libQt6Multimedia.so.6.8.2", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/Qt/6.8.2/gcc_64/lib/libQt6Gui.so.6.8.2", "role": "libraries"}, {"backtrace": 60, "fragment": "/usr/lib/x86_64-linux-gnu/libGLX.so", "role": "libraries"}, {"backtrace": 61, "fragment": "/usr/lib/x86_64-linux-gnu/libOpenGL.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/opt/Qt/6.8.2/gcc_64/lib/libQt6Network.so.6.8.2", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/Qt/6.8.2/gcc_64/lib/libQt6Core.so.6.8.2", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/Qt/6.8.2/gcc_64/lib", "role": "libraries"}], "language": "CXX"}, "name": "NDIStream_send_test_app", "nameOnDisk": "NDIStream_send_test_app", "paths": {"build": "libgoal-ndi/NDIStream_send_test_app", "source": "libgoal/libgoal-ndi/NDIStream_send_test_app"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4, 6]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 7]}, {"name": "Forms", "sourceIndexes": [5]}, {"name": "", "sourceIndexes": [8]}, {"name": "CMake Rules", "sourceIndexes": [9]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-ndi/NDIStream_send_test_app/include/mediaplayervlc.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-ndi/NDIStream_send_test_app/include/mediaplayervlc.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-ndi/NDIStream_send_test_app/mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-ndi/NDIStream_send_test_app/mainwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-ndi/NDIStream_send_test_app/mainwindow.ui", "sourceGroupIndex": 2}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-ndi/NDIStream_send_test_app/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include/ui_mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/timestamp", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_2-Debug/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/timestamp.rule", "sourceGroupIndex": 4}], "type": "EXECUTABLE"}