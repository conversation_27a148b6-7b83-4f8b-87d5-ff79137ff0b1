set(__QT_DEPLOY_TARGET_GSS_libgoal_videoframe_FILE /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/libGSS_libgoal_videoframe.a)
set(__QT_DEPLOY_TARGET_GSS_libgoal_videoframe_TYPE STATIC_LIBRARY)
set(__QT_DEPLOY_TARGET_GSS_libgoal_utils_FILE /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/libGSS_libgoal_utils.a)
set(__QT_DEPLOY_TARGET_GSS_libgoal_utils_TYPE STATIC_LIBRARY)
set(__QT_DEPLOY_TARGET_GSS_libgoal_libav_FILE /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/libGSS_libgoal_libav.a)
set(__QT_DEPLOY_TARGET_GSS_libgoal_libav_TYPE STATIC_LIBRARY)
set(__QT_DEPLOY_TARGET_GSS_libgoal_smemory-video_FILE /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video/libGSS_libgoal_smemory-video.a)
set(__QT_DEPLOY_TARGET_GSS_libgoal_smemory-video_TYPE STATIC_LIBRARY)
set(__QT_DEPLOY_TARGET_NDIStream_send_test_app_FILE /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app)
set(__QT_DEPLOY_TARGET_NDIStream_send_test_app_TYPE EXECUTABLE)
set(__QT_DEPLOY_TARGET_recv_test_app_FILE /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/recv_test_app)
set(__QT_DEPLOY_TARGET_recv_test_app_TYPE EXECUTABLE)
set(__QT_DEPLOY_TARGET_GSS_libgoal_ndi_FILE /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/libGSS_libgoal_ndi.a)
set(__QT_DEPLOY_TARGET_GSS_libgoal_ndi_TYPE STATIC_LIBRARY)
set(__QT_DEPLOY_TARGET_GSS_libgoal_sdi_FILE /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/libGSS_libgoal_sdi.a)
set(__QT_DEPLOY_TARGET_GSS_libgoal_sdi_TYPE STATIC_LIBRARY)
set(__QT_DEPLOY_TARGET_GSS_libgoal_devices_FILE /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/libGSS_libgoal_devices.a)
set(__QT_DEPLOY_TARGET_GSS_libgoal_devices_TYPE STATIC_LIBRARY)
set(__QT_DEPLOY_TARGET_GSS_libgoal_avrouter_FILE /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/libGSS_libgoal_avrouter.a)
set(__QT_DEPLOY_TARGET_GSS_libgoal_avrouter_TYPE STATIC_LIBRARY)
set(__QT_DEPLOY_TARGET_GSS_libgoal_widgets_FILE /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/libGSS_libgoal_widgets.a)
set(__QT_DEPLOY_TARGET_GSS_libgoal_widgets_TYPE STATIC_LIBRARY)
set(__QT_DEPLOY_TARGET_av-routing-and-mixing_FILE /home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing)
set(__QT_DEPLOY_TARGET_av-routing-and-mixing_TYPE EXECUTABLE)
