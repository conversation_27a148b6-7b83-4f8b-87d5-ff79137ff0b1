{"backtrace": 3, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_qml_type_registration", "qt6_add_qml_module"], "files": ["/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 2, "file": 1, "line": 58, "parent": 0}, {"command": 1, "file": 0, "line": 785, "parent": 1}, {"command": 0, "file": 0, "line": 3791, "parent": 2}]}, "id": "av-routing-and-mixing_qmltyperegistration::@6890427a1f51a3e7e1df", "name": "av-routing-and-mixing_qmltyperegistration", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 3, "isGenerated": true, "path": "build/CMakeFiles/av-routing-and-mixing_qmltyperegistration", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/av-routing-and-mixing_qmltyperegistration.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/av-routing-and-mixing_qmltyperegistrations.cpp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}