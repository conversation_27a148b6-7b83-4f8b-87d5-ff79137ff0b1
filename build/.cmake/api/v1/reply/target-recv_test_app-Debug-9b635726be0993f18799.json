{"artifacts": [{"path": "libgoal-ndi/recv_test_app/recv_test_app"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "set_property", "_qt_internal_find_third_party_dependencies", "_pkg_create_imp_target", "_pkg_recalculate", "_pkg_check_modules_internal", "pkg_check_modules"], "files": ["libgoal/libgoal-ndi/recv_test_app/CMakeLists.txt", "libgoal/libgoal-ndi/CMakeLists.txt", "libgoal/libgoal-videoframe/CMakeLists.txt", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaTargets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaConfig.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Config.cmake", "CMakeLists.txt", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "/usr/share/cmake-3.28/Modules/FindOpenGL.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/FindWrapOpenGL.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiDependencies.cmake", "/usr/share/cmake-3.28/Modules/FindPkgConfig.cmake", "libgoal/libgoal-libav/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 10, "parent": 0}, {"command": 1, "file": 0, "line": 24, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 41, "parent": 3}, {"command": 1, "file": 1, "line": 51, "parent": 3}, {"file": 2}, {"command": 1, "file": 2, "line": 70, "parent": 6}, {"file": 6}, {"command": 4, "file": 6, "line": 15, "parent": 8}, {"file": 5, "parent": 9}, {"command": 4, "file": 5, "line": 190, "parent": 10}, {"file": 4, "parent": 11}, {"command": 3, "file": 4, "line": 55, "parent": 12}, {"file": 3, "parent": 13}, {"command": 2, "file": 3, "line": 61, "parent": 14}, {"command": 4, "file": 5, "line": 190, "parent": 10}, {"file": 12, "parent": 16}, {"command": 3, "file": 12, "line": 43, "parent": 17}, {"file": 11, "parent": 18}, {"command": 6, "file": 11, "line": 45, "parent": 19}, {"command": 5, "file": 10, "line": 145, "parent": 20}, {"command": 4, "file": 9, "line": 76, "parent": 21}, {"file": 8, "parent": 22}, {"command": 3, "file": 8, "line": 55, "parent": 23}, {"file": 7, "parent": 24}, {"command": 2, "file": 7, "line": 100, "parent": 25}, {"command": 3, "file": 12, "line": 55, "parent": 17}, {"file": 13, "parent": 27}, {"command": 2, "file": 13, "line": 61, "parent": 28}, {"command": 3, "file": 8, "line": 43, "parent": 23}, {"file": 16, "parent": 30}, {"command": 8, "file": 16, "line": 37, "parent": 31}, {"command": 5, "file": 10, "line": 36, "parent": 32}, {"command": 4, "file": 9, "line": 76, "parent": 33}, {"file": 15, "parent": 34}, {"command": 4, "file": 15, "line": 13, "parent": 35}, {"file": 14, "parent": 36}, {"command": 7, "file": 14, "line": 673, "parent": 37}, {"command": 2, "file": 14, "line": 671, "parent": 37}, {"file": 18}, {"command": 12, "file": 18, "line": 91, "parent": 40}, {"command": 11, "file": 17, "line": 841, "parent": 41}, {"command": 10, "file": 17, "line": 669, "parent": 42}, {"command": 9, "file": 17, "line": 339, "parent": 43}, {"command": 7, "file": 17, "line": 320, "parent": 44}, {"command": 12, "file": 18, "line": 92, "parent": 40}, {"command": 11, "file": 17, "line": 841, "parent": 46}, {"command": 10, "file": 17, "line": 669, "parent": 47}, {"command": 9, "file": 17, "line": 339, "parent": 48}, {"command": 7, "file": 17, "line": 320, "parent": 49}, {"command": 12, "file": 18, "line": 93, "parent": 40}, {"command": 11, "file": 17, "line": 841, "parent": 51}, {"command": 10, "file": 17, "line": 669, "parent": 52}, {"command": 9, "file": 17, "line": 339, "parent": 53}, {"command": 7, "file": 17, "line": 320, "parent": 54}, {"command": 12, "file": 18, "line": 94, "parent": 40}, {"command": 11, "file": 17, "line": 841, "parent": 56}, {"command": 10, "file": 17, "line": 669, "parent": 57}, {"command": 9, "file": 17, "line": 339, "parent": 58}, {"command": 7, "file": 17, "line": 320, "parent": 59}, {"command": 12, "file": 18, "line": 95, "parent": 40}, {"command": 11, "file": 17, "line": 841, "parent": 61}, {"command": 10, "file": 17, "line": 669, "parent": 62}, {"command": 9, "file": 17, "line": 339, "parent": 63}, {"command": 7, "file": 17, "line": 320, "parent": 64}, {"command": 12, "file": 18, "line": 96, "parent": 40}, {"command": 11, "file": 17, "line": 841, "parent": 66}, {"command": 10, "file": 17, "line": 669, "parent": 67}, {"command": 9, "file": 17, "line": 339, "parent": 68}, {"command": 7, "file": 17, "line": 320, "parent": 69}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17"}, {"backtrace": 2, "fragment": "-fPIC"}], "defines": [{"backtrace": 2, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app"}, {"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app"}, {"backtrace": 0, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/recv_test_app_autogen/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtNetwork"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 2, 4]}], "dependencies": [{"backtrace": 2, "id": "GSS_libgoal_ndi::@e8405e6f8fa3070b957e"}, {"backtrace": 2, "id": "GSS_libgoal_videoframe::@2bc16d3bd896b78e6cbd"}, {"backtrace": 2, "id": "GSS_libgoal_utils::@6c8cb491c3014fb511f8"}, {"id": "recv_test_app_autogen_timestamp_deps::@899e4ac02e105eff13be"}, {"backtrace": 0, "id": "recv_test_app_autogen::@899e4ac02e105eff13be"}], "id": "recv_test_app::@899e4ac02e105eff13be", "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/Qt/6.8.2/gcc_64/lib", "role": "libraries"}, {"backtrace": 2, "fragment": "libgoal-ndi/libGSS_libgoal_ndi.a", "role": "libraries"}, {"backtrace": 4, "fragment": "-lndi", "role": "libraries"}, {"backtrace": 5, "fragment": "libgoal-videoframe/libGSS_libgoal_videoframe.a", "role": "libraries"}, {"backtrace": 7, "fragment": "libgoal-libav/libGSS_libgoal_libav.a", "role": "libraries"}, {"backtrace": 5, "fragment": "libgoal-videoframe/libGSS_libgoal_videoframe.a", "role": "libraries"}, {"backtrace": 7, "fragment": "libgoal-libav/libGSS_libgoal_libav.a", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/Qt/6.8.2/gcc_64/lib/libQt6Multimedia.so.6.8.2", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/Qt/6.8.2/gcc_64/lib/libQt6Network.so.6.8.2", "role": "libraries"}, {"backtrace": 26, "fragment": "/usr/lib/x86_64-linux-gnu/libxkbcommon.so", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/Qt/6.8.2/gcc_64/lib/libQt6Widgets.so.6.8.2", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/Qt/6.8.2/gcc_64/lib/libQt6Gui.so.6.8.2", "role": "libraries"}, {"backtrace": 38, "fragment": "/usr/lib/x86_64-linux-gnu/libGLX.so", "role": "libraries"}, {"backtrace": 39, "fragment": "/usr/lib/x86_64-linux-gnu/libOpenGL.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/usr/lib/x86_64-linux-gnu/libavformat.so", "role": "libraries"}, {"backtrace": 50, "fragment": "/usr/lib/x86_64-linux-gnu/libavfilter.so", "role": "libraries"}, {"backtrace": 55, "fragment": "/usr/lib/x86_64-linux-gnu/libswscale.so", "role": "libraries"}, {"backtrace": 60, "fragment": "/usr/lib/x86_64-linux-gnu/libswresample.so", "role": "libraries"}, {"backtrace": 65, "fragment": "/usr/lib/x86_64-linux-gnu/libavcodec.so", "role": "libraries"}, {"backtrace": 70, "fragment": "/usr/lib/x86_64-linux-gnu/libavutil.so", "role": "libraries"}, {"backtrace": 5, "fragment": "libgoal-utils/libGSS_libgoal_utils.a", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/Qt/6.8.2/gcc_64/lib/libQt6Concurrent.so.6.8.2", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/Qt/6.8.2/gcc_64/lib/libQt6Core.so.6.8.2", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/Qt/6.8.2/gcc_64/lib", "role": "libraries"}], "language": "CXX"}, "name": "recv_test_app", "nameOnDisk": "recv_test_app", "paths": {"build": "libgoal-ndi/recv_test_app", "source": "libgoal/libgoal-ndi/recv_test_app"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 5]}, {"name": "", "sourceIndexes": [3, 6]}, {"name": "CMake Rules", "sourceIndexes": [7]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/libgoal-ndi/recv_test_app/recv_test_app_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-ndi/recv_test_app/mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-ndi/recv_test_app/mainwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-ndi/recv_test_app/mainwindow.ui", "sourceGroupIndex": 2}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-ndi/recv_test_app/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/libgoal-ndi/recv_test_app/recv_test_app_autogen/include/ui_mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/libgoal-ndi/recv_test_app/recv_test_app_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/libgoal-ndi/recv_test_app/recv_test_app_autogen/timestamp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}