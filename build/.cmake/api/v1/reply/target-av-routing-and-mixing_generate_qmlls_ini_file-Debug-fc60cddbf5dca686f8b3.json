{"backtrace": 4, "backtraceGraph": {"commands": ["add_custom_target", "_populate_qmlls_ini_file", "_qt_internal_write_deferred_qmlls_ini_file"], "files": ["/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake:999:EVAL", "CMakeLists.txt"], "nodes": [{"file": 2}, {"file": 2, "line": -1, "parent": 0}, {"command": 2, "file": 1, "line": 1, "parent": 1}, {"command": 1, "file": 0, "line": 1232, "parent": 2}, {"command": 0, "file": 0, "line": 1271, "parent": 3}]}, "id": "av-routing-and-mixing_generate_qmlls_ini_file::@6890427a1f51a3e7e1df", "name": "av-routing-and-mixing_generate_qmlls_ini_file", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 4, "isGenerated": true, "path": "build/CMakeFiles/av-routing-and-mixing_generate_qmlls_ini_file", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/av-routing-and-mixing_generate_qmlls_ini_file.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": ".qmlls.ini.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}