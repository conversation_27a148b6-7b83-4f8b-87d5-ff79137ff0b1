{"archive": {}, "artifacts": [{"path": "libgoal-videoframe/libGSS_libgoal_videoframe.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_include_directories"], "files": ["libgoal/libgoal-videoframe/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 1, "file": 0, "line": 48, "parent": 0}, {"command": 1, "file": 0, "line": 70, "parent": 0}, {"command": 2, "file": 0, "line": 42, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17"}, {"backtrace": 2, "fragment": "-fPIC"}], "defines": [{"backtrace": 3, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 3, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe"}, {"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe"}, {"backtrace": 0, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include"}, {"backtrace": 4, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include"}, {"backtrace": 3, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav"}, {"backtrace": 3, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include"}, {"backtrace": 3, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils"}, {"backtrace": 3, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtNetwork"}, {"backtrace": 3, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtWidgets"}, {"backtrace": 3, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 1, 3, 5, 7, 9]}], "dependencies": [{"id": "GSS_libgoal_libav::@d4925a3eb7b45a3a7728"}], "id": "GSS_libgoal_videoframe::@2bc16d3bd896b78e6cbd", "name": "GSS_libgoal_videoframe", "nameOnDisk": "libGSS_libgoal_videoframe.a", "paths": {"build": "libgoal-videoframe", "source": "libgoal/libgoal-videoframe"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 3, 5, 7, 9]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2, 4, 6, 8, 10]}, {"name": "", "sourceIndexes": [11]}, {"name": "CMake Rules", "sourceIndexes": [12]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-videoframe/include/audiocorrection.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-videoframe/include/audiocorrection.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-videoframe/include/colorcorrection.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-videoframe/include/colorcorrection.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-videoframe/include/framemetadata.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-videoframe/include/framemetadata.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-videoframe/include/qvideoframehelper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-videoframe/include/qvideoframehelper.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-videoframe/include/videoframe.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-videoframe/include/videoframe.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/timestamp.rule", "sourceGroupIndex": 3}], "type": "STATIC_LIBRARY"}