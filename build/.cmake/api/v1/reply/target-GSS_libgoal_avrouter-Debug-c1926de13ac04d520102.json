{"archive": {}, "artifacts": [{"path": "libgoal-avrouter/libGSS_libgoal_avrouter.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_include_directories", "target_sources", "__qt_propagate_generated_resource", "_qt_internal_process_resource", "qt6_add_resources", "qt_add_resources"], "files": ["libgoal/libgoal-avrouter/CMakeLists.txt", "/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 18, "parent": 0}, {"command": 1, "file": 0, "line": 56, "parent": 0}, {"command": 2, "file": 0, "line": 51, "parent": 0}, {"command": 7, "file": 0, "line": 36, "parent": 0}, {"command": 6, "file": 1, "line": 458, "parent": 4}, {"command": 5, "file": 1, "line": 406, "parent": 5}, {"command": 4, "file": 1, "line": 2491, "parent": 6}, {"command": 3, "file": 1, "line": 2089, "parent": 7}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17"}, {"backtrace": 2, "fragment": "-fPIC"}], "defines": [{"backtrace": 2, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter"}, {"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter"}, {"backtrace": 0, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include"}, {"backtrace": 3, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtNetwork"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtConcurrent"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.2/gcc_64/include/QtWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 2, 4, 6, 8, 10, 13, 15, 16]}], "dependencies": [{"backtrace": 2, "id": "GSS_libgoal_ndi::@e8405e6f8fa3070b957e"}, {"backtrace": 2, "id": "GSS_libgoal_videoframe::@2bc16d3bd896b78e6cbd"}, {"backtrace": 2, "id": "GSS_libgoal_utils::@6c8cb491c3014fb511f8"}, {"backtrace": 2, "id": "GSS_libgoal_smemory-video::@ce80f594daf5fb6bfe42"}, {"backtrace": 2, "id": "GSS_libgoal_sdi::@83de4cecf005b7ef38bf"}, {"id": "GSS_libgoal_avrouter_autogen_timestamp_deps::@610245c7e5910b70db46"}, {"backtrace": 0, "id": "GSS_libgoal_avrouter_autogen::@610245c7e5910b70db46"}], "id": "GSS_libgoal_avrouter::@610245c7e5910b70db46", "name": "GSS_libgoal_avrouter", "nameOnDisk": "libGSS_libgoal_avrouter.a", "paths": {"build": "libgoal-avrouter", "source": "libgoal/libgoal-avrouter"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4, 6, 8, 10, 13, 15, 16]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 5, 7, 9, 11, 12, 14]}, {"name": "", "sourceIndexes": [17]}, {"name": "CMake Rules", "sourceIndexes": [18, 19]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-avrouter/include/avrouter/avrouter.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-avrouter/include/avrouter/avrouter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-avrouter/include/avrouter/liveinputmanager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-avrouter/include/avrouter/liveinputmanager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-avrouter/include/avrouter/liveoutputmanager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-avrouter/include/avrouter/liveoutputmanager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-avrouter/include/avrouter/videoformatter.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-avrouter/include/avrouter/videoformatter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-avrouter/include/avrouter/fpssync.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-avrouter/include/avrouter/fpssync.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-avrouter/include/avrouter/avr_helper.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-avrouter/include/avrouter/videooutputprocess.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-avrouter/include/avrouter/videooutputprocess.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-avrouter/include/avrouter/openclprocessor.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-avrouter/include/avrouter/openclprocessor.cpp", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "isGenerated": true, "path": "build/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/timestamp.rule", "sourceGroupIndex": 3}], "type": "STATIC_LIBRARY"}