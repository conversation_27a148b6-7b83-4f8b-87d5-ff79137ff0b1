# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.28

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: av-routing-and-mixing
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/PROJECTS/av-routing-and-mixing/build/
# =============================================================================
# Object build statements for EXECUTABLE target av-routing-and-mixing


#############################################
# Order-only phony target for av-routing-and-mixing

build cmake_object_order_depends_target_av-routing-and-mixing: phony || .qt/rcc/qrc_av-routing-and-mixing.cpp .qt/rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp av-routing-and-mixing_autogen av-routing-and-mixing_autogen/mocs_compilation.cpp av-routing-and-mixing_autogen/timestamp av-routing-and-mixing_autogen_timestamp_deps av-routing-and-mixing_generate_qmlls_ini_file av-routing-and-mixing_qmlimportscan av-routing-and-mixing_qmltyperegistrations.cpp cmake_object_order_depends_target_GSS_libgoal_avrouter cmake_object_order_depends_target_GSS_libgoal_avrouter_resources_1 cmake_object_order_depends_target_GSS_libgoal_ndi cmake_object_order_depends_target_GSS_libgoal_sdi cmake_object_order_depends_target_GSS_libgoal_smemory-video cmake_object_order_depends_target_GSS_libgoal_utils cmake_object_order_depends_target_GSS_libgoal_videoframe cmake_object_order_depends_target_GSS_libgoal_widgets com/teslan/av-routing-and-mixing/av-routing-and-mixing.qmltypes meta_types/av-routing-and-mixing_json_file_list.txt meta_types/av-routing-and-mixing_json_file_list.txt.timestamp meta_types/qt6av-routing-and-mixing_debug_metatypes.json meta_types/qt6av-routing-and-mixing_debug_metatypes.json.gen

build CMakeFiles/av-routing-and-mixing.dir/av-routing-and-mixing_autogen/mocs_compilation.cpp.o: CXX_COMPILER__av-routing-and-mixing_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_av-routing-and-mixing
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = CMakeFiles/av-routing-and-mixing.dir/av-routing-and-mixing_autogen/mocs_compilation.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build -I/home/<USER>/PROJECTS/av-routing-and-mixing -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/include -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtQml -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtQuick -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlMeta -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlModels -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript -isystem /opt/Qt/6.8.2/gcc_64/include/QtOpenGL -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles/av-routing-and-mixing.dir
  OBJECT_FILE_DIR = CMakeFiles/av-routing-and-mixing.dir/av-routing-and-mixing_autogen
  TARGET_COMPILE_PDB = CMakeFiles/av-routing-and-mixing.dir/
  TARGET_PDB = av-routing-and-mixing.pdb

build CMakeFiles/av-routing-and-mixing.dir/main.cpp.o: CXX_COMPILER__av-routing-and-mixing_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/main.cpp || cmake_object_order_depends_target_av-routing-and-mixing
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = CMakeFiles/av-routing-and-mixing.dir/main.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build -I/home/<USER>/PROJECTS/av-routing-and-mixing -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/include -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtQml -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtQuick -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlMeta -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlModels -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript -isystem /opt/Qt/6.8.2/gcc_64/include/QtOpenGL -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles/av-routing-and-mixing.dir
  OBJECT_FILE_DIR = CMakeFiles/av-routing-and-mixing.dir
  TARGET_COMPILE_PDB = CMakeFiles/av-routing-and-mixing.dir/
  TARGET_PDB = av-routing-and-mixing.pdb

build CMakeFiles/av-routing-and-mixing.dir/mainwindow.cpp.o: CXX_COMPILER__av-routing-and-mixing_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/mainwindow.cpp || cmake_object_order_depends_target_av-routing-and-mixing
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = CMakeFiles/av-routing-and-mixing.dir/mainwindow.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build -I/home/<USER>/PROJECTS/av-routing-and-mixing -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/include -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtQml -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtQuick -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlMeta -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlModels -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript -isystem /opt/Qt/6.8.2/gcc_64/include/QtOpenGL -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles/av-routing-and-mixing.dir
  OBJECT_FILE_DIR = CMakeFiles/av-routing-and-mixing.dir
  TARGET_COMPILE_PDB = CMakeFiles/av-routing-and-mixing.dir/
  TARGET_PDB = av-routing-and-mixing.pdb

build CMakeFiles/av-routing-and-mixing.dir/build/.qt/rcc/qrc_av-routing-and-mixing.cpp.o: CXX_COMPILER__av-routing-and-mixing_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/.qt/rcc/qrc_av-routing-and-mixing.cpp || cmake_object_order_depends_target_av-routing-and-mixing
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = CMakeFiles/av-routing-and-mixing.dir/build/.qt/rcc/qrc_av-routing-and-mixing.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build -I/home/<USER>/PROJECTS/av-routing-and-mixing -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/include -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtQml -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtQuick -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlMeta -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlModels -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript -isystem /opt/Qt/6.8.2/gcc_64/include/QtOpenGL -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles/av-routing-and-mixing.dir
  OBJECT_FILE_DIR = CMakeFiles/av-routing-and-mixing.dir/build/.qt/rcc
  TARGET_COMPILE_PDB = CMakeFiles/av-routing-and-mixing.dir/
  TARGET_PDB = av-routing-and-mixing.pdb

build CMakeFiles/av-routing-and-mixing.dir/av-routing-and-mixing_qmltyperegistrations.cpp.o: CXX_COMPILER__av-routing-and-mixing_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_qmltyperegistrations.cpp || cmake_object_order_depends_target_av-routing-and-mixing
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = CMakeFiles/av-routing-and-mixing.dir/av-routing-and-mixing_qmltyperegistrations.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build -I/home/<USER>/PROJECTS/av-routing-and-mixing -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/include -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtQml -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtQuick -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlMeta -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlModels -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript -isystem /opt/Qt/6.8.2/gcc_64/include/QtOpenGL -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles/av-routing-and-mixing.dir
  OBJECT_FILE_DIR = CMakeFiles/av-routing-and-mixing.dir
  TARGET_COMPILE_PDB = CMakeFiles/av-routing-and-mixing.dir/
  TARGET_PDB = av-routing-and-mixing.pdb

build CMakeFiles/av-routing-and-mixing.dir/build/.qt/rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp.o: CXX_COMPILER__av-routing-and-mixing_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/.qt/rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp || cmake_object_order_depends_target_av-routing-and-mixing
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = CMakeFiles/av-routing-and-mixing.dir/build/.qt/rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build -I/home/<USER>/PROJECTS/av-routing-and-mixing -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/include -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtQml/6.8.2/QtQml -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtQml -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlIntegration -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtQuick -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlMeta -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlModels -isystem /opt/Qt/6.8.2/gcc_64/include/QtQmlWorkerScript -isystem /opt/Qt/6.8.2/gcc_64/include/QtOpenGL -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = CMakeFiles/av-routing-and-mixing.dir
  OBJECT_FILE_DIR = CMakeFiles/av-routing-and-mixing.dir/build/.qt/rcc
  TARGET_COMPILE_PDB = CMakeFiles/av-routing-and-mixing.dir/
  TARGET_PDB = av-routing-and-mixing.pdb


# =============================================================================
# Link build statements for EXECUTABLE target av-routing-and-mixing


#############################################
# Link the executable av-routing-and-mixing

build av-routing-and-mixing: CXX_EXECUTABLE_LINKER__av-routing-and-mixing_Debug CMakeFiles/av-routing-and-mixing.dir/av-routing-and-mixing_autogen/mocs_compilation.cpp.o CMakeFiles/av-routing-and-mixing.dir/main.cpp.o CMakeFiles/av-routing-and-mixing.dir/mainwindow.cpp.o CMakeFiles/av-routing-and-mixing.dir/build/.qt/rcc/qrc_av-routing-and-mixing.cpp.o CMakeFiles/av-routing-and-mixing.dir/av-routing-and-mixing_qmltyperegistrations.cpp.o CMakeFiles/av-routing-and-mixing.dir/build/.qt/rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp.o | libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir/./.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp.o /opt/Qt/6.8.2/gcc_64/lib/libQt6Quick.so.6.8.2 libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-widgets/libGSS_libgoal_widgets.a libgoal-utils/libGSS_libgoal_utils.a libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-avrouter/libGSS_libgoal_avrouter.a /opt/Qt/6.8.2/gcc_64/lib/libQt6QmlMeta.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6QmlWorkerScript.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6QmlModels.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Qml.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6OpenGL.so.6.8.2 libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-libav/libGSS_libgoal_libav.a libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-libav/libGSS_libgoal_libav.a /usr/lib/x86_64-linux-gnu/libxkbcommon.so /opt/Qt/6.8.2/gcc_64/lib/libQt6Widgets.so.6.8.2 /usr/lib/x86_64-linux-gnu/libavformat.so /usr/lib/x86_64-linux-gnu/libavfilter.so /usr/lib/x86_64-linux-gnu/libswscale.so /usr/lib/x86_64-linux-gnu/libswresample.so /usr/lib/x86_64-linux-gnu/libavcodec.so /usr/lib/x86_64-linux-gnu/libavutil.so libgoal-utils/libGSS_libgoal_utils.a /opt/Qt/6.8.2/gcc_64/lib/libQt6Concurrent.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Multimedia.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Network.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Gui.so.6.8.2 /usr/lib/x86_64-linux-gnu/libGLX.so /usr/lib/x86_64-linux-gnu/libOpenGL.so /opt/Qt/6.8.2/gcc_64/lib/libQt6Core.so.6.8.2 /usr/lib/x86_64-linux-gnu/libOpenCL.so || av-routing-and-mixing_autogen av-routing-and-mixing_autogen_timestamp_deps av-routing-and-mixing_generate_qmlls_ini_file av-routing-and-mixing_qmlimportscan libgoal-avrouter/GSS_libgoal_avrouter_resources_1 libgoal-avrouter/libGSS_libgoal_avrouter.a libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-widgets/libGSS_libgoal_widgets.a
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g
  LINK_LIBRARIES = -Wl,-rpath,/opt/Qt/6.8.2/gcc_64/lib:  libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir/./.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp.o  /opt/Qt/6.8.2/gcc_64/lib/libQt6Quick.so.6.8.2  libgoal-videoframe/libGSS_libgoal_videoframe.a  libgoal-widgets/libGSS_libgoal_widgets.a  libgoal-utils/libGSS_libgoal_utils.a  libgoal-libav/libGSS_libgoal_libav.a  libgoal-ndi/libGSS_libgoal_ndi.a  libgoal-sdi/libGSS_libgoal_sdi.a  libgoal-smemory-video/libGSS_libgoal_smemory-video.a  libgoal-avrouter/libGSS_libgoal_avrouter.a  /opt/Qt/6.8.2/gcc_64/lib/libQt6QmlMeta.so.6.8.2  /opt/Qt/6.8.2/gcc_64/lib/libQt6QmlWorkerScript.so.6.8.2  /opt/Qt/6.8.2/gcc_64/lib/libQt6QmlModels.so.6.8.2  /opt/Qt/6.8.2/gcc_64/lib/libQt6Qml.so.6.8.2  /opt/Qt/6.8.2/gcc_64/lib/libQt6OpenGL.so.6.8.2  libgoal-ndi/libGSS_libgoal_ndi.a  -lndi  libgoal-sdi/libGSS_libgoal_sdi.a  libgoal-smemory-video/libGSS_libgoal_smemory-video.a  libgoal-videoframe/libGSS_libgoal_videoframe.a  libgoal-libav/libGSS_libgoal_libav.a  libgoal-videoframe/libGSS_libgoal_videoframe.a  libgoal-libav/libGSS_libgoal_libav.a  /usr/lib/x86_64-linux-gnu/libxkbcommon.so  /opt/Qt/6.8.2/gcc_64/lib/libQt6Widgets.so.6.8.2  /usr/lib/x86_64-linux-gnu/libavformat.so  /usr/lib/x86_64-linux-gnu/libavfilter.so  /usr/lib/x86_64-linux-gnu/libswscale.so  /usr/lib/x86_64-linux-gnu/libswresample.so  /usr/lib/x86_64-linux-gnu/libavcodec.so  /usr/lib/x86_64-linux-gnu/libavutil.so  libgoal-utils/libGSS_libgoal_utils.a  /opt/Qt/6.8.2/gcc_64/lib/libQt6Concurrent.so.6.8.2  /opt/Qt/6.8.2/gcc_64/lib/libQt6Multimedia.so.6.8.2  /opt/Qt/6.8.2/gcc_64/lib/libQt6Network.so.6.8.2  /opt/Qt/6.8.2/gcc_64/lib/libQt6Gui.so.6.8.2  /usr/lib/x86_64-linux-gnu/libGLX.so  /usr/lib/x86_64-linux-gnu/libOpenGL.so  /opt/Qt/6.8.2/gcc_64/lib/libQt6Core.so.6.8.2  /usr/lib/x86_64-linux-gnu/libOpenCL.so  -Wl,-rpath-link,/opt/Qt/6.8.2/gcc_64/lib
  OBJECT_DIR = CMakeFiles/av-routing-and-mixing.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/av-routing-and-mixing.dir/
  TARGET_FILE = av-routing-and-mixing
  TARGET_PDB = av-routing-and-mixing.pdb


#############################################
# Utility command for av-routing-and-mixing_qmltyperegistration

build av-routing-and-mixing_qmltyperegistration: phony CMakeFiles/av-routing-and-mixing_qmltyperegistration av-routing-and-mixing_qmltyperegistrations.cpp com/teslan/av-routing-and-mixing/av-routing-and-mixing.qmltypes


#############################################
# Utility command for all_qmltyperegistrations

build all_qmltyperegistrations: phony av-routing-and-mixing_qmltyperegistration


#############################################
# Utility command for av-routing-and-mixing_qmllint

build av-routing-and-mixing_qmllint: phony CMakeFiles/av-routing-and-mixing_qmllint all_qmltyperegistrations


#############################################
# Utility command for av-routing-and-mixing_qmllint_json

build av-routing-and-mixing_qmllint_json: phony CMakeFiles/av-routing-and-mixing_qmllint_json all_qmltyperegistrations


#############################################
# Utility command for av-routing-and-mixing_qmllint_module

build av-routing-and-mixing_qmllint_module: phony CMakeFiles/av-routing-and-mixing_qmllint_module all_qmltyperegistrations


#############################################
# Utility command for all_qmllint

build all_qmllint: phony av-routing-and-mixing_qmllint


#############################################
# Utility command for all_qmllint_json

build all_qmllint_json: phony av-routing-and-mixing_qmllint_json


#############################################
# Utility command for all_qmllint_module

build all_qmllint_module: phony av-routing-and-mixing_qmllint_module


#############################################
# Utility command for av-routing-and-mixing_generate_qmlls_ini_file

build av-routing-and-mixing_generate_qmlls_ini_file: phony CMakeFiles/av-routing-and-mixing_generate_qmlls_ini_file /home/<USER>/PROJECTS/av-routing-and-mixing/.qmlls.ini


#############################################
# Utility command for all_aotstats

build all_aotstats: phony CMakeFiles/all_aotstats .rcc/qmlcache/all_aotstats.aotstats .rcc/qmlcache/all_aotstats.txt


#############################################
# Utility command for av-routing-and-mixing_qmlimportscan

build av-routing-and-mixing_qmlimportscan: phony CMakeFiles/av-routing-and-mixing_qmlimportscan .qt/qml_imports/av-routing-and-mixing_build.cmake


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /usr/bin/cmake-gui -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util


#############################################
# Utility command for av-routing-and-mixing_autogen_timestamp_deps

build av-routing-and-mixing_autogen_timestamp_deps: phony CMakeFiles/av-routing-and-mixing_autogen_timestamp_deps libgoal-avrouter/libGSS_libgoal_avrouter.a libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-widgets/libGSS_libgoal_widgets.a


#############################################
# Utility command for av-routing-and-mixing_autogen

build av-routing-and-mixing_autogen: phony CMakeFiles/av-routing-and-mixing_autogen av-routing-and-mixing_autogen/include/ui_mainwindow.h av-routing-and-mixing_autogen/timestamp av-routing-and-mixing_autogen/mocs_compilation.cpp av-routing-and-mixing_autogen_timestamp_deps av-routing-and-mixing_generate_qmlls_ini_file av-routing-and-mixing_qmlimportscan


#############################################
# Custom command for .qt/rcc/qrc_av-routing-and-mixing.cpp

build .qt/rcc/qrc_av-routing-and-mixing.cpp | ${cmake_ninja_workdir}.qt/rcc/qrc_av-routing-and-mixing.cpp: CUSTOM_COMMAND /home/<USER>/PROJECTS/av-routing-and-mixing/quickviewform.qml .qt/rcc/av-routing-and-mixing.qrc /opt/Qt/6.8.2/gcc_64/libexec/rcc || av-routing-and-mixing_autogen av-routing-and-mixing_autogen_timestamp_deps av-routing-and-mixing_generate_qmlls_ini_file av-routing-and-mixing_qmlimportscan libgoal-avrouter/GSS_libgoal_avrouter_autogen libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps libgoal-avrouter/GSS_libgoal_avrouter_resources_1 libgoal-avrouter/libGSS_libgoal_avrouter.a libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-widgets/GSS_libgoal_widgets_autogen libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps libgoal-widgets/libGSS_libgoal_widgets.a
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /opt/Qt/6.8.2/gcc_64/libexec/rcc --output /home/<USER>/PROJECTS/av-routing-and-mixing/build/.qt/rcc/qrc_av-routing-and-mixing.cpp --name av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/build/.qt/rcc/av-routing-and-mixing.qrc
  DESC = Running rcc for resource av-routing-and-mixing
  restat = 1


#############################################
# Custom command for meta_types/qt6av-routing-and-mixing_debug_metatypes.json.gen

build meta_types/qt6av-routing-and-mixing_debug_metatypes.json.gen meta_types/qt6av-routing-and-mixing_debug_metatypes.json | ${cmake_ninja_workdir}meta_types/qt6av-routing-and-mixing_debug_metatypes.json.gen ${cmake_ninja_workdir}meta_types/qt6av-routing-and-mixing_debug_metatypes.json: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/moc meta_types/av-routing-and-mixing_json_file_list.txt || av-routing-and-mixing_autogen av-routing-and-mixing_autogen_timestamp_deps av-routing-and-mixing_generate_qmlls_ini_file av-routing-and-mixing_qmlimportscan libgoal-avrouter/GSS_libgoal_avrouter_autogen libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps libgoal-avrouter/GSS_libgoal_avrouter_resources_1 libgoal-avrouter/libGSS_libgoal_avrouter.a libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-widgets/GSS_libgoal_widgets_autogen libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps libgoal-widgets/libGSS_libgoal_widgets.a
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /opt/Qt/6.8.2/gcc_64/libexec/moc -o /home/<USER>/PROJECTS/av-routing-and-mixing/build/meta_types/qt6av-routing-and-mixing_debug_metatypes.json.gen --collect-json @/home/<USER>/PROJECTS/av-routing-and-mixing/build/meta_types/av-routing-and-mixing_json_file_list.txt && /usr/bin/cmake -E copy_if_different /home/<USER>/PROJECTS/av-routing-and-mixing/build/meta_types/qt6av-routing-and-mixing_debug_metatypes.json.gen /home/<USER>/PROJECTS/av-routing-and-mixing/build/meta_types/qt6av-routing-and-mixing_debug_metatypes.json
  DESC = Running moc --collect-json for target av-routing-and-mixing
  restat = 1


#############################################
# Custom command for av-routing-and-mixing_qmltyperegistrations.cpp

build av-routing-and-mixing_qmltyperegistrations.cpp com/teslan/av-routing-and-mixing/av-routing-and-mixing.qmltypes | ${cmake_ninja_workdir}av-routing-and-mixing_qmltyperegistrations.cpp ${cmake_ninja_workdir}com/teslan/av-routing-and-mixing/av-routing-and-mixing.qmltypes: CUSTOM_COMMAND qmltypes/av-routing-and-mixing_foreign_types.txt meta_types/qt6av-routing-and-mixing_debug_metatypes.json /opt/Qt/6.8.2/gcc_64/libexec/qmltyperegistrar /opt/Qt/6.8.2/gcc_64/metatypes/qt6core_relwithdebinfo_metatypes.json /opt/Qt/6.8.2/gcc_64/metatypes/qt6qml_relwithdebinfo_metatypes.json /opt/Qt/6.8.2/gcc_64/metatypes/qt6network_relwithdebinfo_metatypes.json /opt/Qt/6.8.2/gcc_64/metatypes/qt6widgets_relwithdebinfo_metatypes.json /opt/Qt/6.8.2/gcc_64/metatypes/qt6gui_relwithdebinfo_metatypes.json /opt/Qt/6.8.2/gcc_64/metatypes/qt6quick_relwithdebinfo_metatypes.json /opt/Qt/6.8.2/gcc_64/metatypes/qt6qmlmeta_relwithdebinfo_metatypes.json /opt/Qt/6.8.2/gcc_64/metatypes/qt6qmlmodels_relwithdebinfo_metatypes.json /opt/Qt/6.8.2/gcc_64/metatypes/qt6qmlworkerscript_relwithdebinfo_metatypes.json /opt/Qt/6.8.2/gcc_64/metatypes/qt6opengl_relwithdebinfo_metatypes.json /opt/Qt/6.8.2/gcc_64/metatypes/qt6multimedia_relwithdebinfo_metatypes.json /opt/Qt/6.8.2/gcc_64/metatypes/qt6concurrent_relwithdebinfo_metatypes.json
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /opt/Qt/6.8.2/gcc_64/libexec/qmltyperegistrar --generate-qmltypes=/home/<USER>/PROJECTS/av-routing-and-mixing/build/com/teslan/av-routing-and-mixing/av-routing-and-mixing.qmltypes --import-name=com.teslan.av-routing-and-mixing --major-version=1 --minor-version=0 @/home/<USER>/PROJECTS/av-routing-and-mixing/build/qmltypes/av-routing-and-mixing_foreign_types.txt -o /home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_qmltyperegistrations.cpp /home/<USER>/PROJECTS/av-routing-and-mixing/build/meta_types/qt6av-routing-and-mixing_debug_metatypes.json && /usr/bin/cmake -E make_directory /home/<USER>/PROJECTS/av-routing-and-mixing/build/.qt/qmltypes && /usr/bin/cmake -E touch /home/<USER>/PROJECTS/av-routing-and-mixing/build/.qt/qmltypes/av-routing-and-mixing.qmltypes
  DESC = Automatic QML type registration for target av-routing-and-mixing
  restat = 1


#############################################
# Custom command for .qt/rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp

build .qt/rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp | ${cmake_ninja_workdir}.qt/rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp: CUSTOM_COMMAND com/teslan/av-routing-and-mixing/qmldir .qt/rcc/qmake_com_teslan_av-routing-and-mixing.qrc /opt/Qt/6.8.2/gcc_64/libexec/rcc || av-routing-and-mixing_autogen av-routing-and-mixing_autogen_timestamp_deps av-routing-and-mixing_generate_qmlls_ini_file av-routing-and-mixing_qmlimportscan libgoal-avrouter/GSS_libgoal_avrouter_autogen libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps libgoal-avrouter/GSS_libgoal_avrouter_resources_1 libgoal-avrouter/libGSS_libgoal_avrouter.a libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-widgets/GSS_libgoal_widgets_autogen libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps libgoal-widgets/libGSS_libgoal_widgets.a
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /opt/Qt/6.8.2/gcc_64/libexec/rcc --output /home/<USER>/PROJECTS/av-routing-and-mixing/build/.qt/rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp --name qmake_com_teslan_av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/build/.qt/rcc/qmake_com_teslan_av-routing-and-mixing.qrc
  DESC = Running rcc for resource qmake_com_teslan_av-routing-and-mixing
  restat = 1


#############################################
# Custom command for av-routing-and-mixing_autogen/timestamp

build av-routing-and-mixing_autogen/timestamp av-routing-and-mixing_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}av-routing-and-mixing_autogen/timestamp ${cmake_ninja_workdir}av-routing-and-mixing_autogen/mocs_compilation.cpp: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/libexec/uic || av-routing-and-mixing_autogen_timestamp_deps av-routing-and-mixing_generate_qmlls_ini_file av-routing-and-mixing_qmlimportscan libgoal-avrouter/GSS_libgoal_avrouter_autogen libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps libgoal-avrouter/libGSS_libgoal_avrouter.a libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-widgets/GSS_libgoal_widgets_autogen libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps libgoal-widgets/libGSS_libgoal_widgets.a
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /usr/bin/cmake -E cmake_autogen /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/av-routing-and-mixing_autogen.dir/AutogenInfo.json Debug && /usr/bin/cmake -E touch /home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/timestamp && /usr/bin/cmake -E cmake_transform_depfile Ninja gccdepfile /home/<USER>/PROJECTS/av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/build /home/<USER>/PROJECTS/av-routing-and-mixing/build /home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/deps /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/260ae827193ad7033af85c2ab5f3e1d49580cca84c6e4ca0fda09e4c95733824.d
  DESC = Automatic MOC and UIC for target av-routing-and-mixing
  depfile = /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/260ae827193ad7033af85c2ab5f3e1d49580cca84c6e4ca0fda09e4c95733824.d
  restat = 1


#############################################
# Custom command for meta_types/av-routing-and-mixing_json_file_list.txt

build meta_types/av-routing-and-mixing_json_file_list.txt meta_types/av-routing-and-mixing_json_file_list.txt.timestamp | ${cmake_ninja_workdir}meta_types/av-routing-and-mixing_json_file_list.txt ${cmake_ninja_workdir}meta_types/av-routing-and-mixing_json_file_list.txt.timestamp: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/cmake_automoc_parser av-routing-and-mixing_autogen/timestamp || av-routing-and-mixing_autogen av-routing-and-mixing_autogen_timestamp_deps av-routing-and-mixing_generate_qmlls_ini_file av-routing-and-mixing_qmlimportscan libgoal-avrouter/GSS_libgoal_avrouter_autogen libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps libgoal-avrouter/GSS_libgoal_avrouter_resources_1 libgoal-avrouter/libGSS_libgoal_avrouter.a libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-widgets/GSS_libgoal_widgets_autogen libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps libgoal-widgets/libGSS_libgoal_widgets.a
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /opt/Qt/6.8.2/gcc_64/libexec/cmake_automoc_parser --cmake-autogen-cache-file /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/av-routing-and-mixing_autogen.dir/ParseCache.txt --cmake-autogen-info-file /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/av-routing-and-mixing_autogen.dir/AutogenInfo.json --output-file-path /home/<USER>/PROJECTS/av-routing-and-mixing/build/meta_types/av-routing-and-mixing_json_file_list.txt --timestamp-file-path /home/<USER>/PROJECTS/av-routing-and-mixing/build/meta_types/av-routing-and-mixing_json_file_list.txt.timestamp --cmake-autogen-include-dir-path /home/<USER>/PROJECTS/av-routing-and-mixing/build/av-routing-and-mixing_autogen/include
  DESC = Running AUTOMOC file extraction for target av-routing-and-mixing
  restat = 1


#############################################
# Phony custom command for CMakeFiles/av-routing-and-mixing_qmltyperegistration

build CMakeFiles/av-routing-and-mixing_qmltyperegistration | ${cmake_ninja_workdir}CMakeFiles/av-routing-and-mixing_qmltyperegistration: phony av-routing-and-mixing_qmltyperegistrations.cpp com/teslan/av-routing-and-mixing/av-routing-and-mixing.qmltypes


#############################################
# Custom command for CMakeFiles/av-routing-and-mixing_qmllint

build CMakeFiles/av-routing-and-mixing_qmllint | ${cmake_ninja_workdir}CMakeFiles/av-routing-and-mixing_qmllint: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/bin/qmllint .rcc/qmllint/av-routing-and-mixing.rsp || all_qmltyperegistrations av-routing-and-mixing_qmltyperegistration
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing && /usr/bin/cmake -E echo Nothing\ to\ do\ for\ target\ av-routing-and-mixing_qmllint.


#############################################
# Custom command for CMakeFiles/av-routing-and-mixing_qmllint_json

build CMakeFiles/av-routing-and-mixing_qmllint_json | ${cmake_ninja_workdir}CMakeFiles/av-routing-and-mixing_qmllint_json: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/bin/qmllint .rcc/qmllint/av-routing-and-mixing_json.rsp || all_qmltyperegistrations av-routing-and-mixing_qmltyperegistration
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing


#############################################
# Custom command for CMakeFiles/av-routing-and-mixing_qmllint_module

build CMakeFiles/av-routing-and-mixing_qmllint_module | ${cmake_ninja_workdir}CMakeFiles/av-routing-and-mixing_qmllint_module: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/bin/qmllint .rcc/qmllint/av-routing-and-mixing_module.rsp || all_qmltyperegistrations av-routing-and-mixing_qmltyperegistration
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing && /opt/Qt/6.8.2/gcc_64/bin/qmllint @/home/<USER>/PROJECTS/av-routing-and-mixing/build/.rcc/qmllint/av-routing-and-mixing_module.rsp


#############################################
# Phony custom command for CMakeFiles/av-routing-and-mixing_generate_qmlls_ini_file

build CMakeFiles/av-routing-and-mixing_generate_qmlls_ini_file | ${cmake_ninja_workdir}CMakeFiles/av-routing-and-mixing_generate_qmlls_ini_file: phony /home/<USER>/PROJECTS/av-routing-and-mixing/.qmlls.ini


#############################################
# Custom command for /home/<USER>/PROJECTS/av-routing-and-mixing/.qmlls.ini

build /home/<USER>/PROJECTS/av-routing-and-mixing/.qmlls.ini: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /usr/bin/cmake -E echo [General] > /home/<USER>/PROJECTS/av-routing-and-mixing/.qmlls.ini && /usr/bin/cmake -E echo buildDir=\"/home/<USER>/PROJECTS/av-routing-and-mixing/build\" >> /home/<USER>/PROJECTS/av-routing-and-mixing/.qmlls.ini && /usr/bin/cmake -E echo no-cmake-calls=false >> /home/<USER>/PROJECTS/av-routing-and-mixing/.qmlls.ini && /usr/bin/cmake -E echo_append docDir= >> /home/<USER>/PROJECTS/av-routing-and-mixing/.qmlls.ini && /opt/Qt/6.8.2/gcc_64/bin/qtpaths --query QT_INSTALL_DOCS >> /home/<USER>/PROJECTS/av-routing-and-mixing/.qmlls.ini && /usr/bin/cmake -E echo importPaths=\"/opt/Qt/6.8.2/gcc_64/qml\" >> /home/<USER>/PROJECTS/av-routing-and-mixing/.qmlls.ini
  DESC = Populating .qmlls.ini file
  restat = 1


#############################################
# Custom command for CMakeFiles/all_aotstats

build CMakeFiles/all_aotstats | ${cmake_ninja_workdir}CMakeFiles/all_aotstats: CUSTOM_COMMAND .rcc/qmlcache/all_aotstats.txt
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /usr/bin/cmake -E cat /home/<USER>/PROJECTS/av-routing-and-mixing/build/.rcc/qmlcache/all_aotstats.txt


#############################################
# Custom command for .rcc/qmlcache/all_aotstats.aotstats

build .rcc/qmlcache/all_aotstats.aotstats .rcc/qmlcache/all_aotstats.txt | ${cmake_ninja_workdir}.rcc/qmlcache/all_aotstats.aotstats ${cmake_ninja_workdir}.rcc/qmlcache/all_aotstats.txt: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build && /opt/Qt/6.8.2/gcc_64/libexec/qmlaotstats aggregate /home/<USER>/PROJECTS/av-routing-and-mixing/build/.rcc/qmlcache/all_aotstats.aotstatslist /home/<USER>/PROJECTS/av-routing-and-mixing/build/.rcc/qmlcache/all_aotstats.aotstats && /opt/Qt/6.8.2/gcc_64/libexec/qmlaotstats format /home/<USER>/PROJECTS/av-routing-and-mixing/build/.rcc/qmlcache/all_aotstats.aotstats /home/<USER>/PROJECTS/av-routing-and-mixing/build/.rcc/qmlcache/all_aotstats.txt --empty-modules /home/<USER>/PROJECTS/av-routing-and-mixing/build/.rcc/qmlcache/aotstats_empty_modules.txt
  DESC = Generating .rcc/qmlcache/all_aotstats.aotstats, .rcc/qmlcache/all_aotstats.txt
  restat = 1


#############################################
# Phony custom command for CMakeFiles/av-routing-and-mixing_qmlimportscan

build CMakeFiles/av-routing-and-mixing_qmlimportscan | ${cmake_ninja_workdir}CMakeFiles/av-routing-and-mixing_qmlimportscan: phony .qt/qml_imports/av-routing-and-mixing_build.cmake


#############################################
# Custom command for .qt/qml_imports/av-routing-and-mixing_build.cmake

build .qt/qml_imports/av-routing-and-mixing_build.cmake | ${cmake_ninja_workdir}.qt/qml_imports/av-routing-and-mixing_build.cmake: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/qmlimportscanner .qt/rcc/av-routing-and-mixing.qrc .qt/rcc/qmake_com_teslan_av-routing-and-mixing.qrc
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing && /opt/Qt/6.8.2/gcc_64/libexec/qmlimportscanner @/home/<USER>/PROJECTS/av-routing-and-mixing/build/.qt/qml_imports/av-routing-and-mixing_build.rsp
  DESC = Running qmlimportscanner for av-routing-and-mixing
  restat = 1


#############################################
# Phony custom command for CMakeFiles/av-routing-and-mixing_autogen_timestamp_deps

build CMakeFiles/av-routing-and-mixing_autogen_timestamp_deps | ${cmake_ninja_workdir}CMakeFiles/av-routing-and-mixing_autogen_timestamp_deps: phony libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-widgets/libGSS_libgoal_widgets.a libgoal-sdi/libGSS_libgoal_sdi.a libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-ndi/libGSS_libgoal_ndi.a libgoal-avrouter/libGSS_libgoal_avrouter.a libgoal-utils/libGSS_libgoal_utils.a /opt/Qt/6.8.2/gcc_64/lib/libQt6Widgets.so.6.8.2 /opt/Qt/6.8.2/gcc_64/libexec/uic /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/lib/libQt6Qml.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Core.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Quick.so.6.8.2 libgoal-libav/libGSS_libgoal_libav.a || libgoal-avrouter/GSS_libgoal_avrouter_autogen libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps libgoal-avrouter/libGSS_libgoal_avrouter.a libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-widgets/GSS_libgoal_widgets_autogen libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps libgoal-widgets/libGSS_libgoal_widgets.a


#############################################
# Phony custom command for CMakeFiles/av-routing-and-mixing_autogen

build CMakeFiles/av-routing-and-mixing_autogen av-routing-and-mixing_autogen/include/ui_mainwindow.h | ${cmake_ninja_workdir}CMakeFiles/av-routing-and-mixing_autogen ${cmake_ninja_workdir}av-routing-and-mixing_autogen/include/ui_mainwindow.h: phony av-routing-and-mixing_autogen/timestamp || av-routing-and-mixing_autogen_timestamp_deps av-routing-and-mixing_generate_qmlls_ini_file av-routing-and-mixing_qmlimportscan libgoal-avrouter/GSS_libgoal_avrouter_autogen libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps libgoal-avrouter/libGSS_libgoal_avrouter.a libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-widgets/GSS_libgoal_widgets_autogen libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps libgoal-widgets/libGSS_libgoal_widgets.a

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/PROJECTS/av-routing-and-mixing/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build libgoal/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal && /usr/bin/cmake-gui -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build libgoal/edit_cache: phony libgoal/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build libgoal/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build libgoal/rebuild_cache: phony libgoal/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build libgoal/list_install_components: phony


#############################################
# Utility command for install

build libgoal/CMakeFiles/install.util: CUSTOM_COMMAND libgoal/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build libgoal/install: phony libgoal/CMakeFiles/install.util


#############################################
# Utility command for install/local

build libgoal/CMakeFiles/install/local.util: CUSTOM_COMMAND libgoal/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build libgoal/install/local: phony libgoal/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build libgoal/CMakeFiles/install/strip.util: CUSTOM_COMMAND libgoal/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build libgoal/install/strip: phony libgoal/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/PROJECTS/av-routing-and-mixing/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target GSS_libgoal_ndi


#############################################
# Order-only phony target for GSS_libgoal_ndi

build cmake_object_order_depends_target_GSS_libgoal_ndi: phony || cmake_object_order_depends_target_GSS_libgoal_utils cmake_object_order_depends_target_GSS_libgoal_videoframe libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen/mocs_compilation.cpp libgoal-ndi/GSS_libgoal_ndi_autogen/timestamp libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps

build libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/GSS_libgoal_ndi_autogen/mocs_compilation.cpp.o: CXX_COMPILER__GSS_libgoal_ndi_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/GSS_libgoal_ndi_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_GSS_libgoal_ndi
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/GSS_libgoal_ndi_autogen/mocs_compilation.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/GSS_libgoal_ndi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir
  OBJECT_FILE_DIR = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/GSS_libgoal_ndi_autogen
  TARGET_COMPILE_PDB = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/GSS_libgoal_ndi.pdb
  TARGET_PDB = libgoal-ndi/libGSS_libgoal_ndi.pdb

build libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndifinder.cpp.o: CXX_COMPILER__GSS_libgoal_ndi_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndifinder.cpp || cmake_object_order_depends_target_GSS_libgoal_ndi
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndifinder.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/GSS_libgoal_ndi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir
  OBJECT_FILE_DIR = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi
  TARGET_COMPILE_PDB = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/GSS_libgoal_ndi.pdb
  TARGET_PDB = libgoal-ndi/libGSS_libgoal_ndi.pdb

build libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndireceiver.cpp.o: CXX_COMPILER__GSS_libgoal_ndi_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndireceiver.cpp || cmake_object_order_depends_target_GSS_libgoal_ndi
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndireceiver.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/GSS_libgoal_ndi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir
  OBJECT_FILE_DIR = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi
  TARGET_COMPILE_PDB = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/GSS_libgoal_ndi.pdb
  TARGET_PDB = libgoal-ndi/libGSS_libgoal_ndi.pdb

build libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndisender.cpp.o: CXX_COMPILER__GSS_libgoal_ndi_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndisender.cpp || cmake_object_order_depends_target_GSS_libgoal_ndi
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndisender.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/GSS_libgoal_ndi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir
  OBJECT_FILE_DIR = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi
  TARGET_COMPILE_PDB = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/GSS_libgoal_ndi.pdb
  TARGET_PDB = libgoal-ndi/libGSS_libgoal_ndi.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target GSS_libgoal_ndi


#############################################
# Link the static library libgoal-ndi/libGSS_libgoal_ndi.a

build libgoal-ndi/libGSS_libgoal_ndi.a: CXX_STATIC_LIBRARY_LINKER__GSS_libgoal_ndi_Debug libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/GSS_libgoal_ndi_autogen/mocs_compilation.cpp.o libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndifinder.cpp.o libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndireceiver.cpp.o libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/include/ndi/ndisender.cpp.o || libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/libGSS_libgoal_videoframe.a
  LANGUAGE_COMPILE_FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g
  OBJECT_DIR = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = libgoal-ndi/CMakeFiles/GSS_libgoal_ndi.dir/GSS_libgoal_ndi.pdb
  TARGET_FILE = libgoal-ndi/libGSS_libgoal_ndi.a
  TARGET_PDB = libgoal-ndi/libGSS_libgoal_ndi.pdb


#############################################
# Utility command for edit_cache

build libgoal-ndi/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi && /usr/bin/cmake-gui -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build libgoal-ndi/edit_cache: phony libgoal-ndi/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build libgoal-ndi/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build libgoal-ndi/rebuild_cache: phony libgoal-ndi/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build libgoal-ndi/list_install_components: phony


#############################################
# Utility command for install

build libgoal-ndi/CMakeFiles/install.util: CUSTOM_COMMAND libgoal-ndi/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build libgoal-ndi/install: phony libgoal-ndi/CMakeFiles/install.util


#############################################
# Utility command for install/local

build libgoal-ndi/CMakeFiles/install/local.util: CUSTOM_COMMAND libgoal-ndi/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build libgoal-ndi/install/local: phony libgoal-ndi/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build libgoal-ndi/CMakeFiles/install/strip.util: CUSTOM_COMMAND libgoal-ndi/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build libgoal-ndi/install/strip: phony libgoal-ndi/CMakeFiles/install/strip.util


#############################################
# Utility command for GSS_libgoal_ndi_autogen_timestamp_deps

build libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps: phony libgoal-ndi/CMakeFiles/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/libGSS_libgoal_videoframe.a


#############################################
# Utility command for GSS_libgoal_ndi_autogen

build libgoal-ndi/GSS_libgoal_ndi_autogen: phony libgoal-ndi/CMakeFiles/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen/timestamp libgoal-ndi/GSS_libgoal_ndi_autogen/mocs_compilation.cpp libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps


#############################################
# Custom command for libgoal-ndi/GSS_libgoal_ndi_autogen/timestamp

build libgoal-ndi/GSS_libgoal_ndi_autogen/timestamp libgoal-ndi/GSS_libgoal_ndi_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}libgoal-ndi/GSS_libgoal_ndi_autogen/timestamp ${cmake_ninja_workdir}libgoal-ndi/GSS_libgoal_ndi_autogen/mocs_compilation.cpp: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/libexec/uic || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi && /usr/bin/cmake -E cmake_autogen /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/CMakeFiles/GSS_libgoal_ndi_autogen.dir/AutogenInfo.json Debug && /usr/bin/cmake -E touch /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/GSS_libgoal_ndi_autogen/timestamp && /usr/bin/cmake -E cmake_transform_depfile Ninja gccdepfile /home/<USER>/PROJECTS/av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi /home/<USER>/PROJECTS/av-routing-and-mixing/build /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/GSS_libgoal_ndi_autogen/deps /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/802ed7e5b96aeb16de9b23a1c167fca9e253fdf4e5a1810f9ae352290178d848.d
  DESC = Automatic MOC and UIC for target GSS_libgoal_ndi
  depfile = /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/802ed7e5b96aeb16de9b23a1c167fca9e253fdf4e5a1810f9ae352290178d848.d
  restat = 1


#############################################
# Phony custom command for libgoal-ndi/CMakeFiles/GSS_libgoal_ndi_autogen_timestamp_deps

build libgoal-ndi/CMakeFiles/GSS_libgoal_ndi_autogen_timestamp_deps | ${cmake_ninja_workdir}libgoal-ndi/CMakeFiles/GSS_libgoal_ndi_autogen_timestamp_deps: phony libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-utils/libGSS_libgoal_utils.a /opt/Qt/6.8.2/gcc_64/libexec/uic /opt/Qt/6.8.2/gcc_64/libexec/moc || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a


#############################################
# Phony custom command for libgoal-ndi/CMakeFiles/GSS_libgoal_ndi_autogen

build libgoal-ndi/CMakeFiles/GSS_libgoal_ndi_autogen | ${cmake_ninja_workdir}libgoal-ndi/CMakeFiles/GSS_libgoal_ndi_autogen: phony libgoal-ndi/GSS_libgoal_ndi_autogen/timestamp || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target GSS_libgoal_videoframe


#############################################
# Order-only phony target for GSS_libgoal_videoframe

build cmake_object_order_depends_target_GSS_libgoal_videoframe: phony || cmake_object_order_depends_target_GSS_libgoal_libav libgoal-videoframe/GSS_libgoal_videoframe_autogen/mocs_compilation.cpp libgoal-videoframe/GSS_libgoal_videoframe_autogen/timestamp

build libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/GSS_libgoal_videoframe_autogen/mocs_compilation.cpp.o: CXX_COMPILER__GSS_libgoal_videoframe_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_GSS_libgoal_videoframe
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/GSS_libgoal_videoframe_autogen/mocs_compilation.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir
  OBJECT_FILE_DIR = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/GSS_libgoal_videoframe_autogen
  TARGET_COMPILE_PDB = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/GSS_libgoal_videoframe.pdb
  TARGET_PDB = libgoal-videoframe/libGSS_libgoal_videoframe.pdb

build libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/audiocorrection.cpp.o: CXX_COMPILER__GSS_libgoal_videoframe_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/audiocorrection.cpp || cmake_object_order_depends_target_GSS_libgoal_videoframe
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/audiocorrection.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir
  OBJECT_FILE_DIR = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include
  TARGET_COMPILE_PDB = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/GSS_libgoal_videoframe.pdb
  TARGET_PDB = libgoal-videoframe/libGSS_libgoal_videoframe.pdb

build libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/colorcorrection.cpp.o: CXX_COMPILER__GSS_libgoal_videoframe_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/colorcorrection.cpp || cmake_object_order_depends_target_GSS_libgoal_videoframe
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/colorcorrection.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir
  OBJECT_FILE_DIR = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include
  TARGET_COMPILE_PDB = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/GSS_libgoal_videoframe.pdb
  TARGET_PDB = libgoal-videoframe/libGSS_libgoal_videoframe.pdb

build libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/framemetadata.cpp.o: CXX_COMPILER__GSS_libgoal_videoframe_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/framemetadata.cpp || cmake_object_order_depends_target_GSS_libgoal_videoframe
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/framemetadata.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir
  OBJECT_FILE_DIR = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include
  TARGET_COMPILE_PDB = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/GSS_libgoal_videoframe.pdb
  TARGET_PDB = libgoal-videoframe/libGSS_libgoal_videoframe.pdb

build libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/qvideoframehelper.cpp.o: CXX_COMPILER__GSS_libgoal_videoframe_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/qvideoframehelper.cpp || cmake_object_order_depends_target_GSS_libgoal_videoframe
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/qvideoframehelper.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir
  OBJECT_FILE_DIR = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include
  TARGET_COMPILE_PDB = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/GSS_libgoal_videoframe.pdb
  TARGET_PDB = libgoal-videoframe/libGSS_libgoal_videoframe.pdb

build libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/videoframe.cpp.o: CXX_COMPILER__GSS_libgoal_videoframe_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/videoframe.cpp || cmake_object_order_depends_target_GSS_libgoal_videoframe
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/videoframe.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir
  OBJECT_FILE_DIR = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include
  TARGET_COMPILE_PDB = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/GSS_libgoal_videoframe.pdb
  TARGET_PDB = libgoal-videoframe/libGSS_libgoal_videoframe.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target GSS_libgoal_videoframe


#############################################
# Link the static library libgoal-videoframe/libGSS_libgoal_videoframe.a

build libgoal-videoframe/libGSS_libgoal_videoframe.a: CXX_STATIC_LIBRARY_LINKER__GSS_libgoal_videoframe_Debug libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/GSS_libgoal_videoframe_autogen/mocs_compilation.cpp.o libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/audiocorrection.cpp.o libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/colorcorrection.cpp.o libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/framemetadata.cpp.o libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/qvideoframehelper.cpp.o libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/include/videoframe.cpp.o || libgoal-libav/libGSS_libgoal_libav.a
  LANGUAGE_COMPILE_FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g
  OBJECT_DIR = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe.dir/GSS_libgoal_videoframe.pdb
  TARGET_FILE = libgoal-videoframe/libGSS_libgoal_videoframe.a
  TARGET_PDB = libgoal-videoframe/libGSS_libgoal_videoframe.pdb


#############################################
# Utility command for edit_cache

build libgoal-videoframe/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe && /usr/bin/cmake-gui -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build libgoal-videoframe/edit_cache: phony libgoal-videoframe/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build libgoal-videoframe/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build libgoal-videoframe/rebuild_cache: phony libgoal-videoframe/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build libgoal-videoframe/list_install_components: phony


#############################################
# Utility command for install

build libgoal-videoframe/CMakeFiles/install.util: CUSTOM_COMMAND libgoal-videoframe/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build libgoal-videoframe/install: phony libgoal-videoframe/CMakeFiles/install.util


#############################################
# Utility command for install/local

build libgoal-videoframe/CMakeFiles/install/local.util: CUSTOM_COMMAND libgoal-videoframe/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build libgoal-videoframe/install/local: phony libgoal-videoframe/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build libgoal-videoframe/CMakeFiles/install/strip.util: CUSTOM_COMMAND libgoal-videoframe/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build libgoal-videoframe/install/strip: phony libgoal-videoframe/CMakeFiles/install/strip.util


#############################################
# Utility command for GSS_libgoal_videoframe_autogen_timestamp_deps

build libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps: phony libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe_autogen_timestamp_deps


#############################################
# Utility command for GSS_libgoal_videoframe_autogen

build libgoal-videoframe/GSS_libgoal_videoframe_autogen: phony libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen/timestamp libgoal-videoframe/GSS_libgoal_videoframe_autogen/mocs_compilation.cpp libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps


#############################################
# Custom command for libgoal-videoframe/GSS_libgoal_videoframe_autogen/timestamp

build libgoal-videoframe/GSS_libgoal_videoframe_autogen/timestamp libgoal-videoframe/GSS_libgoal_videoframe_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}libgoal-videoframe/GSS_libgoal_videoframe_autogen/timestamp ${cmake_ninja_workdir}libgoal-videoframe/GSS_libgoal_videoframe_autogen/mocs_compilation.cpp: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/libexec/uic || libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe && /usr/bin/cmake -E cmake_autogen /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe_autogen.dir/AutogenInfo.json Debug && /usr/bin/cmake -E touch /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/timestamp && /usr/bin/cmake -E cmake_transform_depfile Ninja gccdepfile /home/<USER>/PROJECTS/av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe /home/<USER>/PROJECTS/av-routing-and-mixing/build /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe/GSS_libgoal_videoframe_autogen/deps /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/f3527f5a18e817868ff37c3c341ca8a29092bc4f4988b717fff334404421af6a.d
  DESC = Automatic MOC and UIC for target GSS_libgoal_videoframe
  depfile = /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/f3527f5a18e817868ff37c3c341ca8a29092bc4f4988b717fff334404421af6a.d
  restat = 1


#############################################
# Phony custom command for libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe_autogen_timestamp_deps

build libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe_autogen_timestamp_deps | ${cmake_ninja_workdir}libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe_autogen_timestamp_deps: phony /opt/Qt/6.8.2/gcc_64/libexec/uic /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/lib/libQt6Core.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Multimedia.so.6.8.2


#############################################
# Phony custom command for libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe_autogen

build libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe_autogen | ${cmake_ninja_workdir}libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe_autogen: phony libgoal-videoframe/GSS_libgoal_videoframe_autogen/timestamp || libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target GSS_libgoal_utils


#############################################
# Order-only phony target for GSS_libgoal_utils

build cmake_object_order_depends_target_GSS_libgoal_utils: phony || libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen/mocs_compilation.cpp libgoal-utils/GSS_libgoal_utils_autogen/timestamp libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps

build libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils_autogen/mocs_compilation.cpp.o: CXX_COMPILER__GSS_libgoal_utils_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_GSS_libgoal_utils
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB
  DEP_FILE = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils_autogen/mocs_compilation.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir
  OBJECT_FILE_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils_autogen
  TARGET_COMPILE_PDB = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils.pdb
  TARGET_PDB = libgoal-utils/libGSS_libgoal_utils.pdb

build libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/doitlater.cpp.o: CXX_COMPILER__GSS_libgoal_utils_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/doitlater.cpp || cmake_object_order_depends_target_GSS_libgoal_utils
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB
  DEP_FILE = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/doitlater.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir
  OBJECT_FILE_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include
  TARGET_COMPILE_PDB = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils.pdb
  TARGET_PDB = libgoal-utils/libGSS_libgoal_utils.pdb

build libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/memorypool.cpp.o: CXX_COMPILER__GSS_libgoal_utils_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/memorypool.cpp || cmake_object_order_depends_target_GSS_libgoal_utils
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB
  DEP_FILE = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/memorypool.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir
  OBJECT_FILE_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include
  TARGET_COMPILE_PDB = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils.pdb
  TARGET_PDB = libgoal-utils/libGSS_libgoal_utils.pdb

build libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/qthreadedobject.cpp.o: CXX_COMPILER__GSS_libgoal_utils_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/qthreadedobject.cpp || cmake_object_order_depends_target_GSS_libgoal_utils
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB
  DEP_FILE = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/qthreadedobject.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir
  OBJECT_FILE_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include
  TARGET_COMPILE_PDB = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils.pdb
  TARGET_PDB = libgoal-utils/libGSS_libgoal_utils.pdb

build libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/runguard.cpp.o: CXX_COMPILER__GSS_libgoal_utils_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/runguard.cpp || cmake_object_order_depends_target_GSS_libgoal_utils
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB
  DEP_FILE = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/runguard.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir
  OBJECT_FILE_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include
  TARGET_COMPILE_PDB = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils.pdb
  TARGET_PDB = libgoal-utils/libGSS_libgoal_utils.pdb

build libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/universalplayer.cpp.o: CXX_COMPILER__GSS_libgoal_utils_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/universalplayer.cpp || cmake_object_order_depends_target_GSS_libgoal_utils
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB
  DEP_FILE = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/universalplayer.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir
  OBJECT_FILE_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include
  TARGET_COMPILE_PDB = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils.pdb
  TARGET_PDB = libgoal-utils/libGSS_libgoal_utils.pdb

build libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/freqsync.cpp.o: CXX_COMPILER__GSS_libgoal_utils_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/freqsync.cpp || cmake_object_order_depends_target_GSS_libgoal_utils
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB
  DEP_FILE = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/freqsync.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir
  OBJECT_FILE_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include
  TARGET_COMPILE_PDB = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils.pdb
  TARGET_PDB = libgoal-utils/libGSS_libgoal_utils.pdb

build libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/timedqueue.cpp.o: CXX_COMPILER__GSS_libgoal_utils_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/timedqueue.cpp || cmake_object_order_depends_target_GSS_libgoal_utils
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB
  DEP_FILE = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/timedqueue.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir
  OBJECT_FILE_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include
  TARGET_COMPILE_PDB = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils.pdb
  TARGET_PDB = libgoal-utils/libGSS_libgoal_utils.pdb

build libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/goalsharedmemory.cpp.o: CXX_COMPILER__GSS_libgoal_utils_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsharedmemory.cpp || cmake_object_order_depends_target_GSS_libgoal_utils
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB
  DEP_FILE = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/goalsharedmemory.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir
  OBJECT_FILE_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include
  TARGET_COMPILE_PDB = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils.pdb
  TARGET_PDB = libgoal-utils/libGSS_libgoal_utils.pdb

build libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/goalsystemsemaphore.cpp.o: CXX_COMPILER__GSS_libgoal_utils_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsystemsemaphore.cpp || cmake_object_order_depends_target_GSS_libgoal_utils
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB
  DEP_FILE = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/goalsystemsemaphore.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir
  OBJECT_FILE_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include
  TARGET_COMPILE_PDB = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils.pdb
  TARGET_PDB = libgoal-utils/libGSS_libgoal_utils.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target GSS_libgoal_utils


#############################################
# Link the static library libgoal-utils/libGSS_libgoal_utils.a

build libgoal-utils/libGSS_libgoal_utils.a: CXX_STATIC_LIBRARY_LINKER__GSS_libgoal_utils_Debug libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils_autogen/mocs_compilation.cpp.o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/doitlater.cpp.o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/memorypool.cpp.o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/qthreadedobject.cpp.o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/runguard.cpp.o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/universalplayer.cpp.o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/freqsync.cpp.o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/timedqueue.cpp.o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/goalsharedmemory.cpp.o libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/include/goalsystemsemaphore.cpp.o || libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps
  LANGUAGE_COMPILE_FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g
  OBJECT_DIR = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = libgoal-utils/CMakeFiles/GSS_libgoal_utils.dir/GSS_libgoal_utils.pdb
  TARGET_FILE = libgoal-utils/libGSS_libgoal_utils.a
  TARGET_PDB = libgoal-utils/libGSS_libgoal_utils.pdb


#############################################
# Utility command for edit_cache

build libgoal-utils/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils && /usr/bin/cmake-gui -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build libgoal-utils/edit_cache: phony libgoal-utils/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build libgoal-utils/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build libgoal-utils/rebuild_cache: phony libgoal-utils/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build libgoal-utils/list_install_components: phony


#############################################
# Utility command for install

build libgoal-utils/CMakeFiles/install.util: CUSTOM_COMMAND libgoal-utils/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build libgoal-utils/install: phony libgoal-utils/CMakeFiles/install.util


#############################################
# Utility command for install/local

build libgoal-utils/CMakeFiles/install/local.util: CUSTOM_COMMAND libgoal-utils/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build libgoal-utils/install/local: phony libgoal-utils/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build libgoal-utils/CMakeFiles/install/strip.util: CUSTOM_COMMAND libgoal-utils/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build libgoal-utils/install/strip: phony libgoal-utils/CMakeFiles/install/strip.util


#############################################
# Utility command for GSS_libgoal_utils_autogen_timestamp_deps

build libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps: phony libgoal-utils/CMakeFiles/GSS_libgoal_utils_autogen_timestamp_deps


#############################################
# Utility command for GSS_libgoal_utils_autogen

build libgoal-utils/GSS_libgoal_utils_autogen: phony libgoal-utils/CMakeFiles/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen/timestamp libgoal-utils/GSS_libgoal_utils_autogen/mocs_compilation.cpp libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps


#############################################
# Custom command for libgoal-utils/GSS_libgoal_utils_autogen/timestamp

build libgoal-utils/GSS_libgoal_utils_autogen/timestamp libgoal-utils/GSS_libgoal_utils_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}libgoal-utils/GSS_libgoal_utils_autogen/timestamp ${cmake_ninja_workdir}libgoal-utils/GSS_libgoal_utils_autogen/mocs_compilation.cpp: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/libexec/uic || libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils && /usr/bin/cmake -E cmake_autogen /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/CMakeFiles/GSS_libgoal_utils_autogen.dir/AutogenInfo.json Debug && /usr/bin/cmake -E touch /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/timestamp && /usr/bin/cmake -E cmake_transform_depfile Ninja gccdepfile /home/<USER>/PROJECTS/av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils /home/<USER>/PROJECTS/av-routing-and-mixing/build /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils/GSS_libgoal_utils_autogen/deps /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/c722b75a6b18d62f9a4ab93bf8d2c3d7b7e8a8b952514b3ac60d43a57506764d.d
  DESC = Automatic MOC and UIC for target GSS_libgoal_utils
  depfile = /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/c722b75a6b18d62f9a4ab93bf8d2c3d7b7e8a8b952514b3ac60d43a57506764d.d
  restat = 1


#############################################
# Phony custom command for libgoal-utils/CMakeFiles/GSS_libgoal_utils_autogen_timestamp_deps

build libgoal-utils/CMakeFiles/GSS_libgoal_utils_autogen_timestamp_deps | ${cmake_ninja_workdir}libgoal-utils/CMakeFiles/GSS_libgoal_utils_autogen_timestamp_deps: phony /opt/Qt/6.8.2/gcc_64/libexec/uic /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/lib/libQt6Concurrent.so.6.8.2


#############################################
# Phony custom command for libgoal-utils/CMakeFiles/GSS_libgoal_utils_autogen

build libgoal-utils/CMakeFiles/GSS_libgoal_utils_autogen | ${cmake_ninja_workdir}libgoal-utils/CMakeFiles/GSS_libgoal_utils_autogen: phony libgoal-utils/GSS_libgoal_utils_autogen/timestamp || libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target NDIStream_send_test_app


#############################################
# Order-only phony target for NDIStream_send_test_app

build cmake_object_order_depends_target_NDIStream_send_test_app: phony || cmake_object_order_depends_target_GSS_libgoal_ndi cmake_object_order_depends_target_GSS_libgoal_smemory-video cmake_object_order_depends_target_GSS_libgoal_utils cmake_object_order_depends_target_GSS_libgoal_videoframe libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/mocs_compilation.cpp libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/timestamp libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen_timestamp_deps

build libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/NDIStream_send_test_app_autogen/mocs_compilation.cpp.o: CXX_COMPILER__NDIStream_send_test_app_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_NDIStream_send_test_app
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/NDIStream_send_test_app_autogen/mocs_compilation.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir
  OBJECT_FILE_DIR = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/NDIStream_send_test_app_autogen
  TARGET_COMPILE_PDB = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/
  TARGET_PDB = libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app.pdb

build libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/include/mediaplayervlc.cpp.o: CXX_COMPILER__NDIStream_send_test_app_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/include/mediaplayervlc.cpp || cmake_object_order_depends_target_NDIStream_send_test_app
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/include/mediaplayervlc.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir
  OBJECT_FILE_DIR = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/include
  TARGET_COMPILE_PDB = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/
  TARGET_PDB = libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app.pdb

build libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/mainwindow.cpp.o: CXX_COMPILER__NDIStream_send_test_app_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/mainwindow.cpp || cmake_object_order_depends_target_NDIStream_send_test_app
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/mainwindow.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir
  OBJECT_FILE_DIR = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir
  TARGET_COMPILE_PDB = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/
  TARGET_PDB = libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app.pdb

build libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/main.cpp.o: CXX_COMPILER__NDIStream_send_test_app_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/main.cpp || cmake_object_order_depends_target_NDIStream_send_test_app
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/main.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir
  OBJECT_FILE_DIR = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir
  TARGET_COMPILE_PDB = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/
  TARGET_PDB = libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app.pdb


# =============================================================================
# Link build statements for EXECUTABLE target NDIStream_send_test_app


#############################################
# Link the executable libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app

build libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app: CXX_EXECUTABLE_LINKER__NDIStream_send_test_app_Debug libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/NDIStream_send_test_app_autogen/mocs_compilation.cpp.o libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/include/mediaplayervlc.cpp.o libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/mainwindow.cpp.o libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/main.cpp.o | libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/libGSS_libgoal_ndi.a libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-libav/libGSS_libgoal_libav.a libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-libav/libGSS_libgoal_libav.a libgoal-videoframe/libGSS_libgoal_videoframe.a /opt/Qt/6.8.2/gcc_64/lib/libQt6Widgets.so.6.8.2 /usr/lib/x86_64-linux-gnu/libavformat.so /usr/lib/x86_64-linux-gnu/libavfilter.so /usr/lib/x86_64-linux-gnu/libswscale.so /usr/lib/x86_64-linux-gnu/libswresample.so /usr/lib/x86_64-linux-gnu/libavcodec.so /usr/lib/x86_64-linux-gnu/libavutil.so libgoal-utils/libGSS_libgoal_utils.a /opt/Qt/6.8.2/gcc_64/lib/libQt6Concurrent.so.6.8.2 /usr/lib/x86_64-linux-gnu/libxkbcommon.so /opt/Qt/6.8.2/gcc_64/lib/libQt6Multimedia.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Gui.so.6.8.2 /usr/lib/x86_64-linux-gnu/libGLX.so /usr/lib/x86_64-linux-gnu/libOpenGL.so /opt/Qt/6.8.2/gcc_64/lib/libQt6Network.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Core.so.6.8.2 || libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/libGSS_libgoal_videoframe.a
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g
  LINK_LIBRARIES = -Wl,-rpath,/opt/Qt/6.8.2/gcc_64/lib  libgoal-libav/libGSS_libgoal_libav.a  libgoal-ndi/libGSS_libgoal_ndi.a  libgoal-smemory-video/libGSS_libgoal_smemory-video.a  -lvlc  -lvlccore  -lX11  -lndi  libgoal-libav/libGSS_libgoal_libav.a  libgoal-videoframe/libGSS_libgoal_videoframe.a  libgoal-libav/libGSS_libgoal_libav.a  libgoal-videoframe/libGSS_libgoal_videoframe.a  /opt/Qt/6.8.2/gcc_64/lib/libQt6Widgets.so.6.8.2  /usr/lib/x86_64-linux-gnu/libavformat.so  /usr/lib/x86_64-linux-gnu/libavfilter.so  /usr/lib/x86_64-linux-gnu/libswscale.so  /usr/lib/x86_64-linux-gnu/libswresample.so  /usr/lib/x86_64-linux-gnu/libavcodec.so  /usr/lib/x86_64-linux-gnu/libavutil.so  libgoal-utils/libGSS_libgoal_utils.a  /opt/Qt/6.8.2/gcc_64/lib/libQt6Concurrent.so.6.8.2  /usr/lib/x86_64-linux-gnu/libxkbcommon.so  /opt/Qt/6.8.2/gcc_64/lib/libQt6Multimedia.so.6.8.2  /opt/Qt/6.8.2/gcc_64/lib/libQt6Gui.so.6.8.2  /usr/lib/x86_64-linux-gnu/libGLX.so  /usr/lib/x86_64-linux-gnu/libOpenGL.so  /opt/Qt/6.8.2/gcc_64/lib/libQt6Network.so.6.8.2  /opt/Qt/6.8.2/gcc_64/lib/libQt6Core.so.6.8.2  -Wl,-rpath-link,/opt/Qt/6.8.2/gcc_64/lib
  OBJECT_DIR = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app.dir/
  TARGET_FILE = libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app
  TARGET_PDB = libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app.pdb


#############################################
# Utility command for edit_cache

build libgoal-ndi/NDIStream_send_test_app/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app && /usr/bin/cmake-gui -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build libgoal-ndi/NDIStream_send_test_app/edit_cache: phony libgoal-ndi/NDIStream_send_test_app/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build libgoal-ndi/NDIStream_send_test_app/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build libgoal-ndi/NDIStream_send_test_app/rebuild_cache: phony libgoal-ndi/NDIStream_send_test_app/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build libgoal-ndi/NDIStream_send_test_app/list_install_components: phony


#############################################
# Utility command for install

build libgoal-ndi/NDIStream_send_test_app/CMakeFiles/install.util: CUSTOM_COMMAND libgoal-ndi/NDIStream_send_test_app/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build libgoal-ndi/NDIStream_send_test_app/install: phony libgoal-ndi/NDIStream_send_test_app/CMakeFiles/install.util


#############################################
# Utility command for install/local

build libgoal-ndi/NDIStream_send_test_app/CMakeFiles/install/local.util: CUSTOM_COMMAND libgoal-ndi/NDIStream_send_test_app/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build libgoal-ndi/NDIStream_send_test_app/install/local: phony libgoal-ndi/NDIStream_send_test_app/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build libgoal-ndi/NDIStream_send_test_app/CMakeFiles/install/strip.util: CUSTOM_COMMAND libgoal-ndi/NDIStream_send_test_app/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build libgoal-ndi/NDIStream_send_test_app/install/strip: phony libgoal-ndi/NDIStream_send_test_app/CMakeFiles/install/strip.util


#############################################
# Utility command for NDIStream_send_test_app_autogen_timestamp_deps

build libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen_timestamp_deps: phony libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-videoframe/libGSS_libgoal_videoframe.a


#############################################
# Utility command for NDIStream_send_test_app_autogen

build libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen: phony libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app_autogen libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include/ui_mainwindow.h libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/timestamp libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/mocs_compilation.cpp libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen_timestamp_deps


#############################################
# Custom command for libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/timestamp

build libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/timestamp libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/timestamp ${cmake_ninja_workdir}libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/mocs_compilation.cpp: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/libexec/uic || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app && /usr/bin/cmake -E cmake_autogen /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app_autogen.dir/AutogenInfo.json Debug && /usr/bin/cmake -E touch /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/timestamp && /usr/bin/cmake -E cmake_transform_depfile Ninja gccdepfile /home/<USER>/PROJECTS/av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app /home/<USER>/PROJECTS/av-routing-and-mixing/build /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/deps /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/72a542dc646994527c01a114d863a00506a2451c1d8a713a01530e7080e2aead.d
  DESC = Automatic MOC and UIC for target NDIStream_send_test_app
  depfile = /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/72a542dc646994527c01a114d863a00506a2451c1d8a713a01530e7080e2aead.d
  restat = 1


#############################################
# Phony custom command for libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app_autogen_timestamp_deps

build libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app_autogen_timestamp_deps | ${cmake_ninja_workdir}libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app_autogen_timestamp_deps: phony libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-ndi/libGSS_libgoal_ndi.a /opt/Qt/6.8.2/gcc_64/lib/libQt6Widgets.so.6.8.2 /opt/Qt/6.8.2/gcc_64/libexec/uic /opt/Qt/6.8.2/gcc_64/libexec/moc libgoal-libav/libGSS_libgoal_libav.a || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a


#############################################
# Phony custom command for libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app_autogen

build libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app_autogen libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include/ui_mainwindow.h | ${cmake_ninja_workdir}libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app_autogen ${cmake_ninja_workdir}libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/include/ui_mainwindow.h: phony libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/timestamp || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target GSS_libgoal_libav


#############################################
# Order-only phony target for GSS_libgoal_libav

build cmake_object_order_depends_target_GSS_libgoal_libav: phony || cmake_object_order_depends_target_GSS_libgoal_utils libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen/mocs_compilation.cpp libgoal-libav/GSS_libgoal_libav_autogen/timestamp libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav_autogen/mocs_compilation.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav_autogen/mocs_compilation.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav_autogen
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/imageloader.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/imageloader.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/imageloader.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavbuffer.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavbuffer.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavbuffer.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdecoder.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdecoder.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdirectsaver.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdirectsaver.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdirectsaver.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavencoder.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavencoder.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavencoder.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavfilter.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavfilter.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavfilter.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavplayer.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayer.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavplayer.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavplayerdirector.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayerdirector.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavplayerdirector.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libswshelper.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libswshelper.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libswshelper.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/simpleexportdecoder.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/simpleexportdecoder.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/simpleexportdecoder.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/videoframeexporter.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/videoframeexporter.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/videoframeexporter.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/filtercomplex.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/filtercomplex.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/filtercomplex.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdecoder2.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder2.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdecoder2.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavreader.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavreader.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavreader.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb

build libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavwriter.cpp.o: CXX_COMPILER__GSS_libgoal_libav_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavwriter.cpp || cmake_object_order_depends_target_GSS_libgoal_libav
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavwriter.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  OBJECT_FILE_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target GSS_libgoal_libav


#############################################
# Link the static library libgoal-libav/libGSS_libgoal_libav.a

build libgoal-libav/libGSS_libgoal_libav.a: CXX_STATIC_LIBRARY_LINKER__GSS_libgoal_libav_Debug libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav_autogen/mocs_compilation.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/imageloader.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavbuffer.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdecoder.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdirectsaver.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavencoder.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavfilter.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavplayer.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavplayerdirector.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libswshelper.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/simpleexportdecoder.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/videoframeexporter.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/filtercomplex.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavdecoder2.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavreader.cpp.o libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/include/libav/libavwriter.cpp.o || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps
  LANGUAGE_COMPILE_FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g
  OBJECT_DIR = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = libgoal-libav/CMakeFiles/GSS_libgoal_libav.dir/GSS_libgoal_libav.pdb
  TARGET_FILE = libgoal-libav/libGSS_libgoal_libav.a
  TARGET_PDB = libgoal-libav/libGSS_libgoal_libav.pdb


#############################################
# Utility command for edit_cache

build libgoal-libav/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav && /usr/bin/cmake-gui -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build libgoal-libav/edit_cache: phony libgoal-libav/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build libgoal-libav/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build libgoal-libav/rebuild_cache: phony libgoal-libav/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build libgoal-libav/list_install_components: phony


#############################################
# Utility command for install

build libgoal-libav/CMakeFiles/install.util: CUSTOM_COMMAND libgoal-libav/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build libgoal-libav/install: phony libgoal-libav/CMakeFiles/install.util


#############################################
# Utility command for install/local

build libgoal-libav/CMakeFiles/install/local.util: CUSTOM_COMMAND libgoal-libav/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build libgoal-libav/install/local: phony libgoal-libav/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build libgoal-libav/CMakeFiles/install/strip.util: CUSTOM_COMMAND libgoal-libav/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build libgoal-libav/install/strip: phony libgoal-libav/CMakeFiles/install/strip.util


#############################################
# Utility command for GSS_libgoal_libav_autogen_timestamp_deps

build libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps: phony libgoal-libav/CMakeFiles/GSS_libgoal_libav_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a


#############################################
# Utility command for GSS_libgoal_libav_autogen

build libgoal-libav/GSS_libgoal_libav_autogen: phony libgoal-libav/CMakeFiles/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen/timestamp libgoal-libav/GSS_libgoal_libav_autogen/mocs_compilation.cpp libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps


#############################################
# Custom command for libgoal-libav/GSS_libgoal_libav_autogen/timestamp

build libgoal-libav/GSS_libgoal_libav_autogen/timestamp libgoal-libav/GSS_libgoal_libav_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}libgoal-libav/GSS_libgoal_libav_autogen/timestamp ${cmake_ninja_workdir}libgoal-libav/GSS_libgoal_libav_autogen/mocs_compilation.cpp: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/libexec/uic || libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav && /usr/bin/cmake -E cmake_autogen /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/CMakeFiles/GSS_libgoal_libav_autogen.dir/AutogenInfo.json Debug && /usr/bin/cmake -E touch /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/timestamp && /usr/bin/cmake -E cmake_transform_depfile Ninja gccdepfile /home/<USER>/PROJECTS/av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav /home/<USER>/PROJECTS/av-routing-and-mixing/build /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/deps /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/163e39b2d25c7cc64cf600153dcf7106eebde809361453e5c03298d3106554d5.d
  DESC = Automatic MOC and UIC for target GSS_libgoal_libav
  depfile = /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/163e39b2d25c7cc64cf600153dcf7106eebde809361453e5c03298d3106554d5.d
  restat = 1


#############################################
# Phony custom command for libgoal-libav/CMakeFiles/GSS_libgoal_libav_autogen_timestamp_deps

build libgoal-libav/CMakeFiles/GSS_libgoal_libav_autogen_timestamp_deps | ${cmake_ninja_workdir}libgoal-libav/CMakeFiles/GSS_libgoal_libav_autogen_timestamp_deps: phony libgoal-utils/libGSS_libgoal_utils.a /opt/Qt/6.8.2/gcc_64/lib/libQt6Widgets.so.6.8.2 /opt/Qt/6.8.2/gcc_64/libexec/uic /opt/Qt/6.8.2/gcc_64/libexec/moc || libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a


#############################################
# Phony custom command for libgoal-libav/CMakeFiles/GSS_libgoal_libav_autogen

build libgoal-libav/CMakeFiles/GSS_libgoal_libav_autogen | ${cmake_ninja_workdir}libgoal-libav/CMakeFiles/GSS_libgoal_libav_autogen: phony libgoal-libav/GSS_libgoal_libav_autogen/timestamp || libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target GSS_libgoal_smemory-video


#############################################
# Order-only phony target for GSS_libgoal_smemory-video

build cmake_object_order_depends_target_GSS_libgoal_smemory-video: phony || cmake_object_order_depends_target_GSS_libgoal_utils cmake_object_order_depends_target_GSS_libgoal_videoframe libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/mocs_compilation.cpp libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/timestamp libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps

build libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/GSS_libgoal_smemory-video_autogen/mocs_compilation.cpp.o: CXX_COMPILER__GSS_libgoal_smemory-video_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_GSS_libgoal_smemory-video
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/GSS_libgoal_smemory-video_autogen/mocs_compilation.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets
  OBJECT_DIR = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir
  OBJECT_FILE_DIR = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/GSS_libgoal_smemory-video_autogen
  TARGET_COMPILE_PDB = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/GSS_libgoal_smemory-video.pdb
  TARGET_PDB = libgoal-smemory-video/libGSS_libgoal_smemory-video.pdb

build libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/include/smemory-video/sharedmemoryvideoplayer.cpp.o: CXX_COMPILER__GSS_libgoal_smemory-video_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideoplayer.cpp || cmake_object_order_depends_target_GSS_libgoal_smemory-video
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/include/smemory-video/sharedmemoryvideoplayer.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets
  OBJECT_DIR = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir
  OBJECT_FILE_DIR = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/include/smemory-video
  TARGET_COMPILE_PDB = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/GSS_libgoal_smemory-video.pdb
  TARGET_PDB = libgoal-smemory-video/libGSS_libgoal_smemory-video.pdb

build libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/include/smemory-video/sharedmemoryvideosender.cpp.o: CXX_COMPILER__GSS_libgoal_smemory-video_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideosender.cpp || cmake_object_order_depends_target_GSS_libgoal_smemory-video
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/include/smemory-video/sharedmemoryvideosender.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets
  OBJECT_DIR = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir
  OBJECT_FILE_DIR = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/include/smemory-video
  TARGET_COMPILE_PDB = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/GSS_libgoal_smemory-video.pdb
  TARGET_PDB = libgoal-smemory-video/libGSS_libgoal_smemory-video.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target GSS_libgoal_smemory-video


#############################################
# Link the static library libgoal-smemory-video/libGSS_libgoal_smemory-video.a

build libgoal-smemory-video/libGSS_libgoal_smemory-video.a: CXX_STATIC_LIBRARY_LINKER__GSS_libgoal_smemory-video_Debug libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/GSS_libgoal_smemory-video_autogen/mocs_compilation.cpp.o libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/include/smemory-video/sharedmemoryvideoplayer.cpp.o libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/include/smemory-video/sharedmemoryvideosender.cpp.o || libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/libGSS_libgoal_videoframe.a
  LANGUAGE_COMPILE_FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g
  OBJECT_DIR = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video.dir/GSS_libgoal_smemory-video.pdb
  TARGET_FILE = libgoal-smemory-video/libGSS_libgoal_smemory-video.a
  TARGET_PDB = libgoal-smemory-video/libGSS_libgoal_smemory-video.pdb


#############################################
# Utility command for edit_cache

build libgoal-smemory-video/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video && /usr/bin/cmake-gui -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build libgoal-smemory-video/edit_cache: phony libgoal-smemory-video/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build libgoal-smemory-video/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build libgoal-smemory-video/rebuild_cache: phony libgoal-smemory-video/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build libgoal-smemory-video/list_install_components: phony


#############################################
# Utility command for install

build libgoal-smemory-video/CMakeFiles/install.util: CUSTOM_COMMAND libgoal-smemory-video/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build libgoal-smemory-video/install: phony libgoal-smemory-video/CMakeFiles/install.util


#############################################
# Utility command for install/local

build libgoal-smemory-video/CMakeFiles/install/local.util: CUSTOM_COMMAND libgoal-smemory-video/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build libgoal-smemory-video/install/local: phony libgoal-smemory-video/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build libgoal-smemory-video/CMakeFiles/install/strip.util: CUSTOM_COMMAND libgoal-smemory-video/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build libgoal-smemory-video/install/strip: phony libgoal-smemory-video/CMakeFiles/install/strip.util


#############################################
# Utility command for GSS_libgoal_smemory-video_autogen_timestamp_deps

build libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps: phony libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/libGSS_libgoal_videoframe.a


#############################################
# Utility command for GSS_libgoal_smemory-video_autogen

build libgoal-smemory-video/GSS_libgoal_smemory-video_autogen: phony libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/timestamp libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/mocs_compilation.cpp libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps


#############################################
# Custom command for libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/timestamp

build libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/timestamp libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/timestamp ${cmake_ninja_workdir}libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/mocs_compilation.cpp: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/libexec/uic || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video && /usr/bin/cmake -E cmake_autogen /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video_autogen.dir/AutogenInfo.json Debug && /usr/bin/cmake -E touch /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/timestamp && /usr/bin/cmake -E cmake_transform_depfile Ninja gccdepfile /home/<USER>/PROJECTS/av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video /home/<USER>/PROJECTS/av-routing-and-mixing/build /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/deps /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/71857b489da2da9df8868cf20e3b550540ba230e0b488f7a353301bf337ad325.d
  DESC = Automatic MOC and UIC for target GSS_libgoal_smemory-video
  depfile = /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/71857b489da2da9df8868cf20e3b550540ba230e0b488f7a353301bf337ad325.d
  restat = 1


#############################################
# Phony custom command for libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video_autogen_timestamp_deps

build libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video_autogen_timestamp_deps | ${cmake_ninja_workdir}libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video_autogen_timestamp_deps: phony libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-utils/libGSS_libgoal_utils.a /opt/Qt/6.8.2/gcc_64/libexec/uic /opt/Qt/6.8.2/gcc_64/libexec/moc || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a


#############################################
# Phony custom command for libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video_autogen

build libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video_autogen | ${cmake_ninja_workdir}libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video_autogen: phony libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/timestamp || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target recv_test_app


#############################################
# Order-only phony target for recv_test_app

build cmake_object_order_depends_target_recv_test_app: phony || cmake_object_order_depends_target_GSS_libgoal_ndi cmake_object_order_depends_target_GSS_libgoal_utils cmake_object_order_depends_target_GSS_libgoal_videoframe libgoal-ndi/recv_test_app/recv_test_app_autogen libgoal-ndi/recv_test_app/recv_test_app_autogen/mocs_compilation.cpp libgoal-ndi/recv_test_app/recv_test_app_autogen/timestamp libgoal-ndi/recv_test_app/recv_test_app_autogen_timestamp_deps

build libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/recv_test_app_autogen/mocs_compilation.cpp.o: CXX_COMPILER__recv_test_app_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/recv_test_app_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_recv_test_app
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/recv_test_app_autogen/mocs_compilation.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/recv_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir
  OBJECT_FILE_DIR = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/recv_test_app_autogen
  TARGET_COMPILE_PDB = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/
  TARGET_PDB = libgoal-ndi/recv_test_app/recv_test_app.pdb

build libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/mainwindow.cpp.o: CXX_COMPILER__recv_test_app_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/mainwindow.cpp || cmake_object_order_depends_target_recv_test_app
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/mainwindow.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/recv_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir
  OBJECT_FILE_DIR = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir
  TARGET_COMPILE_PDB = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/
  TARGET_PDB = libgoal-ndi/recv_test_app/recv_test_app.pdb

build libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/main.cpp.o: CXX_COMPILER__recv_test_app_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/main.cpp || cmake_object_order_depends_target_recv_test_app
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/main.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/recv_test_app_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent
  OBJECT_DIR = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir
  OBJECT_FILE_DIR = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir
  TARGET_COMPILE_PDB = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/
  TARGET_PDB = libgoal-ndi/recv_test_app/recv_test_app.pdb


# =============================================================================
# Link build statements for EXECUTABLE target recv_test_app


#############################################
# Link the executable libgoal-ndi/recv_test_app/recv_test_app

build libgoal-ndi/recv_test_app/recv_test_app: CXX_EXECUTABLE_LINKER__recv_test_app_Debug libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/recv_test_app_autogen/mocs_compilation.cpp.o libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/mainwindow.cpp.o libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/main.cpp.o | libgoal-ndi/libGSS_libgoal_ndi.a libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-libav/libGSS_libgoal_libav.a libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-libav/libGSS_libgoal_libav.a /opt/Qt/6.8.2/gcc_64/lib/libQt6Multimedia.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Network.so.6.8.2 /usr/lib/x86_64-linux-gnu/libxkbcommon.so /opt/Qt/6.8.2/gcc_64/lib/libQt6Widgets.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Gui.so.6.8.2 /usr/lib/x86_64-linux-gnu/libGLX.so /usr/lib/x86_64-linux-gnu/libOpenGL.so /usr/lib/x86_64-linux-gnu/libavformat.so /usr/lib/x86_64-linux-gnu/libavfilter.so /usr/lib/x86_64-linux-gnu/libswscale.so /usr/lib/x86_64-linux-gnu/libswresample.so /usr/lib/x86_64-linux-gnu/libavcodec.so /usr/lib/x86_64-linux-gnu/libavutil.so libgoal-utils/libGSS_libgoal_utils.a /opt/Qt/6.8.2/gcc_64/lib/libQt6Concurrent.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Core.so.6.8.2 || libgoal-ndi/libGSS_libgoal_ndi.a libgoal-ndi/recv_test_app/recv_test_app_autogen libgoal-ndi/recv_test_app/recv_test_app_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/libGSS_libgoal_videoframe.a
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g
  LINK_LIBRARIES = -Wl,-rpath,/opt/Qt/6.8.2/gcc_64/lib  libgoal-ndi/libGSS_libgoal_ndi.a  -lndi  libgoal-videoframe/libGSS_libgoal_videoframe.a  libgoal-libav/libGSS_libgoal_libav.a  libgoal-videoframe/libGSS_libgoal_videoframe.a  libgoal-libav/libGSS_libgoal_libav.a  /opt/Qt/6.8.2/gcc_64/lib/libQt6Multimedia.so.6.8.2  /opt/Qt/6.8.2/gcc_64/lib/libQt6Network.so.6.8.2  /usr/lib/x86_64-linux-gnu/libxkbcommon.so  /opt/Qt/6.8.2/gcc_64/lib/libQt6Widgets.so.6.8.2  /opt/Qt/6.8.2/gcc_64/lib/libQt6Gui.so.6.8.2  /usr/lib/x86_64-linux-gnu/libGLX.so  /usr/lib/x86_64-linux-gnu/libOpenGL.so  /usr/lib/x86_64-linux-gnu/libavformat.so  /usr/lib/x86_64-linux-gnu/libavfilter.so  /usr/lib/x86_64-linux-gnu/libswscale.so  /usr/lib/x86_64-linux-gnu/libswresample.so  /usr/lib/x86_64-linux-gnu/libavcodec.so  /usr/lib/x86_64-linux-gnu/libavutil.so  libgoal-utils/libGSS_libgoal_utils.a  /opt/Qt/6.8.2/gcc_64/lib/libQt6Concurrent.so.6.8.2  /opt/Qt/6.8.2/gcc_64/lib/libQt6Core.so.6.8.2  -Wl,-rpath-link,/opt/Qt/6.8.2/gcc_64/lib
  OBJECT_DIR = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app.dir/
  TARGET_FILE = libgoal-ndi/recv_test_app/recv_test_app
  TARGET_PDB = libgoal-ndi/recv_test_app/recv_test_app.pdb


#############################################
# Utility command for edit_cache

build libgoal-ndi/recv_test_app/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app && /usr/bin/cmake-gui -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build libgoal-ndi/recv_test_app/edit_cache: phony libgoal-ndi/recv_test_app/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build libgoal-ndi/recv_test_app/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build libgoal-ndi/recv_test_app/rebuild_cache: phony libgoal-ndi/recv_test_app/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build libgoal-ndi/recv_test_app/list_install_components: phony


#############################################
# Utility command for install

build libgoal-ndi/recv_test_app/CMakeFiles/install.util: CUSTOM_COMMAND libgoal-ndi/recv_test_app/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build libgoal-ndi/recv_test_app/install: phony libgoal-ndi/recv_test_app/CMakeFiles/install.util


#############################################
# Utility command for install/local

build libgoal-ndi/recv_test_app/CMakeFiles/install/local.util: CUSTOM_COMMAND libgoal-ndi/recv_test_app/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build libgoal-ndi/recv_test_app/install/local: phony libgoal-ndi/recv_test_app/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build libgoal-ndi/recv_test_app/CMakeFiles/install/strip.util: CUSTOM_COMMAND libgoal-ndi/recv_test_app/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build libgoal-ndi/recv_test_app/install/strip: phony libgoal-ndi/recv_test_app/CMakeFiles/install/strip.util


#############################################
# Utility command for recv_test_app_autogen_timestamp_deps

build libgoal-ndi/recv_test_app/recv_test_app_autogen_timestamp_deps: phony libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a


#############################################
# Utility command for recv_test_app_autogen

build libgoal-ndi/recv_test_app/recv_test_app_autogen: phony libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app_autogen libgoal-ndi/recv_test_app/recv_test_app_autogen/include/ui_mainwindow.h libgoal-ndi/recv_test_app/recv_test_app_autogen/timestamp libgoal-ndi/recv_test_app/recv_test_app_autogen/mocs_compilation.cpp libgoal-ndi/recv_test_app/recv_test_app_autogen_timestamp_deps


#############################################
# Custom command for libgoal-ndi/recv_test_app/recv_test_app_autogen/timestamp

build libgoal-ndi/recv_test_app/recv_test_app_autogen/timestamp libgoal-ndi/recv_test_app/recv_test_app_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}libgoal-ndi/recv_test_app/recv_test_app_autogen/timestamp ${cmake_ninja_workdir}libgoal-ndi/recv_test_app/recv_test_app_autogen/mocs_compilation.cpp: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/libexec/uic || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-ndi/recv_test_app/recv_test_app_autogen_timestamp_deps libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app && /usr/bin/cmake -E cmake_autogen /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app_autogen.dir/AutogenInfo.json Debug && /usr/bin/cmake -E touch /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/recv_test_app_autogen/timestamp && /usr/bin/cmake -E cmake_transform_depfile Ninja gccdepfile /home/<USER>/PROJECTS/av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app /home/<USER>/PROJECTS/av-routing-and-mixing/build /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app/recv_test_app_autogen/deps /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/6cd5055b4d34b01b509aa5a2510ede34c3b8500ef957790ea70498e03f67d182.d
  DESC = Automatic MOC and UIC for target recv_test_app
  depfile = /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/6cd5055b4d34b01b509aa5a2510ede34c3b8500ef957790ea70498e03f67d182.d
  restat = 1


#############################################
# Phony custom command for libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app_autogen_timestamp_deps

build libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app_autogen_timestamp_deps | ${cmake_ninja_workdir}libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app_autogen_timestamp_deps: phony libgoal-ndi/libGSS_libgoal_ndi.a /opt/Qt/6.8.2/gcc_64/lib/libQt6Widgets.so.6.8.2 /opt/Qt/6.8.2/gcc_64/libexec/uic /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/lib/libQt6Multimedia.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Concurrent.so.6.8.2 || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a


#############################################
# Phony custom command for libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app_autogen

build libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app_autogen libgoal-ndi/recv_test_app/recv_test_app_autogen/include/ui_mainwindow.h | ${cmake_ninja_workdir}libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app_autogen ${cmake_ninja_workdir}libgoal-ndi/recv_test_app/recv_test_app_autogen/include/ui_mainwindow.h: phony libgoal-ndi/recv_test_app/recv_test_app_autogen/timestamp || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-ndi/recv_test_app/recv_test_app_autogen_timestamp_deps libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/PROJECTS/av-routing-and-mixing/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target GSS_libgoal_sdi


#############################################
# Order-only phony target for GSS_libgoal_sdi

build cmake_object_order_depends_target_GSS_libgoal_sdi: phony || cmake_object_order_depends_target_GSS_libgoal_utils cmake_object_order_depends_target_GSS_libgoal_videoframe libgoal-sdi/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen/mocs_compilation.cpp libgoal-sdi/GSS_libgoal_sdi_autogen/timestamp libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps

build libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/GSS_libgoal_sdi_autogen/mocs_compilation.cpp.o: CXX_COMPILER__GSS_libgoal_sdi_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_GSS_libgoal_sdi
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/GSS_libgoal_sdi_autogen/mocs_compilation.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir
  OBJECT_FILE_DIR = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/GSS_libgoal_sdi_autogen
  TARGET_COMPILE_PDB = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/GSS_libgoal_sdi.pdb
  TARGET_PDB = libgoal-sdi/libGSS_libgoal_sdi.pdb

build libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/DeckLinkAPI/DeckLinkAPIDispatch.cpp.o: CXX_COMPILER__GSS_libgoal_sdi_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDispatch.cpp || cmake_object_order_depends_target_GSS_libgoal_sdi
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/DeckLinkAPI/DeckLinkAPIDispatch.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir
  OBJECT_FILE_DIR = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/DeckLinkAPI
  TARGET_COMPILE_PDB = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/GSS_libgoal_sdi.pdb
  TARGET_PDB = libgoal-sdi/libGSS_libgoal_sdi.pdb

build libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/capture.cpp.o: CXX_COMPILER__GSS_libgoal_sdi_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/capture.cpp || cmake_object_order_depends_target_GSS_libgoal_sdi
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/capture.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir
  OBJECT_FILE_DIR = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include
  TARGET_COMPILE_PDB = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/GSS_libgoal_sdi.pdb
  TARGET_PDB = libgoal-sdi/libGSS_libgoal_sdi.pdb

build libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/decklinknotificationcallback.cpp.o: CXX_COMPILER__GSS_libgoal_sdi_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/decklinknotificationcallback.cpp || cmake_object_order_depends_target_GSS_libgoal_sdi
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/decklinknotificationcallback.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir
  OBJECT_FILE_DIR = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include
  TARGET_COMPILE_PDB = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/GSS_libgoal_sdi.pdb
  TARGET_PDB = libgoal-sdi/libGSS_libgoal_sdi.pdb

build libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/outputsdi.cpp.o: CXX_COMPILER__GSS_libgoal_sdi_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/outputsdi.cpp || cmake_object_order_depends_target_GSS_libgoal_sdi
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/outputsdi.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir
  OBJECT_FILE_DIR = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include
  TARGET_COMPILE_PDB = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/GSS_libgoal_sdi.pdb
  TARGET_PDB = libgoal-sdi/libGSS_libgoal_sdi.pdb

build libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/bmdsdihelper.cpp.o: CXX_COMPILER__GSS_libgoal_sdi_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/bmdsdihelper.cpp || cmake_object_order_depends_target_GSS_libgoal_sdi
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/bmdsdihelper.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork
  OBJECT_DIR = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir
  OBJECT_FILE_DIR = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include
  TARGET_COMPILE_PDB = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/GSS_libgoal_sdi.pdb
  TARGET_PDB = libgoal-sdi/libGSS_libgoal_sdi.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target GSS_libgoal_sdi


#############################################
# Link the static library libgoal-sdi/libGSS_libgoal_sdi.a

build libgoal-sdi/libGSS_libgoal_sdi.a: CXX_STATIC_LIBRARY_LINKER__GSS_libgoal_sdi_Debug libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/GSS_libgoal_sdi_autogen/mocs_compilation.cpp.o libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/DeckLinkAPI/DeckLinkAPIDispatch.cpp.o libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/capture.cpp.o libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/decklinknotificationcallback.cpp.o libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/outputsdi.cpp.o libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/include/bmdsdihelper.cpp.o || libgoal-sdi/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/libGSS_libgoal_videoframe.a
  LANGUAGE_COMPILE_FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g
  OBJECT_DIR = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = libgoal-sdi/CMakeFiles/GSS_libgoal_sdi.dir/GSS_libgoal_sdi.pdb
  TARGET_FILE = libgoal-sdi/libGSS_libgoal_sdi.a
  TARGET_PDB = libgoal-sdi/libGSS_libgoal_sdi.pdb


#############################################
# Utility command for edit_cache

build libgoal-sdi/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi && /usr/bin/cmake-gui -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build libgoal-sdi/edit_cache: phony libgoal-sdi/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build libgoal-sdi/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build libgoal-sdi/rebuild_cache: phony libgoal-sdi/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build libgoal-sdi/list_install_components: phony


#############################################
# Utility command for install

build libgoal-sdi/CMakeFiles/install.util: CUSTOM_COMMAND libgoal-sdi/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build libgoal-sdi/install: phony libgoal-sdi/CMakeFiles/install.util


#############################################
# Utility command for install/local

build libgoal-sdi/CMakeFiles/install/local.util: CUSTOM_COMMAND libgoal-sdi/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build libgoal-sdi/install/local: phony libgoal-sdi/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build libgoal-sdi/CMakeFiles/install/strip.util: CUSTOM_COMMAND libgoal-sdi/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build libgoal-sdi/install/strip: phony libgoal-sdi/CMakeFiles/install/strip.util


#############################################
# Utility command for GSS_libgoal_sdi_autogen_timestamp_deps

build libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps: phony libgoal-sdi/CMakeFiles/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a


#############################################
# Utility command for GSS_libgoal_sdi_autogen

build libgoal-sdi/GSS_libgoal_sdi_autogen: phony libgoal-sdi/CMakeFiles/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen/timestamp libgoal-sdi/GSS_libgoal_sdi_autogen/mocs_compilation.cpp libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps


#############################################
# Custom command for libgoal-sdi/GSS_libgoal_sdi_autogen/timestamp

build libgoal-sdi/GSS_libgoal_sdi_autogen/timestamp libgoal-sdi/GSS_libgoal_sdi_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}libgoal-sdi/GSS_libgoal_sdi_autogen/timestamp ${cmake_ninja_workdir}libgoal-sdi/GSS_libgoal_sdi_autogen/mocs_compilation.cpp: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/libexec/uic || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi && /usr/bin/cmake -E cmake_autogen /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/CMakeFiles/GSS_libgoal_sdi_autogen.dir/AutogenInfo.json Debug && /usr/bin/cmake -E touch /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/timestamp && /usr/bin/cmake -E cmake_transform_depfile Ninja gccdepfile /home/<USER>/PROJECTS/av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi /home/<USER>/PROJECTS/av-routing-and-mixing/build /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi/GSS_libgoal_sdi_autogen/deps /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/c269f8feba53262dd7f376c9cf37fdb100c9f2b31932d54d491e4066140eff1c.d
  DESC = Automatic MOC and UIC for target GSS_libgoal_sdi
  depfile = /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/c269f8feba53262dd7f376c9cf37fdb100c9f2b31932d54d491e4066140eff1c.d
  restat = 1


#############################################
# Phony custom command for libgoal-sdi/CMakeFiles/GSS_libgoal_sdi_autogen_timestamp_deps

build libgoal-sdi/CMakeFiles/GSS_libgoal_sdi_autogen_timestamp_deps | ${cmake_ninja_workdir}libgoal-sdi/CMakeFiles/GSS_libgoal_sdi_autogen_timestamp_deps: phony /opt/Qt/6.8.2/gcc_64/libexec/uic /opt/Qt/6.8.2/gcc_64/libexec/moc libgoal-libav/libGSS_libgoal_libav.a || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a


#############################################
# Phony custom command for libgoal-sdi/CMakeFiles/GSS_libgoal_sdi_autogen

build libgoal-sdi/CMakeFiles/GSS_libgoal_sdi_autogen | ${cmake_ninja_workdir}libgoal-sdi/CMakeFiles/GSS_libgoal_sdi_autogen: phony libgoal-sdi/GSS_libgoal_sdi_autogen/timestamp || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/PROJECTS/av-routing-and-mixing/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target GSS_libgoal_devices


#############################################
# Order-only phony target for GSS_libgoal_devices

build cmake_object_order_depends_target_GSS_libgoal_devices: phony || libgoal-devices/GSS_libgoal_devices_autogen libgoal-devices/GSS_libgoal_devices_autogen/mocs_compilation.cpp libgoal-devices/GSS_libgoal_devices_autogen/timestamp libgoal-devices/GSS_libgoal_devices_autogen_timestamp_deps

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices_autogen/mocs_compilation.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices_autogen/mocs_compilation.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices_autogen
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/ajakumocontroler.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/ajakumocontroler.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/ajakumocontroler.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/aspencontroller.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/aspencontroller.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/aspencontroller.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/bmvideohubcontroller.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubcontroller.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/bmvideohubcontroller.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/bmvideohubswitcher.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/bmvideohubswitcher.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/bmvideohubswitcher.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novacontrollerbase.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novacontrollerbase.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novacontrollerbase.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novamctrl660procontroller.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl660procontroller.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novamctrl660procontroller.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novaprouhdjrcontroller.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novaprouhdjrcontroller.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novaprouhdjrcontroller.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novamctrl4kcontroller.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novamctrl4kcontroller.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novamctrl4kcontroller.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novahseriescontroller.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novahseriescontroller.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novahseriescontroller.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novavxseries.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/novavxseries.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novavxseries.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/lightwaremxseriescontroller.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/VideoSwitcher/lightwaremxseriescontroller.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/lightwaremxseriescontroller.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/XKeys/xkeyspiecontroller.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/XKeys/xkeyspiecontroller.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/XKeys/xkeyspiecontroller.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/XKeys
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/UdpMsg/udpmessagesender.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagesender.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/UdpMsg/udpmessagesender.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/UdpMsg
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb

build libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/UdpMsg/udpmessagereader.cpp.o: CXX_COMPILER__GSS_libgoal_devices_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include/UdpMsg/udpmessagereader.cpp || cmake_object_order_depends_target_GSS_libgoal_devices
  DEFINES = -DQT_CORE_LIB -DQT_NETWORK_LIB
  DEP_FILE = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/UdpMsg/udpmessagereader.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  OBJECT_FILE_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/UdpMsg
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target GSS_libgoal_devices


#############################################
# Link the static library libgoal-devices/libGSS_libgoal_devices.a

build libgoal-devices/libGSS_libgoal_devices.a: CXX_STATIC_LIBRARY_LINKER__GSS_libgoal_devices_Debug libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices_autogen/mocs_compilation.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/ajakumocontroler.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/aspencontroller.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/bmvideohubcontroller.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/bmvideohubswitcher.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novacontrollerbase.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novamctrl660procontroller.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novaprouhdjrcontroller.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novamctrl4kcontroller.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novahseriescontroller.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/novavxseries.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/VideoSwitcher/lightwaremxseriescontroller.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/XKeys/xkeyspiecontroller.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/UdpMsg/udpmessagesender.cpp.o libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/include/UdpMsg/udpmessagereader.cpp.o || libgoal-devices/GSS_libgoal_devices_autogen libgoal-devices/GSS_libgoal_devices_autogen_timestamp_deps
  LANGUAGE_COMPILE_FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g
  OBJECT_DIR = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = libgoal-devices/CMakeFiles/GSS_libgoal_devices.dir/GSS_libgoal_devices.pdb
  TARGET_FILE = libgoal-devices/libGSS_libgoal_devices.a
  TARGET_PDB = libgoal-devices/libGSS_libgoal_devices.pdb


#############################################
# Utility command for edit_cache

build libgoal-devices/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices && /usr/bin/cmake-gui -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build libgoal-devices/edit_cache: phony libgoal-devices/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build libgoal-devices/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build libgoal-devices/rebuild_cache: phony libgoal-devices/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build libgoal-devices/list_install_components: phony


#############################################
# Utility command for install

build libgoal-devices/CMakeFiles/install.util: CUSTOM_COMMAND libgoal-devices/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build libgoal-devices/install: phony libgoal-devices/CMakeFiles/install.util


#############################################
# Utility command for install/local

build libgoal-devices/CMakeFiles/install/local.util: CUSTOM_COMMAND libgoal-devices/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build libgoal-devices/install/local: phony libgoal-devices/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build libgoal-devices/CMakeFiles/install/strip.util: CUSTOM_COMMAND libgoal-devices/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build libgoal-devices/install/strip: phony libgoal-devices/CMakeFiles/install/strip.util


#############################################
# Utility command for GSS_libgoal_devices_autogen_timestamp_deps

build libgoal-devices/GSS_libgoal_devices_autogen_timestamp_deps: phony libgoal-devices/CMakeFiles/GSS_libgoal_devices_autogen_timestamp_deps


#############################################
# Utility command for GSS_libgoal_devices_autogen

build libgoal-devices/GSS_libgoal_devices_autogen: phony libgoal-devices/CMakeFiles/GSS_libgoal_devices_autogen libgoal-devices/GSS_libgoal_devices_autogen/timestamp libgoal-devices/GSS_libgoal_devices_autogen/mocs_compilation.cpp libgoal-devices/GSS_libgoal_devices_autogen_timestamp_deps


#############################################
# Custom command for libgoal-devices/GSS_libgoal_devices_autogen/timestamp

build libgoal-devices/GSS_libgoal_devices_autogen/timestamp libgoal-devices/GSS_libgoal_devices_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}libgoal-devices/GSS_libgoal_devices_autogen/timestamp ${cmake_ninja_workdir}libgoal-devices/GSS_libgoal_devices_autogen/mocs_compilation.cpp: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/libexec/uic || libgoal-devices/GSS_libgoal_devices_autogen_timestamp_deps
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices && /usr/bin/cmake -E cmake_autogen /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/CMakeFiles/GSS_libgoal_devices_autogen.dir/AutogenInfo.json Debug && /usr/bin/cmake -E touch /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/timestamp && /usr/bin/cmake -E cmake_transform_depfile Ninja gccdepfile /home/<USER>/PROJECTS/av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices /home/<USER>/PROJECTS/av-routing-and-mixing/build /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices/GSS_libgoal_devices_autogen/deps /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/31ae266279ef8304b1a7fead282729318a835eef240f7de4f1a7f5655fbe6cce.d
  DESC = Automatic MOC and UIC for target GSS_libgoal_devices
  depfile = /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/31ae266279ef8304b1a7fead282729318a835eef240f7de4f1a7f5655fbe6cce.d
  restat = 1


#############################################
# Phony custom command for libgoal-devices/CMakeFiles/GSS_libgoal_devices_autogen_timestamp_deps

build libgoal-devices/CMakeFiles/GSS_libgoal_devices_autogen_timestamp_deps | ${cmake_ninja_workdir}libgoal-devices/CMakeFiles/GSS_libgoal_devices_autogen_timestamp_deps: phony /opt/Qt/6.8.2/gcc_64/libexec/uic /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/lib/libQt6Network.so.6.8.2


#############################################
# Phony custom command for libgoal-devices/CMakeFiles/GSS_libgoal_devices_autogen

build libgoal-devices/CMakeFiles/GSS_libgoal_devices_autogen | ${cmake_ninja_workdir}libgoal-devices/CMakeFiles/GSS_libgoal_devices_autogen: phony libgoal-devices/GSS_libgoal_devices_autogen/timestamp || libgoal-devices/GSS_libgoal_devices_autogen_timestamp_deps

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/PROJECTS/av-routing-and-mixing/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target GSS_libgoal_avrouter


#############################################
# Order-only phony target for GSS_libgoal_avrouter

build cmake_object_order_depends_target_GSS_libgoal_avrouter: phony || cmake_object_order_depends_target_GSS_libgoal_ndi cmake_object_order_depends_target_GSS_libgoal_sdi cmake_object_order_depends_target_GSS_libgoal_smemory-video cmake_object_order_depends_target_GSS_libgoal_utils cmake_object_order_depends_target_GSS_libgoal_videoframe libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp libgoal-avrouter/GSS_libgoal_avrouter_autogen libgoal-avrouter/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp libgoal-avrouter/GSS_libgoal_avrouter_autogen/timestamp libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps

build libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp.o: CXX_COMPILER__GSS_libgoal_avrouter_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_GSS_libgoal_avrouter
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets
  OBJECT_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir
  OBJECT_FILE_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter_autogen
  TARGET_COMPILE_PDB = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter.pdb
  TARGET_PDB = libgoal-avrouter/libGSS_libgoal_avrouter.pdb

build libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/avrouter.cpp.o: CXX_COMPILER__GSS_libgoal_avrouter_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avrouter.cpp || cmake_object_order_depends_target_GSS_libgoal_avrouter
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/avrouter.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets
  OBJECT_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir
  OBJECT_FILE_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter
  TARGET_COMPILE_PDB = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter.pdb
  TARGET_PDB = libgoal-avrouter/libGSS_libgoal_avrouter.pdb

build libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/liveinputmanager.cpp.o: CXX_COMPILER__GSS_libgoal_avrouter_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveinputmanager.cpp || cmake_object_order_depends_target_GSS_libgoal_avrouter
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/liveinputmanager.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets
  OBJECT_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir
  OBJECT_FILE_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter
  TARGET_COMPILE_PDB = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter.pdb
  TARGET_PDB = libgoal-avrouter/libGSS_libgoal_avrouter.pdb

build libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/liveoutputmanager.cpp.o: CXX_COMPILER__GSS_libgoal_avrouter_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveoutputmanager.cpp || cmake_object_order_depends_target_GSS_libgoal_avrouter
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/liveoutputmanager.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets
  OBJECT_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir
  OBJECT_FILE_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter
  TARGET_COMPILE_PDB = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter.pdb
  TARGET_PDB = libgoal-avrouter/libGSS_libgoal_avrouter.pdb

build libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/videoformatter.cpp.o: CXX_COMPILER__GSS_libgoal_avrouter_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videoformatter.cpp || cmake_object_order_depends_target_GSS_libgoal_avrouter
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/videoformatter.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets
  OBJECT_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir
  OBJECT_FILE_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter
  TARGET_COMPILE_PDB = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter.pdb
  TARGET_PDB = libgoal-avrouter/libGSS_libgoal_avrouter.pdb

build libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/fpssync.cpp.o: CXX_COMPILER__GSS_libgoal_avrouter_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/fpssync.cpp || cmake_object_order_depends_target_GSS_libgoal_avrouter
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/fpssync.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets
  OBJECT_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir
  OBJECT_FILE_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter
  TARGET_COMPILE_PDB = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter.pdb
  TARGET_PDB = libgoal-avrouter/libGSS_libgoal_avrouter.pdb

build libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/videooutputprocess.cpp.o: CXX_COMPILER__GSS_libgoal_avrouter_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videooutputprocess.cpp || cmake_object_order_depends_target_GSS_libgoal_avrouter
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/videooutputprocess.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets
  OBJECT_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir
  OBJECT_FILE_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter
  TARGET_COMPILE_PDB = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter.pdb
  TARGET_PDB = libgoal-avrouter/libGSS_libgoal_avrouter.pdb

build libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/openclprocessor.cpp.o: CXX_COMPILER__GSS_libgoal_avrouter_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/openclprocessor.cpp || cmake_object_order_depends_target_GSS_libgoal_avrouter
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/openclprocessor.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets
  OBJECT_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir
  OBJECT_FILE_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter
  TARGET_COMPILE_PDB = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter.pdb
  TARGET_PDB = libgoal-avrouter/libGSS_libgoal_avrouter.pdb

build libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp.o: CXX_COMPILER__GSS_libgoal_avrouter_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp || cmake_object_order_depends_target_GSS_libgoal_avrouter
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtNetwork -isystem /opt/Qt/6.8.2/gcc_64/include/QtConcurrent -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets
  OBJECT_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir
  OBJECT_FILE_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/.qt/rcc
  TARGET_COMPILE_PDB = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter.pdb
  TARGET_PDB = libgoal-avrouter/libGSS_libgoal_avrouter.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target GSS_libgoal_avrouter


#############################################
# Link the static library libgoal-avrouter/libGSS_libgoal_avrouter.a

build libgoal-avrouter/libGSS_libgoal_avrouter.a: CXX_STATIC_LIBRARY_LINKER__GSS_libgoal_avrouter_Debug libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp.o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/avrouter.cpp.o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/liveinputmanager.cpp.o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/liveoutputmanager.cpp.o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/videoformatter.cpp.o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/fpssync.cpp.o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/videooutputprocess.cpp.o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/include/avrouter/openclprocessor.cpp.o libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp.o || libgoal-avrouter/GSS_libgoal_avrouter_autogen libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/libGSS_libgoal_videoframe.a
  LANGUAGE_COMPILE_FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g
  OBJECT_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter.dir/GSS_libgoal_avrouter.pdb
  TARGET_FILE = libgoal-avrouter/libGSS_libgoal_avrouter.a
  TARGET_PDB = libgoal-avrouter/libGSS_libgoal_avrouter.pdb


#############################################
# Utility command for GSS_libgoal_avrouter_other_files

build libgoal-avrouter/GSS_libgoal_avrouter_other_files: phony

# =============================================================================
# Object build statements for OBJECT_LIBRARY target GSS_libgoal_avrouter_resources_1


#############################################
# Order-only phony target for GSS_libgoal_avrouter_resources_1

build cmake_object_order_depends_target_GSS_libgoal_avrouter_resources_1: phony || libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir

build libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir/.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp.o: CXX_COMPILER__GSS_libgoal_avrouter_resources_1_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp || cmake_object_order_depends_target_GSS_libgoal_avrouter_resources_1
  DEFINES = -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir/.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter -I/opt/Qt/6.8.2/gcc_64/include/QtCore -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include -I/opt/Qt/6.8.2/gcc_64/include/QtMultimedia -I/opt/Qt/6.8.2/gcc_64/include/QtGui -I/opt/Qt/6.8.2/gcc_64/include/QtNetwork -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include -I/opt/Qt/6.8.2/gcc_64/include/QtConcurrent -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include -I/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtCore/6.8.2/QtCore -I/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/6.8.2/QtMultimedia -I/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2 -I/opt/Qt/6.8.2/gcc_64/include/QtGui/6.8.2/QtGui -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include -I/opt/Qt/6.8.2/gcc_64/include/QtWidgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++
  OBJECT_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir
  OBJECT_FILE_DIR = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir/.qt/rcc
  TARGET_COMPILE_PDB = libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir/
  TARGET_PDB = ""



#############################################
# Object library GSS_libgoal_avrouter_resources_1

build libgoal-avrouter/GSS_libgoal_avrouter_resources_1: phony libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir/.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp.o


#############################################
# Utility command for edit_cache

build libgoal-avrouter/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter && /usr/bin/cmake-gui -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build libgoal-avrouter/edit_cache: phony libgoal-avrouter/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build libgoal-avrouter/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build libgoal-avrouter/rebuild_cache: phony libgoal-avrouter/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build libgoal-avrouter/list_install_components: phony


#############################################
# Utility command for install

build libgoal-avrouter/CMakeFiles/install.util: CUSTOM_COMMAND libgoal-avrouter/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build libgoal-avrouter/install: phony libgoal-avrouter/CMakeFiles/install.util


#############################################
# Utility command for install/local

build libgoal-avrouter/CMakeFiles/install/local.util: CUSTOM_COMMAND libgoal-avrouter/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build libgoal-avrouter/install/local: phony libgoal-avrouter/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build libgoal-avrouter/CMakeFiles/install/strip.util: CUSTOM_COMMAND libgoal-avrouter/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build libgoal-avrouter/install/strip: phony libgoal-avrouter/CMakeFiles/install/strip.util


#############################################
# Utility command for GSS_libgoal_avrouter_autogen_timestamp_deps

build libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps: phony libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/libGSS_libgoal_videoframe.a


#############################################
# Utility command for GSS_libgoal_avrouter_autogen

build libgoal-avrouter/GSS_libgoal_avrouter_autogen: phony libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen libgoal-avrouter/GSS_libgoal_avrouter_autogen/timestamp libgoal-avrouter/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps


#############################################
# Custom command for libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp

build libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp | ${cmake_ninja_workdir}libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp: CUSTOM_COMMAND /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/resources/kernels.cl libgoal-avrouter/.qt/rcc/GSS_libgoal_avrouter.qrc /opt/Qt/6.8.2/gcc_64/libexec/rcc || libgoal-avrouter/GSS_libgoal_avrouter_autogen libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter && /opt/Qt/6.8.2/gcc_64/libexec/rcc --output /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp --name GSS_libgoal_avrouter /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/.qt/rcc/GSS_libgoal_avrouter.qrc
  DESC = Running rcc for resource GSS_libgoal_avrouter
  restat = 1


#############################################
# Custom command for libgoal-avrouter/GSS_libgoal_avrouter_autogen/timestamp

build libgoal-avrouter/GSS_libgoal_avrouter_autogen/timestamp libgoal-avrouter/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}libgoal-avrouter/GSS_libgoal_avrouter_autogen/timestamp ${cmake_ninja_workdir}libgoal-avrouter/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/libexec/uic || libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter && /usr/bin/cmake -E cmake_autogen /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen.dir/AutogenInfo.json Debug && /usr/bin/cmake -E touch /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/timestamp && /usr/bin/cmake -E cmake_transform_depfile Ninja gccdepfile /home/<USER>/PROJECTS/av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter /home/<USER>/PROJECTS/av-routing-and-mixing/build /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter/GSS_libgoal_avrouter_autogen/deps /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/e6f022aa7407771098adb4594a81d6a5c519e9fa23814307f0fdaec6a44dfd17.d
  DESC = Automatic MOC and UIC for target GSS_libgoal_avrouter
  depfile = /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/e6f022aa7407771098adb4594a81d6a5c519e9fa23814307f0fdaec6a44dfd17.d
  restat = 1


#############################################
# Phony custom command for libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen_timestamp_deps

build libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen_timestamp_deps | ${cmake_ninja_workdir}libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen_timestamp_deps: phony libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-sdi/libGSS_libgoal_sdi.a libgoal-videoframe/libGSS_libgoal_videoframe.a libgoal-ndi/libGSS_libgoal_ndi.a libgoal-utils/libGSS_libgoal_utils.a /opt/Qt/6.8.2/gcc_64/libexec/uic /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/lib/libQt6Core.so.6.8.2 /opt/Qt/6.8.2/gcc_64/lib/libQt6Multimedia.so.6.8.2 /usr/lib/x86_64-linux-gnu/libOpenCL.so || libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a


#############################################
# Phony custom command for libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen

build libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen | ${cmake_ninja_workdir}libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen: phony libgoal-avrouter/GSS_libgoal_avrouter_autogen/timestamp || libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps libgoal-libav/GSS_libgoal_libav_autogen libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps libgoal-libav/libGSS_libgoal_libav.a libgoal-ndi/GSS_libgoal_ndi_autogen libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps libgoal-ndi/libGSS_libgoal_ndi.a libgoal-sdi/GSS_libgoal_sdi_autogen libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps libgoal-sdi/libGSS_libgoal_sdi.a libgoal-smemory-video/GSS_libgoal_smemory-video_autogen libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps libgoal-smemory-video/libGSS_libgoal_smemory-video.a libgoal-utils/GSS_libgoal_utils_autogen libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps libgoal-utils/libGSS_libgoal_utils.a libgoal-videoframe/GSS_libgoal_videoframe_autogen libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps libgoal-videoframe/libGSS_libgoal_videoframe.a

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/PROJECTS/av-routing-and-mixing/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target GSS_libgoal_widgets


#############################################
# Order-only phony target for GSS_libgoal_widgets

build cmake_object_order_depends_target_GSS_libgoal_widgets: phony || libgoal-widgets/GSS_libgoal_widgets_autogen libgoal-widgets/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp libgoal-widgets/GSS_libgoal_widgets_autogen/mocs_compilation.cpp libgoal-widgets/GSS_libgoal_widgets_autogen/timestamp libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps

build libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets_autogen/mocs_compilation.cpp.o: CXX_COMPILER__GSS_libgoal_widgets_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_GSS_libgoal_widgets
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets_autogen/mocs_compilation.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui
  OBJECT_DIR = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir
  OBJECT_FILE_DIR = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets_autogen
  TARGET_COMPILE_PDB = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets.pdb
  TARGET_PDB = libgoal-widgets/libGSS_libgoal_widgets.pdb

build libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/manager.cpp.o: CXX_COMPILER__GSS_libgoal_widgets_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/manager.cpp || cmake_object_order_depends_target_GSS_libgoal_widgets
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/manager.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui
  OBJECT_DIR = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir
  OBJECT_FILE_DIR = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline
  TARGET_COMPILE_PDB = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets.pdb
  TARGET_PDB = libgoal-widgets/libGSS_libgoal_widgets.pdb

build libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/model.cpp.o: CXX_COMPILER__GSS_libgoal_widgets_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/model.cpp || cmake_object_order_depends_target_GSS_libgoal_widgets
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/model.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui
  OBJECT_DIR = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir
  OBJECT_FILE_DIR = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline
  TARGET_COMPILE_PDB = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets.pdb
  TARGET_PDB = libgoal-widgets/libGSS_libgoal_widgets.pdb

build libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/slider.cpp.o: CXX_COMPILER__GSS_libgoal_widgets_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/timeline/slider.cpp || cmake_object_order_depends_target_GSS_libgoal_widgets
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/slider.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui
  OBJECT_DIR = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir
  OBJECT_FILE_DIR = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline
  TARGET_COMPILE_PDB = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets.pdb
  TARGET_PDB = libgoal-widgets/libGSS_libgoal_widgets.pdb

build libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/splash/goalsplashscreen.cpp.o: CXX_COMPILER__GSS_libgoal_widgets_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/goalsplashscreen.cpp || cmake_object_order_depends_target_GSS_libgoal_widgets
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/splash/goalsplashscreen.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui
  OBJECT_DIR = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir
  OBJECT_FILE_DIR = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/splash
  TARGET_COMPILE_PDB = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets.pdb
  TARGET_PDB = libgoal-widgets/libGSS_libgoal_widgets.pdb

build libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp.o: CXX_COMPILER__GSS_libgoal_widgets_unscanned_Debug /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp || cmake_object_order_depends_target_GSS_libgoal_widgets
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_WIDGETS_LIB
  DEP_FILE = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp.o.d
  FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g -std=gnu++17 -fPIC
  INCLUDES = -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets -I/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/include -I/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtWidgets -isystem /opt/Qt/6.8.2/gcc_64/include -isystem /opt/Qt/6.8.2/gcc_64/include/QtCore -isystem /opt/Qt/6.8.2/gcc_64/mkspecs/linux-g++ -isystem /opt/Qt/6.8.2/gcc_64/include/QtGui
  OBJECT_DIR = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir
  OBJECT_FILE_DIR = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets_autogen/GGRFHCMMEE
  TARGET_COMPILE_PDB = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets.pdb
  TARGET_PDB = libgoal-widgets/libGSS_libgoal_widgets.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target GSS_libgoal_widgets


#############################################
# Link the static library libgoal-widgets/libGSS_libgoal_widgets.a

build libgoal-widgets/libGSS_libgoal_widgets.a: CXX_STATIC_LIBRARY_LINKER__GSS_libgoal_widgets_Debug libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets_autogen/mocs_compilation.cpp.o libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/manager.cpp.o libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/model.cpp.o libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/timeline/slider.cpp.o libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/include/splash/goalsplashscreen.cpp.o libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp.o || libgoal-widgets/GSS_libgoal_widgets_autogen libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps
  LANGUAGE_COMPILE_FLAGS = -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG -g
  OBJECT_DIR = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = libgoal-widgets/CMakeFiles/GSS_libgoal_widgets.dir/GSS_libgoal_widgets.pdb
  TARGET_FILE = libgoal-widgets/libGSS_libgoal_widgets.a
  TARGET_PDB = libgoal-widgets/libGSS_libgoal_widgets.pdb


#############################################
# Utility command for edit_cache

build libgoal-widgets/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets && /usr/bin/cmake-gui -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build libgoal-widgets/edit_cache: phony libgoal-widgets/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build libgoal-widgets/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/PROJECTS/av-routing-and-mixing -B/home/<USER>/PROJECTS/av-routing-and-mixing/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build libgoal-widgets/rebuild_cache: phony libgoal-widgets/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build libgoal-widgets/list_install_components: phony


#############################################
# Utility command for install

build libgoal-widgets/CMakeFiles/install.util: CUSTOM_COMMAND libgoal-widgets/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets && /usr/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build libgoal-widgets/install: phony libgoal-widgets/CMakeFiles/install.util


#############################################
# Utility command for install/local

build libgoal-widgets/CMakeFiles/install/local.util: CUSTOM_COMMAND libgoal-widgets/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets && /usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build libgoal-widgets/install/local: phony libgoal-widgets/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build libgoal-widgets/CMakeFiles/install/strip.util: CUSTOM_COMMAND libgoal-widgets/all
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets && /usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build libgoal-widgets/install/strip: phony libgoal-widgets/CMakeFiles/install/strip.util


#############################################
# Utility command for GSS_libgoal_widgets_autogen_timestamp_deps

build libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps: phony libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen_timestamp_deps


#############################################
# Utility command for GSS_libgoal_widgets_autogen

build libgoal-widgets/GSS_libgoal_widgets_autogen: phony libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen libgoal-widgets/GSS_libgoal_widgets_autogen/timestamp libgoal-widgets/GSS_libgoal_widgets_autogen/mocs_compilation.cpp libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps


#############################################
# Custom command for libgoal-widgets/GSS_libgoal_widgets_autogen/timestamp

build libgoal-widgets/GSS_libgoal_widgets_autogen/timestamp libgoal-widgets/GSS_libgoal_widgets_autogen/mocs_compilation.cpp | ${cmake_ninja_workdir}libgoal-widgets/GSS_libgoal_widgets_autogen/timestamp ${cmake_ninja_workdir}libgoal-widgets/GSS_libgoal_widgets_autogen/mocs_compilation.cpp: CUSTOM_COMMAND /opt/Qt/6.8.2/gcc_64/libexec/moc /opt/Qt/6.8.2/gcc_64/libexec/uic || libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets && /usr/bin/cmake -E cmake_autogen /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen.dir/AutogenInfo.json Debug && /usr/bin/cmake -E touch /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/timestamp && /usr/bin/cmake -E cmake_transform_depfile Ninja gccdepfile /home/<USER>/PROJECTS/av-routing-and-mixing /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets /home/<USER>/PROJECTS/av-routing-and-mixing/build /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/GSS_libgoal_widgets_autogen/deps /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/fdc8e6e501db598e7d0511840e5830b7d40143481c4bb11e9218ef8f2466886a.d
  DESC = Automatic MOC and UIC for target GSS_libgoal_widgets
  depfile = /home/<USER>/PROJECTS/av-routing-and-mixing/build/CMakeFiles/d/fdc8e6e501db598e7d0511840e5830b7d40143481c4bb11e9218ef8f2466886a.d
  restat = 1


#############################################
# Custom command for libgoal-widgets/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp

build libgoal-widgets/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp | ${cmake_ninja_workdir}libgoal-widgets/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp: CUSTOM_COMMAND /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/splash-resources.qrc libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen.dir/AutoRcc_splash-resources_GGRFHCMMEE_Info.json /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/resources/splash-rvw.png /opt/Qt/6.8.2/gcc_64/libexec/rcc || libgoal-widgets/GSS_libgoal_widgets_autogen libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps
  COMMAND = cd /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets && /usr/bin/cmake -E cmake_autorcc /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen.dir/AutoRcc_splash-resources_GGRFHCMMEE_Info.json Debug
  DESC = Automatic RCC for include/splash/splash-resources.qrc
  restat = 1


#############################################
# Phony custom command for libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen_timestamp_deps

build libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen_timestamp_deps | ${cmake_ninja_workdir}libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen_timestamp_deps: phony /opt/Qt/6.8.2/gcc_64/lib/libQt6Widgets.so.6.8.2 /opt/Qt/6.8.2/gcc_64/libexec/uic /opt/Qt/6.8.2/gcc_64/libexec/moc


#############################################
# Phony custom command for libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen

build libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen | ${cmake_ninja_workdir}libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen: phony libgoal-widgets/GSS_libgoal_widgets_autogen/timestamp || libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps

# =============================================================================
# Target aliases.

build GSS_libgoal_avrouter: phony libgoal-avrouter/libGSS_libgoal_avrouter.a

build GSS_libgoal_avrouter_autogen: phony libgoal-avrouter/GSS_libgoal_avrouter_autogen

build GSS_libgoal_avrouter_autogen_timestamp_deps: phony libgoal-avrouter/GSS_libgoal_avrouter_autogen_timestamp_deps

build GSS_libgoal_avrouter_other_files: phony libgoal-avrouter/GSS_libgoal_avrouter_other_files

build GSS_libgoal_avrouter_resources_1: phony libgoal-avrouter/GSS_libgoal_avrouter_resources_1

build GSS_libgoal_devices: phony libgoal-devices/libGSS_libgoal_devices.a

build GSS_libgoal_devices_autogen: phony libgoal-devices/GSS_libgoal_devices_autogen

build GSS_libgoal_devices_autogen_timestamp_deps: phony libgoal-devices/GSS_libgoal_devices_autogen_timestamp_deps

build GSS_libgoal_libav: phony libgoal-libav/libGSS_libgoal_libav.a

build GSS_libgoal_libav_autogen: phony libgoal-libav/GSS_libgoal_libav_autogen

build GSS_libgoal_libav_autogen_timestamp_deps: phony libgoal-libav/GSS_libgoal_libav_autogen_timestamp_deps

build GSS_libgoal_ndi: phony libgoal-ndi/libGSS_libgoal_ndi.a

build GSS_libgoal_ndi_autogen: phony libgoal-ndi/GSS_libgoal_ndi_autogen

build GSS_libgoal_ndi_autogen_timestamp_deps: phony libgoal-ndi/GSS_libgoal_ndi_autogen_timestamp_deps

build GSS_libgoal_sdi: phony libgoal-sdi/libGSS_libgoal_sdi.a

build GSS_libgoal_sdi_autogen: phony libgoal-sdi/GSS_libgoal_sdi_autogen

build GSS_libgoal_sdi_autogen_timestamp_deps: phony libgoal-sdi/GSS_libgoal_sdi_autogen_timestamp_deps

build GSS_libgoal_smemory-video: phony libgoal-smemory-video/libGSS_libgoal_smemory-video.a

build GSS_libgoal_smemory-video_autogen: phony libgoal-smemory-video/GSS_libgoal_smemory-video_autogen

build GSS_libgoal_smemory-video_autogen_timestamp_deps: phony libgoal-smemory-video/GSS_libgoal_smemory-video_autogen_timestamp_deps

build GSS_libgoal_utils: phony libgoal-utils/libGSS_libgoal_utils.a

build GSS_libgoal_utils_autogen: phony libgoal-utils/GSS_libgoal_utils_autogen

build GSS_libgoal_utils_autogen_timestamp_deps: phony libgoal-utils/GSS_libgoal_utils_autogen_timestamp_deps

build GSS_libgoal_videoframe: phony libgoal-videoframe/libGSS_libgoal_videoframe.a

build GSS_libgoal_videoframe_autogen: phony libgoal-videoframe/GSS_libgoal_videoframe_autogen

build GSS_libgoal_videoframe_autogen_timestamp_deps: phony libgoal-videoframe/GSS_libgoal_videoframe_autogen_timestamp_deps

build GSS_libgoal_widgets: phony libgoal-widgets/libGSS_libgoal_widgets.a

build GSS_libgoal_widgets_autogen: phony libgoal-widgets/GSS_libgoal_widgets_autogen

build GSS_libgoal_widgets_autogen_timestamp_deps: phony libgoal-widgets/GSS_libgoal_widgets_autogen_timestamp_deps

build NDIStream_send_test_app: phony libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app

build NDIStream_send_test_app_autogen: phony libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen

build NDIStream_send_test_app_autogen_timestamp_deps: phony libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen_timestamp_deps

build libGSS_libgoal_avrouter.a: phony libgoal-avrouter/libGSS_libgoal_avrouter.a

build libGSS_libgoal_devices.a: phony libgoal-devices/libGSS_libgoal_devices.a

build libGSS_libgoal_libav.a: phony libgoal-libav/libGSS_libgoal_libav.a

build libGSS_libgoal_ndi.a: phony libgoal-ndi/libGSS_libgoal_ndi.a

build libGSS_libgoal_sdi.a: phony libgoal-sdi/libGSS_libgoal_sdi.a

build libGSS_libgoal_smemory-video.a: phony libgoal-smemory-video/libGSS_libgoal_smemory-video.a

build libGSS_libgoal_utils.a: phony libgoal-utils/libGSS_libgoal_utils.a

build libGSS_libgoal_videoframe.a: phony libgoal-videoframe/libGSS_libgoal_videoframe.a

build libGSS_libgoal_widgets.a: phony libgoal-widgets/libGSS_libgoal_widgets.a

build recv_test_app: phony libgoal-ndi/recv_test_app/recv_test_app

build recv_test_app_autogen: phony libgoal-ndi/recv_test_app/recv_test_app_autogen

build recv_test_app_autogen_timestamp_deps: phony libgoal-ndi/recv_test_app/recv_test_app_autogen_timestamp_deps

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/PROJECTS/av-routing-and-mixing/build

build all: phony av-routing-and-mixing libgoal/all libgoal-ndi/all libgoal-sdi/all libgoal-devices/all libgoal-avrouter/all libgoal-widgets/all

# =============================================================================

#############################################
# Folder: /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal

build libgoal/all: phony

# =============================================================================

#############################################
# Folder: /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-avrouter

build libgoal-avrouter/all: phony libgoal-avrouter/GSS_libgoal_avrouter_resources_1

# =============================================================================

#############################################
# Folder: /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-devices

build libgoal-devices/all: phony

# =============================================================================

#############################################
# Folder: /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav

build libgoal-libav/all: phony

# =============================================================================

#############################################
# Folder: /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi

build libgoal-ndi/all: phony libgoal-videoframe/all libgoal-utils/all libgoal-ndi/NDIStream_send_test_app/all libgoal-ndi/recv_test_app/all

# =============================================================================

#############################################
# Folder: /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/NDIStream_send_test_app

build libgoal-ndi/NDIStream_send_test_app/all: phony libgoal-libav/all libgoal-smemory-video/all

# =============================================================================

#############################################
# Folder: /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-ndi/recv_test_app

build libgoal-ndi/recv_test_app/all: phony

# =============================================================================

#############################################
# Folder: /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-sdi

build libgoal-sdi/all: phony

# =============================================================================

#############################################
# Folder: /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-smemory-video

build libgoal-smemory-video/all: phony

# =============================================================================

#############################################
# Folder: /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-utils

build libgoal-utils/all: phony

# =============================================================================

#############################################
# Folder: /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-videoframe

build libgoal-videoframe/all: phony

# =============================================================================

#############################################
# Folder: /home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-widgets

build libgoal-widgets/all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | .qt/qml_imports/av-routing-and-mixing_conf.cmake /home/<USER>/PROJECTS/av-routing-and-mixing/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/splash-resources.qrc /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/3rdparty/kwin/FindXKB.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/FindWrapAtomic.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/FindWrapOpenGL.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/FindWrapVulkanHeaders.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Config.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Targets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeature.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeatureCommon.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtInstallPaths.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/qt.toolchain.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreConfigExtras.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreConfigureFileTemplate.in /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreResourceInit.in.cpp /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusMacros.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsVersionlessTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiPlugins.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QComposePlatformInputContextPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QComposePlatformInputContextPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QComposePlatformInputContextPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QComposePlatformInputContextPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSEmulatorIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSEmulatorIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSEmulatorIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSEmulatorIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsEglDeviceIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsEglDeviceIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsEglDeviceIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsEglDeviceIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsGbmIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsGbmIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsGbmIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsGbmIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSX11IntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSX11IntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSX11IntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSX11IntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevKeyboardPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevKeyboardPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevKeyboardPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevKeyboardPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevMousePluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevMousePluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevMousePluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevMousePluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTabletPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTabletPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTabletPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTabletPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTouchScreenPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTouchScreenPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTouchScreenPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTouchScreenPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGifPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGifPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGtk3ThemePluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGtk3ThemePluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGtk3ThemePluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGtk3ThemePluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICNSPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICNSPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICNSPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICNSPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICOPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICOPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QIbusPlatformInputContextPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QIbusPlatformInputContextPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QIbusPlatformInputContextPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QIbusPlatformInputContextPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QJpegPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QJpegPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QLinuxFbIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QLinuxFbIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QLinuxFbIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QLinuxFbIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalEglIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalEglIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalEglIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalEglIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTgaPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTgaPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTgaPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTgaPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTiffPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTiffPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTiffPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTiffPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVirtualKeyboardPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVirtualKeyboardPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVirtualKeyboardPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVirtualKeyboardPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVkKhrDisplayIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVkKhrDisplayIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVkKhrDisplayIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVkKhrDisplayIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVncIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVncIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVncIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVncIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandEglPlatformIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandEglPlatformIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandEglPlatformIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandEglPlatformIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWbmpPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWbmpPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWbmpPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWbmpPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWebpPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWebpPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWebpPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWebpPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbEglIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbEglIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbEglIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbEglIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbGlxIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbGlxIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbGlxIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbGlxIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXdgDesktopPortalThemePluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXdgDesktopPortalThemePluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXdgDesktopPortalThemePluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXdgDesktopPortalThemePluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaMacros.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaPlugins.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6QFFmpegMediaPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6QFFmpegMediaPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6QFFmpegMediaPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6QFFmpegMediaPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkPlugins.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QGlibNetworkInformationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QGlibNetworkInformationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QGlibNetworkInformationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QGlibNetworkInformationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QNetworkManagerNetworkInformationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QNetworkManagerNetworkInformationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QNetworkManagerNetworkInformationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QNetworkManagerNetworkInformationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6DataVisualizationQmlpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6DataVisualizationQmlpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6DataVisualizationQmlpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6DataVisualizationQmlpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6GraphspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6GraphspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6GraphspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6GraphspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6LabsPlatformpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6LabsPlatformpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6LabsPlatformpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6LabsPlatformpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlAssetDownloaderpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlAssetDownloaderpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlAssetDownloaderpluginDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlAssetDownloaderpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlAssetDownloaderpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlNetworkpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlNetworkpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlNetworkpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlNetworkpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6Quick3DXrpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6Quick3DXrpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6Quick3DXrpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6Quick3DXrpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickControlsTestUtilsPrivatepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickControlsTestUtilsPrivatepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickControlsTestUtilsPrivatepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickControlsTestUtilsPrivatepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickTestpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickTestpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickTestpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickTestpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6SensorsQuickpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6SensorsQuickpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6SensorsQuickpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6SensorsQuickpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6TextToSpeechQmlAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6TextToSpeechQmlConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6TextToSpeechQmlTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6TextToSpeechQmlTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorIviapplicationpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorIviapplicationpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorIviapplicationpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorIviapplicationpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorPresentationTimepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorPresentationTimepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorPresentationTimepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorPresentationTimepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorQtShellAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorQtShellConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorQtShellTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorQtShellTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorWLShellpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorWLShellpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorWLShellpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorWLShellpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorXdgShellpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorXdgShellpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorXdgShellpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorXdgShellpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingExtensionAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingExtensionConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingExtensionTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingExtensionTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WebChannelQuickpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WebChannelQuickpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WebChannelQuickpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WebChannelQuickpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_locationAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_locationConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_locationTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_locationTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_remoteobjectsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_remoteobjectsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_remoteobjectsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_remoteobjectsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_scxmlAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_scxmlConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_scxmlTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_scxmlTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6effectspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6effectspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6effectspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6effectspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6grpcquickpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6grpcquickpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6grpcquickpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6grpcquickpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsanimationpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsanimationpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsanimationpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsanimationpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsmodelspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsmodelspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsmodelspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsmodelspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6lottieqtpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6lottieqtpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6lottieqtpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6lottieqtpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6modelspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6modelspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6modelspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6modelspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6particlespluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6particlespluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6particlespluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6particlespluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6positioningquickpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6positioningquickpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6positioningquickpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6positioningquickpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6protobufquickpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6protobufquickpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6protobufquickpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6protobufquickpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlfolderlistmodelpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlfolderlistmodelpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlfolderlistmodelpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlfolderlistmodelpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmllocalstoragepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmllocalstoragepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmllocalstoragepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmllocalstoragepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlsettingspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlsettingspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlsettingspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlsettingspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlshapespluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlshapespluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlshapespluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlshapespluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwavefrontmeshpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwavefrontmeshpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwavefrontmeshpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwavefrontmeshpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwebsocketsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwebsocketsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwebsocketsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwebsocketsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlxmllistmodelpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlxmllistmodelpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlxmllistmodelpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlxmllistmodelpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dphysicspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dphysicspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dphysicspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dphysicspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquicklayoutspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquicklayoutspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquicklayoutspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquicklayoutspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquickvectorimagepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquickvectorimagepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquickvectorimagepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquickvectorimagepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtchartsqml2AdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtchartsqml2Config.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtchartsqml2Targets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtchartsqml2Targets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectsprivateAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectsprivateConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectsprivateTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectsprivateTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlcorepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlcorepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlcorepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlcorepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlstatemachineAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlstatemachineConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlstatemachineTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlstatemachineTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick2pluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick2pluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick2pluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick2pluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dassetutilspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dassetutilspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dassetutilspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dassetutilspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3deffectpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3deffectpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3deffectpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3deffectpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelpersimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelpersimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelpersimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelpersimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelperspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelperspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelperspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelperspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticleeffectspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticleeffectspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticleeffectspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticleeffectspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticles3dpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticles3dpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticles3dpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticles3dpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dphysicshelperspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dphysicshelperspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dphysicshelperspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dphysicshelperspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstyleimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstyleimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstyleimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstyleimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3styleimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3styleimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3styleimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3styleimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3stylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3stylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3stylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3stylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstyleimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstyleimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstyleimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstyleimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestyleimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestyleimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestyleimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestyleimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2implpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2implpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2implpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2implpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstyleimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstyleimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstyleimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstyleimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2nativestylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2nativestylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2nativestylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2nativestylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2pluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2pluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2pluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2pluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstyleimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstyleimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstyleimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstyleimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogs2quickimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogs2quickimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogs2quickimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogs2quickimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene2dpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene2dpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene2dpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene2dpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene3dpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene3dpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene3dpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene3dpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktemplates2pluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktemplates2pluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktemplates2pluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktemplates2pluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelineblendtreespluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelineblendtreespluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelineblendtreespluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelineblendtreespluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelinepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelinepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelinepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelinepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbbuiltinstylespluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbbuiltinstylespluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbbuiltinstylespluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbbuiltinstylespluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbcomponentspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbcomponentspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbcomponentspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbcomponentspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbhangulpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbhangulpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbhangulpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbhangulpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkblayoutspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkblayoutspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkblayoutspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkblayoutspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbopenwnnpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbopenwnnpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbopenwnnpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbopenwnnpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpinyinpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpinyinpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpinyinpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpinyinpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbsettingspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbsettingspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbsettingspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbsettingspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbstylespluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbstylespluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbstylespluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbstylespluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbtcimepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbtcimepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbtcimepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbtcimepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbthaipluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbthaipluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbthaipluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbthaipluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtwebviewquickpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtwebviewquickpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtwebviewquickpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtwebviewquickpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3danimationpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3danimationpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3danimationpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3danimationpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dcorepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dcorepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dcorepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dcorepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dextraspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dextraspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dextraspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dextraspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dinputpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dinputpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dinputpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dinputpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dlogicpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dlogicpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dlogicpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dlogicpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3drenderpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3drenderpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3drenderpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3drenderpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dspatialaudioAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dspatialaudioConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dspatialaudioTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dspatialaudioTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickmultimediaAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickmultimediaConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickmultimediaTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickmultimediaTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quicktoolingAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quicktoolingConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quicktoolingTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quicktoolingTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickwindowAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickwindowConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickwindowTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickwindowTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qwaylandcompositorpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qwaylandcompositorpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qwaylandcompositorpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qwaylandcompositorpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6sharedimagepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6sharedimagepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6sharedimagepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6sharedimagepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6workerscriptpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6workerscriptpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6workerscriptpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6workerscriptpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QDebugMessageServiceFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QDebugMessageServiceFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QDebugMessageServiceFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QDebugMessageServiceFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QLocalClientConnectionFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QLocalClientConnectionFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QLocalClientConnectionFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QLocalClientConnectionFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebugServerFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebugServerFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebugServerFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebugServerFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebuggerServiceFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebuggerServiceFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebuggerServiceFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebuggerServiceFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlInspectorServiceFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlInspectorServiceFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlInspectorServiceFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlInspectorServiceFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugConnectorFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugConnectorFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugConnectorFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugConnectorFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugServiceFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugServiceFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugServiceFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugServiceFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlPreviewServiceFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlPreviewServiceFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlPreviewServiceFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlPreviewServiceFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlProfilerServiceFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlProfilerServiceFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlProfilerServiceFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlProfilerServiceFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuick3DProfilerAdapterFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuick3DProfilerAdapterFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuick3DProfilerAdapterFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuick3DProfilerAdapterFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuickProfilerAdapterFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuickProfilerAdapterFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuickProfilerAdapterFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuickProfilerAdapterFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QTcpServerConnectionFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QTcpServerConnectionFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QTcpServerConnectionFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QTcpServerConnectionFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlConfigExtras.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlFindQmlscInternal.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlModuleDirMappingTemplate.qrc.in /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlPlugins.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlPublicCMakeHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6qmldirTemplate.cmake.in /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlIntegration/Qt6QmlIntegrationAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlIntegration/Qt6QmlIntegrationConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlIntegration/Qt6QmlIntegrationConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlIntegration/Qt6QmlIntegrationConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlIntegration/Qt6QmlIntegrationTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlIntegration/Qt6QmlIntegrationVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsVersionlessTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickPlugins.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsVersionlessTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsMacros.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsPlugins.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsVersionlessTargets.cmake /usr/share/cmake-3.28/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.28/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompileFeatures.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.28/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake /usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.28/Modules/CMakeNinjaFindMake.cmake /usr/share/cmake-3.28/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.28/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake-3.28/Modules/CMakePushCheckState.cmake /usr/share/cmake-3.28/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake /usr/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake /usr/share/cmake-3.28/Modules/CheckIncludeFileCXX.cmake /usr/share/cmake-3.28/Modules/CheckLibraryExists.cmake /usr/share/cmake-3.28/Modules/CheckSymbolExists.cmake /usr/share/cmake-3.28/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-FindBinUtils.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU.cmake /usr/share/cmake-3.28/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/FeatureSummary.cmake /usr/share/cmake-3.28/Modules/FindOpenCL.cmake /usr/share/cmake-3.28/Modules/FindOpenGL.cmake /usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake-3.28/Modules/FindPackageMessage.cmake /usr/share/cmake-3.28/Modules/FindPkgConfig.cmake /usr/share/cmake-3.28/Modules/FindThreads.cmake /usr/share/cmake-3.28/Modules/FindVulkan.cmake /usr/share/cmake-3.28/Modules/GNUInstallDirs.cmake /usr/share/cmake-3.28/Modules/Internal/CheckCompilerFlag.cmake /usr/share/cmake-3.28/Modules/Internal/CheckFlagCommonConfig.cmake /usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake /usr/share/cmake-3.28/Modules/Internal/FeatureTesting.cmake /usr/share/cmake-3.28/Modules/MacroAddFileDependencies.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-Determine-CXX.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake /usr/share/cmake-3.28/Modules/Platform/Linux.cmake /usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.28.3/CMakeCXXCompiler.cmake CMakeFiles/3.28.3/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build .qt/qml_imports/av-routing-and-mixing_conf.cmake /home/<USER>/PROJECTS/av-routing-and-mixing/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/CMakeLists.txt /home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include/splash/splash-resources.qrc /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/3rdparty/kwin/FindXKB.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/FindWrapAtomic.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/FindWrapOpenGL.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/FindWrapVulkanHeaders.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Config.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Targets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeature.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeatureCommon.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtInstallPaths.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/qt.toolchain.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Concurrent/Qt6ConcurrentVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreConfigExtras.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreConfigureFileTemplate.in /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreResourceInit.in.cpp /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Core/Qt6CoreVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusMacros.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBus/Qt6DBusVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6DBusTools/Qt6DBusToolsVersionlessTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate/Qt6ExamplesAssetDownloaderPrivateVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiPlugins.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6GuiVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QComposePlatformInputContextPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QComposePlatformInputContextPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QComposePlatformInputContextPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QComposePlatformInputContextPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSEmulatorIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSEmulatorIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSEmulatorIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSEmulatorIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsEglDeviceIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsEglDeviceIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsEglDeviceIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsEglDeviceIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsGbmIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsGbmIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsGbmIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSKmsGbmIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSX11IntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSX11IntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSX11IntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEglFSX11IntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevKeyboardPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevKeyboardPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevKeyboardPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevKeyboardPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevMousePluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevMousePluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevMousePluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevMousePluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTabletPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTabletPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTabletPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTabletPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTouchScreenPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTouchScreenPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTouchScreenPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QEvdevTouchScreenPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGifPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGifPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGtk3ThemePluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGtk3ThemePluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGtk3ThemePluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QGtk3ThemePluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICNSPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICNSPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICNSPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICNSPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICOPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICOPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QIbusPlatformInputContextPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QIbusPlatformInputContextPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QIbusPlatformInputContextPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QIbusPlatformInputContextPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QJpegPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QJpegPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QLinuxFbIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QLinuxFbIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QLinuxFbIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QLinuxFbIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalEglIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalEglIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalEglIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalEglIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTgaPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTgaPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTgaPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTgaPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTiffPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTiffPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTiffPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTiffPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVirtualKeyboardPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVirtualKeyboardPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVirtualKeyboardPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVirtualKeyboardPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVkKhrDisplayIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVkKhrDisplayIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVkKhrDisplayIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVkKhrDisplayIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVncIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVncIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVncIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QVncIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandEglPlatformIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandEglPlatformIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandEglPlatformIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandEglPlatformIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWaylandIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWbmpPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWbmpPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWbmpPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWbmpPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWebpPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWebpPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWebpPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QWebpPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbEglIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbEglIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbEglIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbEglIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbGlxIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbGlxIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbGlxIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbGlxIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbIntegrationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbIntegrationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbIntegrationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXcbIntegrationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXdgDesktopPortalThemePluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXdgDesktopPortalThemePluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXdgDesktopPortalThemePluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Gui/Qt6QXdgDesktopPortalThemePluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaMacros.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaPlugins.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6MultimediaVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6QFFmpegMediaPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6QFFmpegMediaPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6QFFmpegMediaPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Multimedia/Qt6QFFmpegMediaPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkPlugins.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6NetworkVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QGlibNetworkInformationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QGlibNetworkInformationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QGlibNetworkInformationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QGlibNetworkInformationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QNetworkManagerNetworkInformationPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QNetworkManagerNetworkInformationPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QNetworkManagerNetworkInformationPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QNetworkManagerNetworkInformationPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6DataVisualizationQmlpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6DataVisualizationQmlpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6DataVisualizationQmlpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6DataVisualizationQmlpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6GraphspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6GraphspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6GraphspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6GraphspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6LabsPlatformpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6LabsPlatformpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6LabsPlatformpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6LabsPlatformpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlAssetDownloaderpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlAssetDownloaderpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlAssetDownloaderpluginDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlAssetDownloaderpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlAssetDownloaderpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlNetworkpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlNetworkpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlNetworkpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QmlNetworkpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6Quick3DXrpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6Quick3DXrpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6Quick3DXrpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6Quick3DXrpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickControlsTestUtilsPrivatepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickControlsTestUtilsPrivatepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickControlsTestUtilsPrivatepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickControlsTestUtilsPrivatepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickTestpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickTestpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickTestpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6QuickTestpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6SensorsQuickpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6SensorsQuickpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6SensorsQuickpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6SensorsQuickpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6TextToSpeechQmlAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6TextToSpeechQmlConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6TextToSpeechQmlTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6TextToSpeechQmlTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorIviapplicationpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorIviapplicationpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorIviapplicationpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorIviapplicationpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorPresentationTimepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorPresentationTimepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorPresentationTimepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorPresentationTimepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorQtShellAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorQtShellConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorQtShellTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorQtShellTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorWLShellpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorWLShellpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorWLShellpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorWLShellpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorXdgShellpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorXdgShellpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorXdgShellpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandCompositorXdgShellpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingExtensionAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingExtensionConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingExtensionTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingExtensionTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WaylandTextureSharingTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WebChannelQuickpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WebChannelQuickpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WebChannelQuickpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6WebChannelQuickpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_locationAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_locationConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_locationTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_locationTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_remoteobjectsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_remoteobjectsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_remoteobjectsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_remoteobjectsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_scxmlAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_scxmlConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_scxmlTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6declarative_scxmlTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6effectspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6effectspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6effectspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6effectspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6grpcquickpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6grpcquickpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6grpcquickpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6grpcquickpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsanimationpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsanimationpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsanimationpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsanimationpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsmodelspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsmodelspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsmodelspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6labsmodelspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6lottieqtpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6lottieqtpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6lottieqtpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6lottieqtpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6modelspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6modelspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6modelspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6modelspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6particlespluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6particlespluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6particlespluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6particlespluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6positioningquickpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6positioningquickpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6positioningquickpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6positioningquickpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6protobufquickpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6protobufquickpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6protobufquickpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6protobufquickpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlfolderlistmodelpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlfolderlistmodelpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlfolderlistmodelpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlfolderlistmodelpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmllocalstoragepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmllocalstoragepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmllocalstoragepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmllocalstoragepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlsettingspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlsettingspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlsettingspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlsettingspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlshapespluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlshapespluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlshapespluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlshapespluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwavefrontmeshpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwavefrontmeshpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwavefrontmeshpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwavefrontmeshpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwebsocketsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwebsocketsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwebsocketsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlwebsocketsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlxmllistmodelpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlxmllistmodelpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlxmllistmodelpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qmlxmllistmodelpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dphysicspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dphysicspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dphysicspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dphysicspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquick3dpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquicklayoutspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquicklayoutspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquicklayoutspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquicklayoutspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquickvectorimagepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquickvectorimagepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquickvectorimagepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qquickvectorimagepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtchartsqml2AdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtchartsqml2Config.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtchartsqml2Targets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtchartsqml2Targets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectsprivateAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectsprivateConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectsprivateTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtgraphicaleffectsprivateTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlcorepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlcorepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlcorepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlcorepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlstatemachineAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlstatemachineConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlstatemachineTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtqmlstatemachineTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick2pluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick2pluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick2pluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick2pluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dassetutilspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dassetutilspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dassetutilspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dassetutilspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3deffectpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3deffectpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3deffectpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3deffectpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelpersimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelpersimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelpersimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelpersimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelperspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelperspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelperspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dhelperspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticleeffectspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticleeffectspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticleeffectspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticleeffectspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticles3dpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticles3dpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticles3dpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dparticles3dpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dphysicshelperspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dphysicshelperspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dphysicshelperspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquick3dphysicshelperspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstyleimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstyleimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstyleimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstyleimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2basicstylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3styleimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3styleimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3styleimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3styleimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3stylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3stylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3stylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fluentwinui3stylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstyleimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstyleimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstyleimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstyleimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2fusionstylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestyleimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestyleimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestyleimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestyleimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2imaginestylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2implpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2implpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2implpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2implpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstyleimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstyleimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstyleimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstyleimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2materialstylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2nativestylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2nativestylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2nativestylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2nativestylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2pluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2pluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2pluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2pluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstyleimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstyleimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstyleimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstyleimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstylepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstylepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstylepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickcontrols2universalstylepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogs2quickimplpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogs2quickimplpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogs2quickimplpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogs2quickimplpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickdialogspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene2dpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene2dpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene2dpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene2dpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene3dpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene3dpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene3dpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquickscene3dpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktemplates2pluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktemplates2pluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktemplates2pluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktemplates2pluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelineblendtreespluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelineblendtreespluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelineblendtreespluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelineblendtreespluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelinepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelinepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelinepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtquicktimelinepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbbuiltinstylespluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbbuiltinstylespluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbbuiltinstylespluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbbuiltinstylespluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbcomponentspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbcomponentspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbcomponentspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbcomponentspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbhangulpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbhangulpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbhangulpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbhangulpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkblayoutspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkblayoutspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkblayoutspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkblayoutspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbopenwnnpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbopenwnnpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbopenwnnpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbopenwnnpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpinyinpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpinyinpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpinyinpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpinyinpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbpluginspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbsettingspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbsettingspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbsettingspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbsettingspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbstylespluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbstylespluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbstylespluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbstylespluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbtcimepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbtcimepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbtcimepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbtcimepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbthaipluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbthaipluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbthaipluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtvkbthaipluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtwebviewquickpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtwebviewquickpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtwebviewquickpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qtwebviewquickpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3danimationpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3danimationpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3danimationpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3danimationpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dcorepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dcorepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dcorepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dcorepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dextraspluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dextraspluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dextraspluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dextraspluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dinputpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dinputpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dinputpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dinputpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dlogicpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dlogicpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dlogicpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dlogicpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3drenderpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3drenderpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3drenderpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3drenderpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dspatialaudioAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dspatialaudioConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dspatialaudioTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quick3dspatialaudioTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickmultimediaAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickmultimediaConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickmultimediaTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickmultimediaTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quicktoolingAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quicktoolingConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quicktoolingTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quicktoolingTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickwindowAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickwindowConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickwindowTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6quickwindowTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qwaylandcompositorpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qwaylandcompositorpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qwaylandcompositorpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6qwaylandcompositorpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6sharedimagepluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6sharedimagepluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6sharedimagepluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6sharedimagepluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6workerscriptpluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6workerscriptpluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6workerscriptpluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/QmlPlugins/Qt6workerscriptpluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QDebugMessageServiceFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QDebugMessageServiceFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QDebugMessageServiceFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QDebugMessageServiceFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QLocalClientConnectionFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QLocalClientConnectionFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QLocalClientConnectionFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QLocalClientConnectionFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebugServerFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebugServerFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebugServerFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebugServerFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebuggerServiceFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebuggerServiceFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebuggerServiceFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlDebuggerServiceFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlInspectorServiceFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlInspectorServiceFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlInspectorServiceFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlInspectorServiceFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugConnectorFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugConnectorFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugConnectorFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugConnectorFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugServiceFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugServiceFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugServiceFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlNativeDebugServiceFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlPreviewServiceFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlPreviewServiceFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlPreviewServiceFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlPreviewServiceFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlProfilerServiceFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlProfilerServiceFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlProfilerServiceFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQmlProfilerServiceFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuick3DProfilerAdapterFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuick3DProfilerAdapterFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuick3DProfilerAdapterFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuick3DProfilerAdapterFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuickProfilerAdapterFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuickProfilerAdapterFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuickProfilerAdapterFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QQuickProfilerAdapterFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QTcpServerConnectionFactoryPluginAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QTcpServerConnectionFactoryPluginConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QTcpServerConnectionFactoryPluginTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QTcpServerConnectionFactoryPluginTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlConfigExtras.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlFindQmlscInternal.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlModuleDirMappingTemplate.qrc.in /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlPlugins.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlPublicCMakeHelpers.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6QmlVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Qml/Qt6qmldirTemplate.cmake.in /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlAssetDownloader/Qt6QmlAssetDownloaderVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlIntegration/Qt6QmlIntegrationAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlIntegration/Qt6QmlIntegrationConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlIntegration/Qt6QmlIntegrationConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlIntegration/Qt6QmlIntegrationConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlIntegration/Qt6QmlIntegrationTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlIntegration/Qt6QmlIntegrationVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlModels/Qt6QmlModelsVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlTools/Qt6QmlToolsVersionlessTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QmlWorkerScript/Qt6QmlWorkerScriptVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickPlugins.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Quick/Qt6QuickVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6QuickTools/Qt6QuickToolsVersionlessTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsMacros.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsPlugins.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsVersionlessAliasTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsAdditionalTargetInfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfig.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersion.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersionImpl.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsDependencies.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets-relwithdebinfo.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets.cmake /opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsVersionlessTargets.cmake /usr/share/cmake-3.28/Modules/CMakeCXXCompiler.cmake.in /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp /usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.28/Modules/CMakeCompilerIdDetection.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompileFeatures.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake /usr/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake /usr/share/cmake-3.28/Modules/CMakeFindBinUtils.cmake /usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake /usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.28/Modules/CMakeNinjaFindMake.cmake /usr/share/cmake-3.28/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake /usr/share/cmake-3.28/Modules/CMakeParseLibraryArchitecture.cmake /usr/share/cmake-3.28/Modules/CMakePushCheckState.cmake /usr/share/cmake-3.28/Modules/CMakeSystem.cmake.in /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake /usr/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake /usr/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake /usr/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake /usr/share/cmake-3.28/Modules/CheckIncludeFileCXX.cmake /usr/share/cmake-3.28/Modules/CheckLibraryExists.cmake /usr/share/cmake-3.28/Modules/CheckSymbolExists.cmake /usr/share/cmake-3.28/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-FindBinUtils.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU.cmake /usr/share/cmake-3.28/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/share/cmake-3.28/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/TI-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/share/cmake-3.28/Modules/FeatureSummary.cmake /usr/share/cmake-3.28/Modules/FindOpenCL.cmake /usr/share/cmake-3.28/Modules/FindOpenGL.cmake /usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake /usr/share/cmake-3.28/Modules/FindPackageMessage.cmake /usr/share/cmake-3.28/Modules/FindPkgConfig.cmake /usr/share/cmake-3.28/Modules/FindThreads.cmake /usr/share/cmake-3.28/Modules/FindVulkan.cmake /usr/share/cmake-3.28/Modules/GNUInstallDirs.cmake /usr/share/cmake-3.28/Modules/Internal/CheckCompilerFlag.cmake /usr/share/cmake-3.28/Modules/Internal/CheckFlagCommonConfig.cmake /usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake /usr/share/cmake-3.28/Modules/Internal/FeatureTesting.cmake /usr/share/cmake-3.28/Modules/MacroAddFileDependencies.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-Determine-CXX.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake /usr/share/cmake-3.28/Modules/Platform/Linux.cmake /usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.28.3/CMakeCXXCompiler.cmake CMakeFiles/3.28.3/CMakeSystem.cmake: phony


#############################################
# Clean additional files.

build CMakeFiles/clean.additional: CLEAN_ADDITIONAL
  CONFIG = Debug


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles/clean.additional


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
