GSS_libgoal_libav_autogen/timestamp: \
	/home/<USER>/PROJECTS/av-routing-and-mixing/build/libgoal-libav/GSS_libgoal_libav_autogen/moc_predefs.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/CMakeLists.txt \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/filtercomplex.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/filtercomplex.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/imageloader.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/imageloader.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavbuffer.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavbuffer.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder2.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder2.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdirectsaver.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdirectsaver.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavencoder.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavencoder.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavfilter.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavfilter.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavhelpers.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayer.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayer.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayerdirector.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayerdirector.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavprotocolcommons.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavreader.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavreader.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavwriter.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavwriter.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libswshelper.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libswshelper.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/simpleexportdecoder.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/simpleexportdecoder.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/streamreaderinterface.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/videoframeexporter.cpp \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/videoframeexporter.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/elapsedtimereference.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/memorypool.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/qthreadedobject.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/threadsafequeue.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/universalplayer.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/colorcorrection.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/framemetadata.h \
	/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/videoframe.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/QtConcurrent \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/QtConcurrentDepends \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtaskbuilder.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrent_global.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentcompilertest.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentexports.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentfilter.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentfilterkernel.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentfunctionwrappers.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentiteratekernel.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentmap.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentmapkernel.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentmedian.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentreducekernel.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentrun.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentrunbase.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentstoredfunctioncall.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrenttask.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentthreadengine.h \
	/opt/Qt/6.8.2/gcc_64/include/QtConcurrent/qtconcurrentversion.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QByteArray \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QDataStream \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QDateTime \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QDeadlineTimer \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QDebug \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QElapsedTimer \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QFileInfo \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QHash \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QIODevice \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QJsonArray \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QJsonDocument \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QJsonObject \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QJsonValue \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QList \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QMap \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QMutex \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QMutexLocker \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QObject \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QPair \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QQueue \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QReadWriteLock \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QSemaphore \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QSize \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QString \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QStringList \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QThread \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QTimer \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QUuid \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QVariant \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QVector \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QtCore \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QtCoreDepends \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/QtDebug \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/q20algorithm.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/q20chrono.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/q20functional.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/q20iterator.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/q20map.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/q20memory.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/q20type_traits.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/q20vector.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/q23functional.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/q23utility.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/q26numeric.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qabstractanimation.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qabstracteventdispatcher.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qabstractitemmodel.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qabstractnativeeventfilter.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qabstractproxymodel.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qalgorithms.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qanimationgroup.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qanystringview.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qapplicationstatic.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qarraydata.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qarraydataops.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qarraydatapointer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qassert.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qassociativeiterable.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qatomic.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qatomic_cxx11.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qatomicscopedvaluerollback.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qbasicatomic.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qbasictimer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qbindingstorage.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qbitarray.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qbuffer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qbytearray.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qbytearrayalgorithms.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qbytearraylist.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qbytearraymatcher.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qbytearrayview.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcache.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcalendar.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcborarray.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcborcommon.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcbormap.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcborstream.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcborstreamreader.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcborstreamwriter.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcborvalue.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qchar.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qchronotimer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcollator.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcommandlineoption.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcommandlineparser.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcompare.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcompare_impl.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcomparehelpers.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcompilerdetection.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qconcatenatetablesproxymodel.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qconfig.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qconstructormacros.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcontainerfwd.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcontainerinfo.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcontainertools_impl.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcontiguouscache.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcoreapplication.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcoreapplication_platform.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcoreevent.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qcryptographichash.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qdarwinhelpers.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qdatastream.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qdatetime.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qdeadlinetimer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qdebug.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qdir.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qdiriterator.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qdirlisting.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qeasingcurve.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qelapsedtimer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qendian.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qeventloop.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qexception.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qexceptionhandling.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfactoryinterface.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfile.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfiledevice.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfileinfo.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfileselector.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfilesystemwatcher.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qflags.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfloat16.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qforeach.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfunctionaltools_impl.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfunctionpointer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfuture.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfuture_impl.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfutureinterface.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfuturesynchronizer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qfuturewatcher.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qgenericatomic.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qglobal.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qglobalstatic.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qhash.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qhashfunctions.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qidentityproxymodel.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qiodevice.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qiodevicebase.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qitemselectionmodel.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qiterable.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qiterator.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qjsonarray.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qjsondocument.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qjsonobject.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qjsonvalue.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qlatin1stringmatcher.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qlatin1stringview.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qlibrary.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qlibraryinfo.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qline.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qlist.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qlocale.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qlockfile.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qlogging.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qloggingcategory.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qmalloc.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qmap.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qmargins.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qmath.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qmetacontainer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qmetaobject.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qmetatype.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qmimedata.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qmimedatabase.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qmimetype.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qminmax.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qmutex.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qnamespace.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qnativeinterface.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qnumeric.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qobject.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qobject_impl.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qobjectcleanuphandler.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qobjectdefs.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qoperatingsystemversion.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qoverload.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qpair.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qparallelanimationgroup.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qpauseanimation.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qpermissions.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qplugin.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qpluginloader.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qpoint.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qpointer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qprocess.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qprocessordetection.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qpromise.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qproperty.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qpropertyanimation.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qpropertyprivate.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qqueue.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qrandom.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qreadwritelock.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qrect.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qrefcount.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qregularexpression.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qresource.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qresultstore.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qrunnable.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsavefile.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qscopedpointer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qscopedvaluerollback.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qscopeguard.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsemaphore.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsequentialanimationgroup.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsequentialiterable.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qset.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsettings.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qshareddata.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qshareddata_impl.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsharedmemory.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsharedpointer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsignalmapper.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsimd.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsize.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsocketnotifier.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsortfilterproxymodel.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qspan.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstack.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstandardpaths.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstaticlatin1stringmatcher.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstorageinfo.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstring.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstringalgorithms.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstringbuilder.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstringconverter.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstringconverter_base.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstringfwd.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstringlist.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstringlistmodel.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstringliteral.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstringmatcher.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstringtokenizer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qstringview.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qswap.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsysinfo.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsystemdetection.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qsystemsemaphore.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtaggedpointer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtclasshelpermacros.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtconfiginclude.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtconfigmacros.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtcore-config.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtcoreexports.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtcoreversion.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtdeprecationdefinitions.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtdeprecationmarkers.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtemporarydir.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtemporaryfile.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtenvironmentvariables.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtextboundaryfinder.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtextstream.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qthread.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qthreadpool.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qthreadstorage.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtimeline.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtimer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtimezone.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtipccommon.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtmetamacros.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtnoop.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtpreprocessorsupport.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtranslator.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtransposeproxymodel.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtresource.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtsan_impl.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtsymbolmacros.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qttranslation.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qttypetraits.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtversion.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtversionchecks.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtypeinfo.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtyperevision.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qtypes.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qurl.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qurlquery.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qutf8stringview.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/quuid.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qvariant.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qvariantanimation.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qvarianthash.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qvariantlist.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qvariantmap.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qvarlengtharray.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qvector.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qversionnumber.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qversiontagging.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qwaitcondition.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qwineventnotifier.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qxmlstream.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qxpfunctional.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qxptype_traits.h \
	/opt/Qt/6.8.2/gcc_64/include/QtCore/qyieldcpu.h \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/QImage \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/qcolor.h \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/qimage.h \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/qpaintdevice.h \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/qpixelformat.h \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/qpolygon.h \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/qregion.h \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/qrgb.h \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/qrgba64.h \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/qtgui-config.h \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/qtguiexports.h \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/qtguiglobal.h \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/qtransform.h \
	/opt/Qt/6.8.2/gcc_64/include/QtGui/qwindowdefs.h \
	/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/QAudioBuffer \
	/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/QAudioFormat \
	/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/qaudio.h \
	/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/qaudiobuffer.h \
	/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/qaudioformat.h \
	/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/qtaudio.h \
	/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/qtmultimedia-config.h \
	/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/qtmultimediaexports.h \
	/opt/Qt/6.8.2/gcc_64/include/QtMultimedia/qtmultimediaglobal.h \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Config.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6Targets.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeature.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtFeatureCommon.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtInstallPaths.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake \
	/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake \
	/usr/include/alloca.h \
	/usr/include/asm-generic/errno-base.h \
	/usr/include/asm-generic/errno.h \
	/usr/include/assert.h \
	/usr/include/c++/13/algorithm \
	/usr/include/c++/13/array \
	/usr/include/c++/13/atomic \
	/usr/include/c++/13/backward/auto_ptr.h \
	/usr/include/c++/13/backward/binders.h \
	/usr/include/c++/13/bit \
	/usr/include/c++/13/bits/algorithmfwd.h \
	/usr/include/c++/13/bits/align.h \
	/usr/include/c++/13/bits/alloc_traits.h \
	/usr/include/c++/13/bits/allocated_ptr.h \
	/usr/include/c++/13/bits/allocator.h \
	/usr/include/c++/13/bits/atomic_base.h \
	/usr/include/c++/13/bits/atomic_futex.h \
	/usr/include/c++/13/bits/atomic_lockfree_defines.h \
	/usr/include/c++/13/bits/basic_ios.h \
	/usr/include/c++/13/bits/basic_ios.tcc \
	/usr/include/c++/13/bits/basic_string.h \
	/usr/include/c++/13/bits/basic_string.tcc \
	/usr/include/c++/13/bits/char_traits.h \
	/usr/include/c++/13/bits/charconv.h \
	/usr/include/c++/13/bits/chrono.h \
	/usr/include/c++/13/bits/codecvt.h \
	/usr/include/c++/13/bits/concept_check.h \
	/usr/include/c++/13/bits/cpp_type_traits.h \
	/usr/include/c++/13/bits/cxxabi_forced.h \
	/usr/include/c++/13/bits/cxxabi_init_exception.h \
	/usr/include/c++/13/bits/enable_special_members.h \
	/usr/include/c++/13/bits/erase_if.h \
	/usr/include/c++/13/bits/exception.h \
	/usr/include/c++/13/bits/exception_defines.h \
	/usr/include/c++/13/bits/exception_ptr.h \
	/usr/include/c++/13/bits/fs_dir.h \
	/usr/include/c++/13/bits/fs_fwd.h \
	/usr/include/c++/13/bits/fs_ops.h \
	/usr/include/c++/13/bits/fs_path.h \
	/usr/include/c++/13/bits/functexcept.h \
	/usr/include/c++/13/bits/functional_hash.h \
	/usr/include/c++/13/bits/hash_bytes.h \
	/usr/include/c++/13/bits/hashtable.h \
	/usr/include/c++/13/bits/hashtable_policy.h \
	/usr/include/c++/13/bits/invoke.h \
	/usr/include/c++/13/bits/ios_base.h \
	/usr/include/c++/13/bits/istream.tcc \
	/usr/include/c++/13/bits/list.tcc \
	/usr/include/c++/13/bits/locale_classes.h \
	/usr/include/c++/13/bits/locale_classes.tcc \
	/usr/include/c++/13/bits/locale_conv.h \
	/usr/include/c++/13/bits/locale_facets.h \
	/usr/include/c++/13/bits/locale_facets.tcc \
	/usr/include/c++/13/bits/locale_facets_nonio.h \
	/usr/include/c++/13/bits/locale_facets_nonio.tcc \
	/usr/include/c++/13/bits/localefwd.h \
	/usr/include/c++/13/bits/memory_resource.h \
	/usr/include/c++/13/bits/memoryfwd.h \
	/usr/include/c++/13/bits/move.h \
	/usr/include/c++/13/bits/nested_exception.h \
	/usr/include/c++/13/bits/new_allocator.h \
	/usr/include/c++/13/bits/node_handle.h \
	/usr/include/c++/13/bits/ostream.tcc \
	/usr/include/c++/13/bits/ostream_insert.h \
	/usr/include/c++/13/bits/parse_numbers.h \
	/usr/include/c++/13/bits/postypes.h \
	/usr/include/c++/13/bits/predefined_ops.h \
	/usr/include/c++/13/bits/ptr_traits.h \
	/usr/include/c++/13/bits/quoted_string.h \
	/usr/include/c++/13/bits/random.h \
	/usr/include/c++/13/bits/random.tcc \
	/usr/include/c++/13/bits/range_access.h \
	/usr/include/c++/13/bits/refwrap.h \
	/usr/include/c++/13/bits/requires_hosted.h \
	/usr/include/c++/13/bits/shared_ptr.h \
	/usr/include/c++/13/bits/shared_ptr_atomic.h \
	/usr/include/c++/13/bits/shared_ptr_base.h \
	/usr/include/c++/13/bits/specfun.h \
	/usr/include/c++/13/bits/sstream.tcc \
	/usr/include/c++/13/bits/std_abs.h \
	/usr/include/c++/13/bits/std_function.h \
	/usr/include/c++/13/bits/std_mutex.h \
	/usr/include/c++/13/bits/std_thread.h \
	/usr/include/c++/13/bits/stl_algo.h \
	/usr/include/c++/13/bits/stl_algobase.h \
	/usr/include/c++/13/bits/stl_bvector.h \
	/usr/include/c++/13/bits/stl_construct.h \
	/usr/include/c++/13/bits/stl_function.h \
	/usr/include/c++/13/bits/stl_heap.h \
	/usr/include/c++/13/bits/stl_iterator.h \
	/usr/include/c++/13/bits/stl_iterator_base_funcs.h \
	/usr/include/c++/13/bits/stl_iterator_base_types.h \
	/usr/include/c++/13/bits/stl_list.h \
	/usr/include/c++/13/bits/stl_map.h \
	/usr/include/c++/13/bits/stl_multimap.h \
	/usr/include/c++/13/bits/stl_numeric.h \
	/usr/include/c++/13/bits/stl_pair.h \
	/usr/include/c++/13/bits/stl_raw_storage_iter.h \
	/usr/include/c++/13/bits/stl_relops.h \
	/usr/include/c++/13/bits/stl_tempbuf.h \
	/usr/include/c++/13/bits/stl_tree.h \
	/usr/include/c++/13/bits/stl_uninitialized.h \
	/usr/include/c++/13/bits/stl_vector.h \
	/usr/include/c++/13/bits/stream_iterator.h \
	/usr/include/c++/13/bits/streambuf.tcc \
	/usr/include/c++/13/bits/streambuf_iterator.h \
	/usr/include/c++/13/bits/string_view.tcc \
	/usr/include/c++/13/bits/stringfwd.h \
	/usr/include/c++/13/bits/this_thread_sleep.h \
	/usr/include/c++/13/bits/uniform_int_dist.h \
	/usr/include/c++/13/bits/unique_lock.h \
	/usr/include/c++/13/bits/unique_ptr.h \
	/usr/include/c++/13/bits/unordered_map.h \
	/usr/include/c++/13/bits/uses_allocator.h \
	/usr/include/c++/13/bits/uses_allocator_args.h \
	/usr/include/c++/13/bits/utility.h \
	/usr/include/c++/13/bits/vector.tcc \
	/usr/include/c++/13/cassert \
	/usr/include/c++/13/cctype \
	/usr/include/c++/13/cerrno \
	/usr/include/c++/13/chrono \
	/usr/include/c++/13/climits \
	/usr/include/c++/13/clocale \
	/usr/include/c++/13/cmath \
	/usr/include/c++/13/codecvt \
	/usr/include/c++/13/compare \
	/usr/include/c++/13/condition_variable \
	/usr/include/c++/13/cstddef \
	/usr/include/c++/13/cstdint \
	/usr/include/c++/13/cstdio \
	/usr/include/c++/13/cstdlib \
	/usr/include/c++/13/cstring \
	/usr/include/c++/13/ctime \
	/usr/include/c++/13/cwchar \
	/usr/include/c++/13/cwctype \
	/usr/include/c++/13/debug/assertions.h \
	/usr/include/c++/13/debug/debug.h \
	/usr/include/c++/13/exception \
	/usr/include/c++/13/ext/aligned_buffer.h \
	/usr/include/c++/13/ext/alloc_traits.h \
	/usr/include/c++/13/ext/atomicity.h \
	/usr/include/c++/13/ext/concurrence.h \
	/usr/include/c++/13/ext/numeric_traits.h \
	/usr/include/c++/13/ext/string_conversions.h \
	/usr/include/c++/13/ext/type_traits.h \
	/usr/include/c++/13/filesystem \
	/usr/include/c++/13/functional \
	/usr/include/c++/13/future \
	/usr/include/c++/13/initializer_list \
	/usr/include/c++/13/iomanip \
	/usr/include/c++/13/ios \
	/usr/include/c++/13/iosfwd \
	/usr/include/c++/13/iostream \
	/usr/include/c++/13/istream \
	/usr/include/c++/13/iterator \
	/usr/include/c++/13/limits \
	/usr/include/c++/13/list \
	/usr/include/c++/13/locale \
	/usr/include/c++/13/map \
	/usr/include/c++/13/memory \
	/usr/include/c++/13/mutex \
	/usr/include/c++/13/new \
	/usr/include/c++/13/numeric \
	/usr/include/c++/13/optional \
	/usr/include/c++/13/ostream \
	/usr/include/c++/13/pstl/execution_defs.h \
	/usr/include/c++/13/pstl/glue_algorithm_defs.h \
	/usr/include/c++/13/pstl/glue_memory_defs.h \
	/usr/include/c++/13/pstl/glue_numeric_defs.h \
	/usr/include/c++/13/random \
	/usr/include/c++/13/ratio \
	/usr/include/c++/13/sstream \
	/usr/include/c++/13/stdexcept \
	/usr/include/c++/13/streambuf \
	/usr/include/c++/13/string \
	/usr/include/c++/13/string_view \
	/usr/include/c++/13/system_error \
	/usr/include/c++/13/thread \
	/usr/include/c++/13/tr1/bessel_function.tcc \
	/usr/include/c++/13/tr1/beta_function.tcc \
	/usr/include/c++/13/tr1/ell_integral.tcc \
	/usr/include/c++/13/tr1/exp_integral.tcc \
	/usr/include/c++/13/tr1/gamma.tcc \
	/usr/include/c++/13/tr1/hypergeometric.tcc \
	/usr/include/c++/13/tr1/legendre_function.tcc \
	/usr/include/c++/13/tr1/modified_bessel_func.tcc \
	/usr/include/c++/13/tr1/poly_hermite.tcc \
	/usr/include/c++/13/tr1/poly_laguerre.tcc \
	/usr/include/c++/13/tr1/riemann_zeta.tcc \
	/usr/include/c++/13/tr1/special_function_util.h \
	/usr/include/c++/13/tuple \
	/usr/include/c++/13/type_traits \
	/usr/include/c++/13/typeinfo \
	/usr/include/c++/13/unordered_map \
	/usr/include/c++/13/utility \
	/usr/include/c++/13/variant \
	/usr/include/c++/13/vector \
	/usr/include/ctype.h \
	/usr/include/endian.h \
	/usr/include/errno.h \
	/usr/include/features-time64.h \
	/usr/include/features.h \
	/usr/include/inttypes.h \
	/usr/include/libintl.h \
	/usr/include/limits.h \
	/usr/include/linux/errno.h \
	/usr/include/linux/limits.h \
	/usr/include/locale.h \
	/usr/include/math.h \
	/usr/include/pthread.h \
	/usr/include/sched.h \
	/usr/include/stdc-predef.h \
	/usr/include/stdint.h \
	/usr/include/stdio.h \
	/usr/include/stdlib.h \
	/usr/include/string.h \
	/usr/include/strings.h \
	/usr/include/time.h \
	/usr/include/wchar.h \
	/usr/include/wctype.h \
	/usr/include/x86_64-linux-gnu/asm/errno.h \
	/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
	/usr/include/x86_64-linux-gnu/bits/byteswap.h \
	/usr/include/x86_64-linux-gnu/bits/cpu-set.h \
	/usr/include/x86_64-linux-gnu/bits/endian.h \
	/usr/include/x86_64-linux-gnu/bits/endianness.h \
	/usr/include/x86_64-linux-gnu/bits/errno.h \
	/usr/include/x86_64-linux-gnu/bits/floatn-common.h \
	/usr/include/x86_64-linux-gnu/bits/floatn.h \
	/usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
	/usr/include/x86_64-linux-gnu/bits/fp-fast.h \
	/usr/include/x86_64-linux-gnu/bits/fp-logb.h \
	/usr/include/x86_64-linux-gnu/bits/iscanonical.h \
	/usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
	/usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
	/usr/include/x86_64-linux-gnu/bits/local_lim.h \
	/usr/include/x86_64-linux-gnu/bits/locale.h \
	/usr/include/x86_64-linux-gnu/bits/long-double.h \
	/usr/include/x86_64-linux-gnu/bits/math-vector.h \
	/usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
	/usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h \
	/usr/include/x86_64-linux-gnu/bits/mathcalls.h \
	/usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
	/usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
	/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
	/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
	/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
	/usr/include/x86_64-linux-gnu/bits/sched.h \
	/usr/include/x86_64-linux-gnu/bits/select.h \
	/usr/include/x86_64-linux-gnu/bits/setjmp.h \
	/usr/include/x86_64-linux-gnu/bits/stdint-intn.h \
	/usr/include/x86_64-linux-gnu/bits/stdint-least.h \
	/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
	/usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
	/usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
	/usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
	/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h \
	/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
	/usr/include/x86_64-linux-gnu/bits/time.h \
	/usr/include/x86_64-linux-gnu/bits/time64.h \
	/usr/include/x86_64-linux-gnu/bits/timesize.h \
	/usr/include/x86_64-linux-gnu/bits/timex.h \
	/usr/include/x86_64-linux-gnu/bits/types.h \
	/usr/include/x86_64-linux-gnu/bits/types/FILE.h \
	/usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
	/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/error_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
	/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h \
	/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
	/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h \
	/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
	/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
	/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
	/usr/include/x86_64-linux-gnu/bits/types/time_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
	/usr/include/x86_64-linux-gnu/bits/types/wint_t.h \
	/usr/include/x86_64-linux-gnu/bits/typesizes.h \
	/usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
	/usr/include/x86_64-linux-gnu/bits/uio_lim.h \
	/usr/include/x86_64-linux-gnu/bits/waitflags.h \
	/usr/include/x86_64-linux-gnu/bits/waitstatus.h \
	/usr/include/x86_64-linux-gnu/bits/wchar.h \
	/usr/include/x86_64-linux-gnu/bits/wctype-wchar.h \
	/usr/include/x86_64-linux-gnu/bits/wordsize.h \
	/usr/include/x86_64-linux-gnu/bits/xopen_lim.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/atomic_word.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/c++allocator.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/c++config.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/c++locale.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/cpu_defines.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/ctype_base.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/ctype_inline.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/error_constants.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/gthr-default.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/gthr.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/messages_members.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/opt_random.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/os_defines.h \
	/usr/include/x86_64-linux-gnu/c++/13/bits/time_members.h \
	/usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
	/usr/include/x86_64-linux-gnu/gnu/stubs.h \
	/usr/include/x86_64-linux-gnu/libavcodec/avcodec.h \
	/usr/include/x86_64-linux-gnu/libavcodec/codec.h \
	/usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h \
	/usr/include/x86_64-linux-gnu/libavcodec/codec_id.h \
	/usr/include/x86_64-linux-gnu/libavcodec/codec_par.h \
	/usr/include/x86_64-linux-gnu/libavcodec/defs.h \
	/usr/include/x86_64-linux-gnu/libavcodec/packet.h \
	/usr/include/x86_64-linux-gnu/libavcodec/version.h \
	/usr/include/x86_64-linux-gnu/libavcodec/version_major.h \
	/usr/include/x86_64-linux-gnu/libavfilter/avfilter.h \
	/usr/include/x86_64-linux-gnu/libavfilter/buffersink.h \
	/usr/include/x86_64-linux-gnu/libavfilter/buffersrc.h \
	/usr/include/x86_64-linux-gnu/libavfilter/version.h \
	/usr/include/x86_64-linux-gnu/libavfilter/version_major.h \
	/usr/include/x86_64-linux-gnu/libavformat/avformat.h \
	/usr/include/x86_64-linux-gnu/libavformat/avio.h \
	/usr/include/x86_64-linux-gnu/libavformat/version.h \
	/usr/include/x86_64-linux-gnu/libavformat/version_major.h \
	/usr/include/x86_64-linux-gnu/libavutil/attributes.h \
	/usr/include/x86_64-linux-gnu/libavutil/avassert.h \
	/usr/include/x86_64-linux-gnu/libavutil/avconfig.h \
	/usr/include/x86_64-linux-gnu/libavutil/avutil.h \
	/usr/include/x86_64-linux-gnu/libavutil/bswap.h \
	/usr/include/x86_64-linux-gnu/libavutil/buffer.h \
	/usr/include/x86_64-linux-gnu/libavutil/channel_layout.h \
	/usr/include/x86_64-linux-gnu/libavutil/common.h \
	/usr/include/x86_64-linux-gnu/libavutil/dict.h \
	/usr/include/x86_64-linux-gnu/libavutil/error.h \
	/usr/include/x86_64-linux-gnu/libavutil/frame.h \
	/usr/include/x86_64-linux-gnu/libavutil/hwcontext.h \
	/usr/include/x86_64-linux-gnu/libavutil/imgutils.h \
	/usr/include/x86_64-linux-gnu/libavutil/intfloat.h \
	/usr/include/x86_64-linux-gnu/libavutil/intreadwrite.h \
	/usr/include/x86_64-linux-gnu/libavutil/log.h \
	/usr/include/x86_64-linux-gnu/libavutil/macros.h \
	/usr/include/x86_64-linux-gnu/libavutil/mathematics.h \
	/usr/include/x86_64-linux-gnu/libavutil/mem.h \
	/usr/include/x86_64-linux-gnu/libavutil/opt.h \
	/usr/include/x86_64-linux-gnu/libavutil/pixdesc.h \
	/usr/include/x86_64-linux-gnu/libavutil/pixfmt.h \
	/usr/include/x86_64-linux-gnu/libavutil/rational.h \
	/usr/include/x86_64-linux-gnu/libavutil/samplefmt.h \
	/usr/include/x86_64-linux-gnu/libavutil/version.h \
	/usr/include/x86_64-linux-gnu/libswresample/swresample.h \
	/usr/include/x86_64-linux-gnu/libswresample/version.h \
	/usr/include/x86_64-linux-gnu/libswresample/version_major.h \
	/usr/include/x86_64-linux-gnu/libswscale/swscale.h \
	/usr/include/x86_64-linux-gnu/libswscale/version.h \
	/usr/include/x86_64-linux-gnu/libswscale/version_major.h \
	/usr/include/x86_64-linux-gnu/sys/cdefs.h \
	/usr/include/x86_64-linux-gnu/sys/select.h \
	/usr/include/x86_64-linux-gnu/sys/types.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/adxintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/amxbf16intrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/amxcomplexintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/amxfp16intrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/amxint8intrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/amxtileintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx2intrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx5124fmapsintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx5124vnniwintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512bf16intrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512bf16vlintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512bitalgintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512bwintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512cdintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512dqintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512erintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512fintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512fp16intrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512fp16vlintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512ifmaintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512ifmavlintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512pfintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vbmi2intrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vbmi2vlintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vbmiintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vbmivlintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vlbwintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vldqintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vlintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vnniintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vnnivlintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vp2intersectintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vp2intersectvlintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vpopcntdqintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avx512vpopcntdqvlintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avxifmaintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avxintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avxneconvertintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avxvnniint8intrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/avxvnniintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/bmi2intrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/bmiintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/cetintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/cldemoteintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/clflushoptintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/clwbintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/clzerointrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/cmpccxaddintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/emmintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/enqcmdintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/f16cintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/fmaintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/fxsrintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/gfniintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/hresetintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/ia32intrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/immintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/keylockerintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/lwpintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/lzcntintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/mm_malloc.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/mmintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/movdirintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/mwaitintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/mwaitxintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/pconfigintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/pkuintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/pmmintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/popcntintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/prfchiintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/prfchwintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/raointintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/rdseedintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/rtmintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/serializeintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/sgxintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/shaintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/smmintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/stdarg.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/stdbool.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/stddef.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/tbmintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/tmmintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/tsxldtrkintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/uintrintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/vaesintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/vpclmulqdqintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/waitpkgintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/wbnoinvdintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/wmmintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/x86gprintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/xmmintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/xsavecintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/xsaveintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/xsaveoptintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/xsavesintrin.h \
	/usr/lib/gcc/x86_64-linux-gnu/13/include/xtestintrin.h \
	/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake \
	/usr/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake \
	/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake \
	/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake \
	/usr/share/cmake-3.28/Modules/FindPkgConfig.cmake \
	/usr/bin/cmake
