{"archive": {}, "artifacts": [{"path": "libgoal-widgets/libGSS_libgoal_widgets.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_include_directories"], "files": ["libgoal/libgoal-widgets/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 13, "parent": 0}, {"command": 1, "file": 0, "line": 37, "parent": 0}, {"command": 2, "file": 0, "line": 32, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17"}, {"backtrace": 2, "fragment": "-fPIC"}], "defines": [{"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-widgets"}, {"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets"}, {"backtrace": 0, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/include"}, {"backtrace": 3, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/mkspecs/linux-g++"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtGui"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 2, 4, 6, 8, 11]}], "dependencies": [{"id": "GSS_libgoal_widgets_autogen_timestamp_deps::@1a6ca22365c285a707f8"}, {"backtrace": 0, "id": "GSS_libgoal_widgets_autogen::@1a6ca22365c285a707f8"}], "id": "GSS_libgoal_widgets::@1a6ca22365c285a707f8", "name": "GSS_libgoal_widgets", "nameOnDisk": "libGSS_libgoal_widgets.a", "paths": {"build": "libgoal-widgets", "source": "libgoal/libgoal-widgets"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4, 6, 8, 11]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 5, 7]}, {"name": "Resources", "sourceIndexes": [9]}, {"name": "", "sourceIndexes": [10]}, {"name": "CMake Rules", "sourceIndexes": [12, 13]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-widgets/include/timeline/manager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-widgets/include/timeline/manager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-widgets/include/timeline/model.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-widgets/include/timeline/model.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-widgets/include/timeline/slider.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-widgets/include/timeline/slider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-widgets/include/splash/goalsplashscreen.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-widgets/include/splash/goalsplashscreen.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-widgets/include/splash/splash-resources.qrc", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/timestamp", "sourceGroupIndex": 3}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/timestamp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-widgets/GSS_libgoal_widgets_autogen/GGRFHCMMEE/qrc_splash-resources.cpp.rule", "sourceGroupIndex": 4}], "type": "STATIC_LIBRARY"}