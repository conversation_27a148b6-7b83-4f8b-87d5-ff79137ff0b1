{"archive": {}, "artifacts": [{"path": "libgoal-utils/libGSS_libgoal_utils.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_include_directories", "target_sources"], "files": ["libgoal/libgoal-utils/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 9, "parent": 0}, {"command": 1, "file": 0, "line": 51, "parent": 0}, {"command": 2, "file": 0, "line": 46, "parent": 0}, {"command": 3, "file": 0, "line": 31, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17"}, {"backtrace": 2, "fragment": "-fPIC"}], "defines": [{"backtrace": 2, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 2, "define": "QT_CORE_LIB"}], "includes": [{"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-utils"}, {"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils"}, {"backtrace": 0, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-utils/GSS_libgoal_utils_autogen/include"}, {"backtrace": 3, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtConcurrent"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/mkspecs/linux-g++"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 2, 5, 7, 9, 13, 15, 17, 19, 20]}], "dependencies": [{"id": "GSS_libgoal_utils_autogen_timestamp_deps::@6c8cb491c3014fb511f8"}, {"backtrace": 0, "id": "GSS_libgoal_utils_autogen::@6c8cb491c3014fb511f8"}], "id": "GSS_libgoal_utils::@6c8cb491c3014fb511f8", "name": "GSS_libgoal_utils", "nameOnDisk": "libGSS_libgoal_utils.a", "paths": {"build": "libgoal-utils", "source": "libgoal/libgoal-utils"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 5, 7, 9, 13, 15, 17, 19, 20]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 4, 6, 8, 10, 11, 12, 14, 16, 18, 21, 22]}, {"name": "", "sourceIndexes": [23]}, {"name": "CMake Rules", "sourceIndexes": [24]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-utils/GSS_libgoal_utils_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-utils/include/doitlater.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-utils/include/doitlater.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-utils/include/elapsedtimereference.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-utils/include/memorypool.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-utils/include/memorypool.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-utils/include/qthreadedobject.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-utils/include/qthreadedobject.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-utils/include/runguard.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-utils/include/runguard.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-utils/include/threadsafequeue.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-utils/include/timesynccache.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libgoal/libgoal-utils/include/universalplayer.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-utils/include/universalplayer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-utils/include/freqsync.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-utils/include/freqsync.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-utils/include/timedqueue.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-utils/include/timedqueue.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-utils/include/obfuscate.h", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "libgoal/libgoal-utils/include/goalsharedmemory.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "libgoal/libgoal-utils/include/goalsystemsemaphore.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "libgoal/libgoal-utils/include/goalsharedmemory.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "libgoal/libgoal-utils/include/goalsystemsemaphore.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-utils/GSS_libgoal_utils_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-utils/GSS_libgoal_utils_autogen/timestamp.rule", "sourceGroupIndex": 3}], "type": "STATIC_LIBRARY"}