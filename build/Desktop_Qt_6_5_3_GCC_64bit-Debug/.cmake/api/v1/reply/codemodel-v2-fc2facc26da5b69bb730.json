{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 9, 10, 11, 12], "hasInstallRule": true, "jsonFile": "directory-.-Debug-3779eb7ccf31bade44f7.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45]}, {"build": "libgoal", "jsonFile": "directory-libgoal-Debug-9d0ff3bccd0d8d1a246d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "libgoal"}, {"build": "libgoal-ndi", "childIndexes": [3, 4, 5, 8], "jsonFile": "directory-libgoal-ndi-Debug-34859441f47e3f43c708.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 2, "source": "libgoal/libgoal-ndi", "targetIndexes": [13, 14, 15]}, {"build": "libgoal-videoframe", "jsonFile": "directory-libgoal-videoframe-Debug-1309eb80b927c2445157.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 3, "source": "libgoal/libgoal-videoframe", "targetIndexes": [25, 26, 27]}, {"build": "libgoal-utils", "jsonFile": "directory-libgoal-utils-Debug-07b608341c359a3a3d67.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 4, "source": "libgoal/libgoal-utils", "targetIndexes": [22, 23, 24]}, {"build": "libgoal-ndi/NDIStream_send_test_app", "childIndexes": [6, 7], "jsonFile": "directory-libgoal-ndi.NDIStream_send_test_app-Debug-85e6ec79151aafa8b63a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 5, "source": "libgoal/libgoal-ndi/NDIStream_send_test_app", "targetIndexes": [31, 32, 33]}, {"build": "libgoal-libav", "jsonFile": "directory-libgoal-libav-Debug-52662cca0608a1863458.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 5, "projectIndex": 6, "source": "libgoal/libgoal-libav", "targetIndexes": [10, 11, 12]}, {"build": "libgoal-smemory-video", "jsonFile": "directory-libgoal-smemory-video-Debug-e223693788a3ecf2c7ac.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 5, "projectIndex": 7, "source": "libgoal/libgoal-smemory-video", "targetIndexes": [19, 20, 21]}, {"build": "libgoal-ndi/recv_test_app", "jsonFile": "directory-libgoal-ndi.recv_test_app-Debug-ad708c96f26ff926da08.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 8, "source": "libgoal/libgoal-ndi/recv_test_app", "targetIndexes": [46, 47, 48]}, {"build": "libgoal-sdi", "jsonFile": "directory-libgoal-sdi-Debug-5118cf0663713067f8ae.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 9, "source": "libgoal/libgoal-sdi", "targetIndexes": [16, 17, 18]}, {"build": "libgoal-devices", "jsonFile": "directory-libgoal-devices-Debug-014f5eff0ea2a37927db.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 10, "source": "libgoal/libgoal-devices", "targetIndexes": [7, 8, 9]}, {"build": "libgoal-avrouter", "jsonFile": "directory-libgoal-avrouter-Debug-2050b8a657a7bedb4072.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 11, "source": "libgoal/libgoal-avrouter", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}, {"build": "libgoal-widgets", "jsonFile": "directory-libgoal-widgets-Debug-3ff3cb590b0cfa5796e7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 12, "source": "libgoal/libgoal-widgets", "targetIndexes": [28, 29, 30]}], "name": "Debug", "projects": [{"childIndexes": [1, 2, 9, 10, 11, 12], "directoryIndexes": [0], "name": "av-routing-and-mixing", "targetIndexes": [34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45]}, {"directoryIndexes": [1], "name": "LibGoal", "parentIndex": 0}, {"childIndexes": [3, 4, 5, 8], "directoryIndexes": [2], "name": "GSS_libgoal_ndi", "parentIndex": 0, "targetIndexes": [13, 14, 15]}, {"directoryIndexes": [3], "name": "GSS_libgoal_videoframe", "parentIndex": 2, "targetIndexes": [25, 26, 27]}, {"directoryIndexes": [4], "name": "GSS_libgoal_utils", "parentIndex": 2, "targetIndexes": [22, 23, 24]}, {"childIndexes": [6, 7], "directoryIndexes": [5], "name": "NDIStream_send_test_app", "parentIndex": 2, "targetIndexes": [31, 32, 33]}, {"directoryIndexes": [6], "name": "GSS_libgoal_libav", "parentIndex": 5, "targetIndexes": [10, 11, 12]}, {"directoryIndexes": [7], "name": "GSS_libgoal_smemory-video", "parentIndex": 5, "targetIndexes": [19, 20, 21]}, {"directoryIndexes": [8], "name": "recv_test_app", "parentIndex": 2, "targetIndexes": [46, 47, 48]}, {"directoryIndexes": [9], "name": "GSS_libgoal_sdi", "parentIndex": 0, "targetIndexes": [16, 17, 18]}, {"directoryIndexes": [10], "name": "GSS_libgoal_devices", "parentIndex": 0, "targetIndexes": [7, 8, 9]}, {"directoryIndexes": [11], "name": "GSS_libgoal_avrouter", "parentIndex": 0, "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}, {"directoryIndexes": [12], "name": "GSS_libgoal_widgets", "parentIndex": 0, "targetIndexes": [28, 29, 30]}], "targets": [{"directoryIndex": 11, "id": "GSS_libgoal_avrouter::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter-Debug-8f3ba8cabb0369c346c9.json", "name": "GSS_libgoal_avrouter", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_autogen::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_autogen-Debug-4c7de6265948ab244d32.json", "name": "GSS_libgoal_avrouter_autogen", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_autogen_timestamp_deps::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_autogen_timestamp_deps-Debug-491b7a87287d4d20915e.json", "name": "GSS_libgoal_avrouter_autogen_timestamp_deps", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_other_files::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_other_files-Debug-52bee4f9036f45c7cea3.json", "name": "GSS_libgoal_avrouter_other_files", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_resources_1::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_resources_1-Debug-2150662c5c335a8fcab3.json", "name": "GSS_libgoal_avrouter_resources_1", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_resources_1_autogen::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_resources_1_autogen-Debug-a71c345c2b9d8c671a40.json", "name": "GSS_libgoal_avrouter_resources_1_autogen", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_resources_1_autogen_timestamp_deps::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_resources_1_autogen_timestamp_deps-Debug-46270ab19be8a03fd81e.json", "name": "GSS_libgoal_avrouter_resources_1_autogen_timestamp_deps", "projectIndex": 11}, {"directoryIndex": 10, "id": "GSS_libgoal_devices::@a4acf01ffd64b3c25b5e", "jsonFile": "target-GSS_libgoal_devices-Debug-56871b37092700675363.json", "name": "GSS_libgoal_devices", "projectIndex": 10}, {"directoryIndex": 10, "id": "GSS_libgoal_devices_autogen::@a4acf01ffd64b3c25b5e", "jsonFile": "target-GSS_libgoal_devices_autogen-Debug-21c2325f04846e4ecd10.json", "name": "GSS_libgoal_devices_autogen", "projectIndex": 10}, {"directoryIndex": 10, "id": "GSS_libgoal_devices_autogen_timestamp_deps::@a4acf01ffd64b3c25b5e", "jsonFile": "target-GSS_libgoal_devices_autogen_timestamp_deps-Debug-e3c672bf91b08d1cf54a.json", "name": "GSS_libgoal_devices_autogen_timestamp_deps", "projectIndex": 10}, {"directoryIndex": 6, "id": "GSS_libgoal_libav::@d4925a3eb7b45a3a7728", "jsonFile": "target-GSS_libgoal_libav-Debug-32619fab0a9cf52e53b6.json", "name": "GSS_libgoal_libav", "projectIndex": 6}, {"directoryIndex": 6, "id": "GSS_libgoal_libav_autogen::@d4925a3eb7b45a3a7728", "jsonFile": "target-GSS_libgoal_libav_autogen-Debug-28a9d90dc5ebca80df6f.json", "name": "GSS_libgoal_libav_autogen", "projectIndex": 6}, {"directoryIndex": 6, "id": "GSS_libgoal_libav_autogen_timestamp_deps::@d4925a3eb7b45a3a7728", "jsonFile": "target-GSS_libgoal_libav_autogen_timestamp_deps-Debug-1843064a210a99a33d17.json", "name": "GSS_libgoal_libav_autogen_timestamp_deps", "projectIndex": 6}, {"directoryIndex": 2, "id": "GSS_libgoal_ndi::@e8405e6f8fa3070b957e", "jsonFile": "target-GSS_libgoal_ndi-Debug-ca77f74a6fc003bf25c3.json", "name": "GSS_libgoal_ndi", "projectIndex": 2}, {"directoryIndex": 2, "id": "GSS_libgoal_ndi_autogen::@e8405e6f8fa3070b957e", "jsonFile": "target-GSS_libgoal_ndi_autogen-Debug-186af6464fe800543eb6.json", "name": "GSS_libgoal_ndi_autogen", "projectIndex": 2}, {"directoryIndex": 2, "id": "GSS_libgoal_ndi_autogen_timestamp_deps::@e8405e6f8fa3070b957e", "jsonFile": "target-GSS_libgoal_ndi_autogen_timestamp_deps-Debug-f51db8fb12849a05dfd4.json", "name": "GSS_libgoal_ndi_autogen_timestamp_deps", "projectIndex": 2}, {"directoryIndex": 9, "id": "GSS_libgoal_sdi::@83de4cecf005b7ef38bf", "jsonFile": "target-GSS_libgoal_sdi-Debug-0d50d5a48d285a871c0c.json", "name": "GSS_libgoal_sdi", "projectIndex": 9}, {"directoryIndex": 9, "id": "GSS_libgoal_sdi_autogen::@83de4cecf005b7ef38bf", "jsonFile": "target-GSS_libgoal_sdi_autogen-Debug-65d9d48255b434774030.json", "name": "GSS_libgoal_sdi_autogen", "projectIndex": 9}, {"directoryIndex": 9, "id": "GSS_libgoal_sdi_autogen_timestamp_deps::@83de4cecf005b7ef38bf", "jsonFile": "target-GSS_libgoal_sdi_autogen_timestamp_deps-Debug-24bf94ecbf535a9616d0.json", "name": "GSS_libgoal_sdi_autogen_timestamp_deps", "projectIndex": 9}, {"directoryIndex": 7, "id": "GSS_libgoal_smemory-video::@ce80f594daf5fb6bfe42", "jsonFile": "target-GSS_libgoal_smemory-video-Debug-d4caad5ef45b674e75ee.json", "name": "GSS_libgoal_smemory-video", "projectIndex": 7}, {"directoryIndex": 7, "id": "GSS_libgoal_smemory-video_autogen::@ce80f594daf5fb6bfe42", "jsonFile": "target-GSS_libgoal_smemory-video_autogen-Debug-22783496a99ec89ff3f6.json", "name": "GSS_libgoal_smemory-video_autogen", "projectIndex": 7}, {"directoryIndex": 7, "id": "GSS_libgoal_smemory-video_autogen_timestamp_deps::@ce80f594daf5fb6bfe42", "jsonFile": "target-GSS_libgoal_smemory-video_autogen_timestamp_deps-Debug-bd0f2538178cb2efab50.json", "name": "GSS_libgoal_smemory-video_autogen_timestamp_deps", "projectIndex": 7}, {"directoryIndex": 4, "id": "GSS_libgoal_utils::@6c8cb491c3014fb511f8", "jsonFile": "target-GSS_libgoal_utils-Debug-4ca266f667c85e2a3186.json", "name": "GSS_libgoal_utils", "projectIndex": 4}, {"directoryIndex": 4, "id": "GSS_libgoal_utils_autogen::@6c8cb491c3014fb511f8", "jsonFile": "target-GSS_libgoal_utils_autogen-Debug-b7a0366b24810a386cf3.json", "name": "GSS_libgoal_utils_autogen", "projectIndex": 4}, {"directoryIndex": 4, "id": "GSS_libgoal_utils_autogen_timestamp_deps::@6c8cb491c3014fb511f8", "jsonFile": "target-GSS_libgoal_utils_autogen_timestamp_deps-Debug-9190ceb52d72cabb940c.json", "name": "GSS_libgoal_utils_autogen_timestamp_deps", "projectIndex": 4}, {"directoryIndex": 3, "id": "GSS_libgoal_videoframe::@2bc16d3bd896b78e6cbd", "jsonFile": "target-GSS_libgoal_videoframe-Debug-2698f5a5dd4401d8aacb.json", "name": "GSS_libgoal_videoframe", "projectIndex": 3}, {"directoryIndex": 3, "id": "GSS_libgoal_videoframe_autogen::@2bc16d3bd896b78e6cbd", "jsonFile": "target-GSS_libgoal_videoframe_autogen-Debug-76f1d2cd7739ab4b5c18.json", "name": "GSS_libgoal_videoframe_autogen", "projectIndex": 3}, {"directoryIndex": 3, "id": "GSS_libgoal_videoframe_autogen_timestamp_deps::@2bc16d3bd896b78e6cbd", "jsonFile": "target-GSS_libgoal_videoframe_autogen_timestamp_deps-Debug-61ecd51c8263dd5b45f5.json", "name": "GSS_libgoal_videoframe_autogen_timestamp_deps", "projectIndex": 3}, {"directoryIndex": 12, "id": "GSS_libgoal_widgets::@1a6ca22365c285a707f8", "jsonFile": "target-GSS_libgoal_widgets-Debug-1e14e0e631e6a62d2ff6.json", "name": "GSS_libgoal_widgets", "projectIndex": 12}, {"directoryIndex": 12, "id": "GSS_libgoal_widgets_autogen::@1a6ca22365c285a707f8", "jsonFile": "target-GSS_libgoal_widgets_autogen-Debug-a2aa78d2eb35378e746d.json", "name": "GSS_libgoal_widgets_autogen", "projectIndex": 12}, {"directoryIndex": 12, "id": "GSS_libgoal_widgets_autogen_timestamp_deps::@1a6ca22365c285a707f8", "jsonFile": "target-GSS_libgoal_widgets_autogen_timestamp_deps-Debug-70ab241fe32d54dcbbf9.json", "name": "GSS_libgoal_widgets_autogen_timestamp_deps", "projectIndex": 12}, {"directoryIndex": 5, "id": "NDIStream_send_test_app::@6a6cc8600da1c3617a31", "jsonFile": "target-NDIStream_send_test_app-Debug-401f1d8fa38d2afc93f6.json", "name": "NDIStream_send_test_app", "projectIndex": 5}, {"directoryIndex": 5, "id": "NDIStream_send_test_app_autogen::@6a6cc8600da1c3617a31", "jsonFile": "target-NDIStream_send_test_app_autogen-Debug-85095afc94cf9e9f53db.json", "name": "NDIStream_send_test_app_autogen", "projectIndex": 5}, {"directoryIndex": 5, "id": "NDIStream_send_test_app_autogen_timestamp_deps::@6a6cc8600da1c3617a31", "jsonFile": "target-NDIStream_send_test_app_autogen_timestamp_deps-Debug-6321c5109010eb5b194e.json", "name": "NDIStream_send_test_app_autogen_timestamp_deps", "projectIndex": 5}, {"directoryIndex": 0, "id": "all_qmllint::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmllint-Debug-9d7bd1576452a4e4dd09.json", "name": "all_qmllint", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmllint_json::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmllint_json-Debug-491015ca80e578dd5165.json", "name": "all_qmllint_json", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmllint_module::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmllint_module-Debug-3af4747aef417007f0e9.json", "name": "all_qmllint_module", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmltyperegistrations::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmltyperegistrations-Debug-552e4ba855b132b7c58e.json", "name": "all_qmltyperegistrations", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing-Debug-0b0155d343c6af76daa0.json", "name": "av-routing-and-mixing", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_autogen-Debug-c68dff9be7f8e2de67aa.json", "name": "av-routing-and-mixing_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_autogen_timestamp_deps-Debug-2ce5a092f0131edd5a4f.json", "name": "av-routing-and-mixing_autogen_timestamp_deps", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmlimportscan::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmlimportscan-Debug-287c820b30bc7627813b.json", "name": "av-routing-and-mixing_qmlimportscan", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmllint::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmllint-Debug-f59cda9917efc555d529.json", "name": "av-routing-and-mixing_qmllint", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmllint_json::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmllint_json-Debug-586f5958148944bf451b.json", "name": "av-routing-and-mixing_qmllint_json", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmllint_module::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmllint_module-Debug-97d7cc6c80e2dbb7ea05.json", "name": "av-routing-and-mixing_qmllint_module", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmltyperegistration::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmltyperegistration-Debug-cd138c1be226d8076543.json", "name": "av-routing-and-mixing_qmltyperegistration", "projectIndex": 0}, {"directoryIndex": 8, "id": "recv_test_app::@899e4ac02e105eff13be", "jsonFile": "target-recv_test_app-Debug-6bc1c6a2a92e3987e415.json", "name": "recv_test_app", "projectIndex": 8}, {"directoryIndex": 8, "id": "recv_test_app_autogen::@899e4ac02e105eff13be", "jsonFile": "target-recv_test_app_autogen-Debug-7ea38977d1311acbd793.json", "name": "recv_test_app_autogen", "projectIndex": 8}, {"directoryIndex": 8, "id": "recv_test_app_autogen_timestamp_deps::@899e4ac02e105eff13be", "jsonFile": "target-recv_test_app_autogen_timestamp_deps-Debug-8835ee8bd6672369e62b.json", "name": "recv_test_app_autogen_timestamp_deps", "projectIndex": 8}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug", "source": "/home/<USER>/PROJECTS/av-routing-and-mixing"}, "version": {"major": 2, "minor": 6}}