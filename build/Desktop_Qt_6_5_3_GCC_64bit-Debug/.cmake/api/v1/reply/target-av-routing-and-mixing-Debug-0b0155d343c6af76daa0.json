{"artifacts": [{"path": "av-routing-and-mixing"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "install", "target_link_libraries", "__qt_internal_propagate_object_files", "__qt_internal_propagate_object_library", "__qt_propagate_generated_resource", "_qt_internal_process_resource", "qt6_add_resources", "qt_add_resources", "set_target_properties", "include", "find_package", "qt6_add_qml_module", "find_dependency", "_qt_internal_find_qt_dependencies", "set_property", "_pkg_create_imp_target", "_pkg_recalculate", "_pkg_check_modules_internal", "pkg_check_modules", "_qt_internal_find_third_party_dependencies", "add_dependencies", "_qt_internal_scan_qml_imports", "_qt_internal_generate_deploy_qml_imports_script", "cmake_language", "_qt_internal_finalize_executable", "qt6_finalize_target", "target_include_directories", "_qt_internal_qml_type_registration", "target_sources", "qt6_extract_metatypes", "_qt_internal_expose_deferred_files_to_ide"], "files": ["/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "libgoal/libgoal-avrouter/CMakeLists.txt", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Quick/Qt6QuickTargets.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Quick/Qt6QuickConfig.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6/Qt6Config.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLVersionlessTargets.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6OpenGL/Qt6OpenGLConfig.cmake", "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Quick/Qt6QuickDependencies.cmake", "libgoal/libgoal-ndi/CMakeLists.txt", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "/usr/share/cmake-3.28/Modules/FindPkgConfig.cmake", "libgoal/libgoal-libav/CMakeLists.txt", "libgoal/libgoal-utils/CMakeLists.txt", "libgoal/libgoal-videoframe/CMakeLists.txt", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Network/Qt6NetworkVersionlessTargets.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Qml/Qt6QmlDependencies.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Qml/Qt6QmlConfig.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "/usr/share/cmake-3.28/Modules/FindOpenGL.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6/FindWrapOpenGL.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Gui/Qt6GuiDependencies.cmake", "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake:679:EVAL"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 29, "parent": 0}, {"command": 2, "file": 0, "line": 741, "parent": 1}, {"command": 1, "file": 0, "line": 549, "parent": 2}, {"command": 0, "file": 0, "line": 588, "parent": 3}, {"command": 4, "file": 1, "line": 95, "parent": 0}, {"file": 2}, {"command": 11, "file": 2, "line": 36, "parent": 6}, {"command": 10, "file": 0, "line": 366, "parent": 7}, {"command": 9, "file": 0, "line": 314, "parent": 8}, {"command": 8, "file": 0, "line": 2055, "parent": 9}, {"command": 7, "file": 0, "line": 1701, "parent": 10}, {"command": 6, "file": 0, "line": 1646, "parent": 11}, {"command": 5, "file": 0, "line": 1594, "parent": 12}, {"command": 5, "file": 1, "line": 74, "parent": 0}, {"command": 14, "file": 1, "line": 15, "parent": 0}, {"file": 5, "parent": 15}, {"command": 14, "file": 5, "line": 157, "parent": 16}, {"file": 4, "parent": 17}, {"command": 13, "file": 4, "line": 52, "parent": 18}, {"file": 3, "parent": 19}, {"command": 12, "file": 3, "line": 61, "parent": 20}, {"command": 15, "file": 1, "line": 58, "parent": 0}, {"command": 5, "file": 6, "line": 369, "parent": 22}, {"command": 13, "file": 4, "line": 40, "parent": 18}, {"file": 11, "parent": 24}, {"command": 17, "file": 11, "line": 39, "parent": 25}, {"command": 16, "file": 10, "line": 111, "parent": 26}, {"command": 14, "file": 9, "line": 76, "parent": 27}, {"file": 8, "parent": 28}, {"command": 13, "file": 8, "line": 55, "parent": 29}, {"file": 7, "parent": 30}, {"command": 12, "file": 7, "line": 61, "parent": 31}, {"file": 12}, {"command": 5, "file": 12, "line": 41, "parent": 33}, {"command": 14, "file": 5, "line": 157, "parent": 16}, {"file": 16, "parent": 35}, {"command": 13, "file": 16, "line": 40, "parent": 36}, {"file": 15, "parent": 37}, {"command": 17, "file": 15, "line": 39, "parent": 38}, {"command": 16, "file": 10, "line": 111, "parent": 39}, {"command": 14, "file": 9, "line": 76, "parent": 40}, {"file": 14, "parent": 41}, {"command": 13, "file": 14, "line": 52, "parent": 42}, {"file": 13, "parent": 43}, {"command": 12, "file": 13, "line": 93, "parent": 44}, {"file": 18}, {"command": 22, "file": 18, "line": 91, "parent": 46}, {"command": 21, "file": 17, "line": 841, "parent": 47}, {"command": 20, "file": 17, "line": 669, "parent": 48}, {"command": 19, "file": 17, "line": 339, "parent": 49}, {"command": 18, "file": 17, "line": 320, "parent": 50}, {"command": 22, "file": 18, "line": 92, "parent": 46}, {"command": 21, "file": 17, "line": 841, "parent": 52}, {"command": 20, "file": 17, "line": 669, "parent": 53}, {"command": 19, "file": 17, "line": 339, "parent": 54}, {"command": 18, "file": 17, "line": 320, "parent": 55}, {"command": 22, "file": 18, "line": 93, "parent": 46}, {"command": 21, "file": 17, "line": 841, "parent": 57}, {"command": 20, "file": 17, "line": 669, "parent": 58}, {"command": 19, "file": 17, "line": 339, "parent": 59}, {"command": 18, "file": 17, "line": 320, "parent": 60}, {"command": 22, "file": 18, "line": 94, "parent": 46}, {"command": 21, "file": 17, "line": 841, "parent": 62}, {"command": 20, "file": 17, "line": 669, "parent": 63}, {"command": 19, "file": 17, "line": 339, "parent": 64}, {"command": 18, "file": 17, "line": 320, "parent": 65}, {"command": 22, "file": 18, "line": 95, "parent": 46}, {"command": 21, "file": 17, "line": 841, "parent": 67}, {"command": 20, "file": 17, "line": 669, "parent": 68}, {"command": 19, "file": 17, "line": 339, "parent": 69}, {"command": 18, "file": 17, "line": 320, "parent": 70}, {"command": 22, "file": 18, "line": 96, "parent": 46}, {"command": 21, "file": 17, "line": 841, "parent": 72}, {"command": 20, "file": 17, "line": 669, "parent": 73}, {"command": 19, "file": 17, "line": 339, "parent": 74}, {"command": 18, "file": 17, "line": 320, "parent": 75}, {"file": 19}, {"command": 5, "file": 19, "line": 51, "parent": 77}, {"file": 20}, {"command": 5, "file": 20, "line": 54, "parent": 79}, {"command": 16, "file": 10, "line": 111, "parent": 26}, {"command": 14, "file": 9, "line": 76, "parent": 81}, {"file": 24, "parent": 82}, {"command": 13, "file": 24, "line": 41, "parent": 83}, {"file": 23, "parent": 84}, {"command": 17, "file": 23, "line": 39, "parent": 85}, {"command": 16, "file": 10, "line": 111, "parent": 86}, {"command": 14, "file": 9, "line": 76, "parent": 87}, {"file": 22, "parent": 88}, {"command": 13, "file": 22, "line": 55, "parent": 89}, {"file": 21, "parent": 90}, {"command": 12, "file": 21, "line": 61, "parent": 91}, {"command": 13, "file": 16, "line": 52, "parent": 36}, {"file": 25, "parent": 93}, {"command": 12, "file": 25, "line": 61, "parent": 94}, {"command": 13, "file": 14, "line": 40, "parent": 42}, {"file": 28, "parent": 96}, {"command": 23, "file": 28, "line": 30, "parent": 97}, {"command": 16, "file": 10, "line": 36, "parent": 98}, {"command": 14, "file": 9, "line": 76, "parent": 99}, {"file": 27, "parent": 100}, {"command": 14, "file": 27, "line": 13, "parent": 101}, {"file": 26, "parent": 102}, {"command": 18, "file": 26, "line": 673, "parent": 103}, {"command": 12, "file": 26, "line": 671, "parent": 103}, {"command": 5, "file": 0, "line": 550, "parent": 2}, {"command": 5, "file": 2, "line": 56, "parent": 6}, {"file": 1, "line": -1, "parent": 0}, {"command": 29, "file": 29, "line": 1, "parent": 108}, {"command": 28, "file": 0, "line": 709, "parent": 109}, {"command": 27, "file": 0, "line": 636, "parent": 110}, {"command": 26, "file": 0, "line": 636, "parent": 111}, {"command": 25, "file": 6, "line": 2965, "parent": 112}, {"command": 24, "file": 6, "line": 2817, "parent": 113}, {"command": 31, "file": 6, "line": 517, "parent": 22}, {"command": 30, "file": 6, "line": 2620, "parent": 115}, {"command": 11, "file": 1, "line": 51, "parent": 0}, {"command": 10, "file": 0, "line": 366, "parent": 117}, {"command": 9, "file": 0, "line": 314, "parent": 118}, {"command": 8, "file": 0, "line": 2055, "parent": 119}, {"command": 32, "file": 0, "line": 1708, "parent": 120}, {"command": 33, "file": 6, "line": 2425, "parent": 115}, {"command": 32, "file": 0, "line": 1136, "parent": 122}, {"command": 32, "file": 6, "line": 2586, "parent": 115}, {"command": 10, "file": 6, "line": 561, "parent": 22}, {"command": 9, "file": 0, "line": 314, "parent": 125}, {"command": 8, "file": 0, "line": 2055, "parent": 126}, {"command": 32, "file": 0, "line": 1708, "parent": 127}, {"command": 34, "file": 0, "line": 704, "parent": 109}, {"command": 32, "file": 0, "line": 1792, "parent": 129}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17"}, {"backtrace": 106, "fragment": "-fPIC"}], "defines": [{"backtrace": 14, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 106, "define": "QT_CORE_LIB"}, {"backtrace": 14, "define": "QT_GUI_LIB"}, {"backtrace": 14, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 23, "define": "QT_NETWORK_LIB"}, {"backtrace": 14, "define": "QT_OPENGL_LIB"}, {"backtrace": 23, "define": "QT_QMLINTEGRATION_LIB"}, {"backtrace": 14, "define": "QT_QMLMODELS_LIB"}, {"backtrace": 23, "define": "QT_QML_LIB"}, {"backtrace": 14, "define": "QT_QUICK_LIB"}, {"backtrace": 14, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug"}, {"path": "/home/<USER>/PROJECTS/av-routing-and-mixing"}, {"backtrace": 0, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/av-routing-and-mixing_autogen/include"}, {"backtrace": 116, "path": "/opt/Qt/6.5.3/gcc_64/include/QtQml/6.5.3"}, {"backtrace": 116, "path": "/opt/Qt/6.5.3/gcc_64/include/QtQml/6.5.3/QtQml"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets/include"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter"}, {"backtrace": 14, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include"}, {"backtrace": 116, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtCore/6.5.3"}, {"backtrace": 116, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtCore/6.5.3/QtCore"}, {"backtrace": 116, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtCore"}, {"backtrace": 116, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include"}, {"backtrace": 116, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/mkspecs/linux-g++"}, {"backtrace": 116, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtQml"}, {"backtrace": 116, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtQmlIntegration"}, {"backtrace": 116, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtNetwork"}, {"backtrace": 14, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtWidgets"}, {"backtrace": 14, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtGui"}, {"backtrace": 14, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtQuick"}, {"backtrace": 14, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtQmlModels"}, {"backtrace": 14, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtOpenGL"}, {"backtrace": 14, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtMultimedia/6.5.3"}, {"backtrace": 14, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtMultimedia/6.5.3/QtMultimedia"}, {"backtrace": 14, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtGui/6.5.3"}, {"backtrace": 14, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtGui/6.5.3/QtGui"}, {"backtrace": 14, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtMultimedia"}, {"backtrace": 14, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include/QtConcurrent"}], "language": "CXX", "languageStandard": {"backtraces": [106, 106], "standard": "17"}, "sourceIndexes": [0, 1, 2, 5, 7, 8]}], "dependencies": [{"backtrace": 114, "id": "av-routing-and-mixing_qmlimportscan::@6890427a1f51a3e7e1df"}, {"backtrace": 14, "id": "GSS_libgoal_ndi::@e8405e6f8fa3070b957e"}, {"backtrace": 14, "id": "GSS_libgoal_videoframe::@2bc16d3bd896b78e6cbd"}, {"backtrace": 14, "id": "GSS_libgoal_utils::@6c8cb491c3014fb511f8"}, {"backtrace": 14, "id": "GSS_libgoal_smemory-video::@ce80f594daf5fb6bfe42"}, {"backtrace": 14, "id": "GSS_libgoal_sdi::@83de4cecf005b7ef38bf"}, {"backtrace": 14, "id": "GSS_libgoal_avrouter::@610245c7e5910b70db46"}, {"backtrace": 14, "id": "GSS_libgoal_avrouter_resources_1::@610245c7e5910b70db46"}, {"backtrace": 14, "id": "GSS_libgoal_widgets::@1a6ca22365c285a707f8"}, {"id": "av-routing-and-mixing_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "av-routing-and-mixing_autogen::@6890427a1f51a3e7e1df"}], "id": "av-routing-and-mixing::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 5, "path": "bin"}], "prefix": {"path": "/tmp"}}, "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG -g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/Qt/6.5.3/gcc_64/lib:", "role": "libraries"}, {"backtrace": 13, "fragment": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir/./.rcc/qrc_GSS_libgoal_avrouter.cpp.o", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/Qt/6.5.3/gcc_64/lib/libQt6Quick.so.6.5.3", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-videoframe/libGSS_libgoal_videoframe.a", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-widgets/libGSS_libgoal_widgets.a", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-utils/libGSS_libgoal_utils.a", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-libav/libGSS_libgoal_libav.a", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-ndi/libGSS_libgoal_ndi.a", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-sdi/libGSS_libgoal_sdi.a", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-smemory-video/libGSS_libgoal_smemory-video.a", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-avrouter/libGSS_libgoal_avrouter.a", "role": "libraries"}, {"backtrace": 21, "fragment": "/opt/Qt/6.5.3/gcc_64/lib/libQt6QmlModels.so.6.5.3", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/Qt/6.5.3/gcc_64/lib/libQt6Qml.so.6.5.3", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/Qt/6.5.3/gcc_64/lib/libQt6OpenGL.so.6.5.3", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-ndi/libGSS_libgoal_ndi.a", "role": "libraries"}, {"backtrace": 34, "fragment": "-lndi", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-sdi/libGSS_libgoal_sdi.a", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-smemory-video/libGSS_libgoal_smemory-video.a", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-videoframe/libGSS_libgoal_videoframe.a", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-libav/libGSS_libgoal_libav.a", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-videoframe/libGSS_libgoal_videoframe.a", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-libav/libGSS_libgoal_libav.a", "role": "libraries"}, {"backtrace": 45, "fragment": "/usr/lib/x86_64-linux-gnu/libxkbcommon.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/Qt/6.5.3/gcc_64/lib/libQt6Widgets.so.6.5.3", "role": "libraries"}, {"backtrace": 51, "fragment": "/usr/lib/x86_64-linux-gnu/libavformat.so", "role": "libraries"}, {"backtrace": 56, "fragment": "/usr/lib/x86_64-linux-gnu/libavfilter.so", "role": "libraries"}, {"backtrace": 61, "fragment": "/usr/lib/x86_64-linux-gnu/libswscale.so", "role": "libraries"}, {"backtrace": 66, "fragment": "/usr/lib/x86_64-linux-gnu/libswresample.so", "role": "libraries"}, {"backtrace": 71, "fragment": "/usr/lib/x86_64-linux-gnu/libavcodec.so", "role": "libraries"}, {"backtrace": 76, "fragment": "/usr/lib/x86_64-linux-gnu/libavutil.so", "role": "libraries"}, {"backtrace": 14, "fragment": "libgoal-utils/libGSS_libgoal_utils.a", "role": "libraries"}, {"backtrace": 78, "fragment": "/opt/Qt/6.5.3/gcc_64/lib/libQt6Concurrent.so.6.5.3", "role": "libraries"}, {"backtrace": 80, "fragment": "/opt/Qt/6.5.3/gcc_64/lib/libQt6Multimedia.so.6.5.3", "role": "libraries"}, {"backtrace": 92, "fragment": "/opt/Qt/6.5.3/gcc_64/lib/libQt6Network.so.6.5.3", "role": "libraries"}, {"backtrace": 95, "fragment": "/opt/Qt/6.5.3/gcc_64/lib/libQt6Gui.so.6.5.3", "role": "libraries"}, {"backtrace": 104, "fragment": "/usr/lib/x86_64-linux-gnu/libGLX.so", "role": "libraries"}, {"backtrace": 105, "fragment": "/usr/lib/x86_64-linux-gnu/libOpenGL.so", "role": "libraries"}, {"backtrace": 106, "fragment": "/opt/Qt/6.5.3/gcc_64/lib/libQt6Core.so.6.5.3", "role": "libraries"}, {"backtrace": 107, "fragment": "/usr/lib/x86_64-linux-gnu/libOpenCL.so", "role": "libraries"}], "language": "CXX"}, "name": "av-routing-and-mixing", "nameOnDisk": "av-routing-and-mixing", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 5, 7, 8]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3, 11]}, {"name": "Forms", "sourceIndexes": [4]}, {"name": "", "sourceIndexes": [6, 9, 10, 12, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28]}, {"name": "CMake Rules", "sourceIndexes": [13, 14, 15, 16, 17, 18]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/av-routing-and-mixing_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "mainwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "mainwindow.ui", "sourceGroupIndex": 2}, {"backtrace": 121, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/.rcc/qrc_av-routing-and-mixing.cpp", "sourceGroupIndex": 0}, {"backtrace": 123, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/meta_types/qt6av-routing-and-mixing_debug_metatypes.json.gen", "sourceGroupIndex": 3}, {"backtrace": 124, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/av-routing-and-mixing_qmltyperegistrations.cpp", "sourceGroupIndex": 0}, {"backtrace": 128, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/.rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp", "sourceGroupIndex": 0}, {"backtrace": 130, "path": "quickviewform.qml", "sourceGroupIndex": 3}, {"backtrace": 130, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/com/teslan/av-routing-and-mixing/qmldir", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/av-routing-and-mixing_autogen/include/ui_mainwindow.h", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/av-routing-and-mixing_autogen/timestamp", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/.rcc/qrc_av-routing-and-mixing.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/meta_types/qt6av-routing-and-mixing_debug_metatypes.json.gen.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/av-routing-and-mixing_qmltyperegistrations.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/.rcc/qrc_qmake_com_teslan_av-routing-and-mixing.cpp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/av-routing-and-mixing_autogen/timestamp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/meta_types/av-routing-and-mixing_json_file_list.txt.rule", "sourceGroupIndex": 4}, {"backtrace": 106, "path": "/opt/Qt/6.5.3/gcc_64/metatypes/qt6core_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 23, "path": "/opt/Qt/6.5.3/gcc_64/metatypes/qt6qml_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 23, "path": "/opt/Qt/6.5.3/gcc_64/metatypes/qt6network_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 14, "path": "/opt/Qt/6.5.3/gcc_64/metatypes/qt6widgets_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 14, "path": "/opt/Qt/6.5.3/gcc_64/metatypes/qt6gui_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 14, "path": "/opt/Qt/6.5.3/gcc_64/metatypes/qt6quick_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 14, "path": "/opt/Qt/6.5.3/gcc_64/metatypes/qt6qmlmodels_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 14, "path": "/opt/Qt/6.5.3/gcc_64/metatypes/qt6opengl_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 14, "path": "/opt/Qt/6.5.3/gcc_64/metatypes/qt6multimedia_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}, {"backtrace": 14, "path": "/opt/Qt/6.5.3/gcc_64/metatypes/qt6concurrent_relwithdebinfo_metatypes.json", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}