{"artifacts": [{"path": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir/./GSS_libgoal_avrouter_resources_1_autogen/mocs_compilation.cpp.o"}, {"path": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir/./.rcc/qrc_GSS_libgoal_avrouter.cpp.o"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "__qt_propagate_generated_resource", "_qt_internal_process_resource", "qt6_add_resources", "qt_add_resources", "set_property", "_qt_internal_copy_dependency_properties", "__qt_internal_propagate_object_library", "target_compile_definitions"], "files": ["/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "libgoal/libgoal-avrouter/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 4, "file": 1, "line": 36, "parent": 0}, {"command": 3, "file": 0, "line": 366, "parent": 1}, {"command": 2, "file": 0, "line": 314, "parent": 2}, {"command": 1, "file": 0, "line": 2055, "parent": 3}, {"command": 0, "file": 0, "line": 1672, "parent": 4}, {"command": 7, "file": 0, "line": 1701, "parent": 4}, {"command": 6, "file": 0, "line": 1617, "parent": 6}, {"command": 5, "file": 0, "line": 2408, "parent": 7}, {"command": 5, "file": 0, "line": 2408, "parent": 7}, {"command": 8, "file": 0, "line": 1673, "parent": 4}, {"command": 5, "file": 0, "line": 2408, "parent": 7}, {"command": 5, "file": 0, "line": 2408, "parent": 7}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fdiagnostics-color=always"}, {"backtrace": 8, "fragment": "-fPIC"}], "defines": [{"backtrace": 9, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 10, "define": "QT_CORE_LIB"}, {"backtrace": 9, "define": "QT_GUI_LIB"}, {"backtrace": 9, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 9, "define": "QT_NETWORK_LIB"}, {"backtrace": 9, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-avrouter"}, {"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter"}, {"backtrace": 0, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-avrouter/GSS_libgoal_avrouter_resources_1_autogen/include"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include"}, {"backtrace": 11, "path": "/opt/Qt/6.5.3/gcc_64/include/QtCore"}, {"backtrace": 11, "path": "/opt/Qt/6.5.3/gcc_64/include/QtMultimedia"}, {"backtrace": 11, "path": "/opt/Qt/6.5.3/gcc_64/include/QtGui"}, {"backtrace": 11, "path": "/opt/Qt/6.5.3/gcc_64/include/QtNetwork"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include"}, {"backtrace": 11, "path": "/opt/Qt/6.5.3/gcc_64/include/QtConcurrent"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include"}, {"backtrace": 11, "path": "/opt/Qt/6.5.3/gcc_64/include/QtCore/6.5.3"}, {"backtrace": 11, "path": "/opt/Qt/6.5.3/gcc_64/include/QtCore/6.5.3/QtCore"}, {"backtrace": 11, "path": "/opt/Qt/6.5.3/gcc_64/include/QtMultimedia/6.5.3"}, {"backtrace": 11, "path": "/opt/Qt/6.5.3/gcc_64/include/QtMultimedia/6.5.3/QtMultimedia"}, {"backtrace": 11, "path": "/opt/Qt/6.5.3/gcc_64/include/QtGui/6.5.3"}, {"backtrace": 11, "path": "/opt/Qt/6.5.3/gcc_64/include/QtGui/6.5.3/QtGui"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include"}, {"backtrace": 11, "path": "/opt/Qt/6.5.3/gcc_64/include/QtWidgets"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video"}, {"backtrace": 11, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include"}, {"backtrace": 11, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/include"}, {"backtrace": 11, "isSystem": true, "path": "/opt/Qt/6.5.3/gcc_64/mkspecs/linux-g++"}], "language": "CXX", "languageStandard": {"backtraces": [12, 12], "standard": "17"}, "sourceIndexes": [0, 1]}], "dependencies": [{"id": "GSS_libgoal_avrouter_resources_1_autogen_timestamp_deps::@610245c7e5910b70db46"}, {"backtrace": 0, "id": "GSS_libgoal_avrouter_resources_1_autogen::@610245c7e5910b70db46"}], "id": "GSS_libgoal_avrouter_resources_1::@610245c7e5910b70db46", "name": "GSS_libgoal_avrouter_resources_1", "paths": {"build": "libgoal-avrouter", "source": "libgoal/libgoal-avrouter"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "", "sourceIndexes": [2]}, {"name": "CMake Rules", "sourceIndexes": [3, 4]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-avrouter/GSS_libgoal_avrouter_resources_1_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-avrouter/.rcc/qrc_GSS_libgoal_avrouter.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-avrouter/GSS_libgoal_avrouter_resources_1_autogen/timestamp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-avrouter/.rcc/qrc_GSS_libgoal_avrouter.cpp.rule", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-avrouter/GSS_libgoal_avrouter_resources_1_autogen/timestamp.rule", "sourceGroupIndex": 2}], "type": "OBJECT_LIBRARY"}