{"backtrace": 5, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_expose_source_file_to_ide", "_qt_internal_process_resource", "qt6_add_resources", "qt_add_resources"], "files": ["/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "libgoal/libgoal-avrouter/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 4, "file": 1, "line": 36, "parent": 0}, {"command": 3, "file": 0, "line": 366, "parent": 1}, {"command": 2, "file": 0, "line": 314, "parent": 2}, {"command": 1, "file": 0, "line": 1937, "parent": 3}, {"command": 0, "file": 0, "line": 1725, "parent": 4}]}, "id": "GSS_libgoal_avrouter_other_files::@610245c7e5910b70db46", "name": "GSS_libgoal_avrouter_other_files", "paths": {"build": "libgoal-avrouter", "source": "libgoal/libgoal-avrouter"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}], "sources": [{"backtrace": 5, "path": "libgoal/libgoal-avrouter/resources/kernels.cl", "sourceGroupIndex": 0}], "type": "UTILITY"}