{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["libgoal/libgoal-smemory-video/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"id": "GSS_libgoal_smemory-video_autogen_timestamp_deps::@ce80f594daf5fb6bfe42"}], "id": "GSS_libgoal_smemory-video_autogen::@ce80f594daf5fb6bfe42", "isGeneratorProvided": true, "name": "GSS_libgoal_smemory-video_autogen", "paths": {"build": "libgoal-smemory-video", "source": "libgoal/libgoal-smemory-video"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video_autogen.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-smemory-video/GSS_libgoal_smemory-video_autogen/timestamp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}