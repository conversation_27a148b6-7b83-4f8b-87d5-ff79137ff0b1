{"entries": [{"name": "CMAKE_ADDR2LINE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/addr2line"}, {"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ar"}, {"name": "CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "Debug"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "28"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_COLOR_DIAGNOSTICS", "properties": [{"name": "HELPSTRING", "value": "Enable colored diagnostics throughout."}], "type": "BOOL", "value": "1"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "/usr/bin/cmake"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "/usr/bin/cpack"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "/usr/bin/ctest"}, {"name": "CMAKE_CXX_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "CXX compiler"}], "type": "STRING", "value": "/usr/bin/g++"}, {"name": "CMAKE_CXX_COMPILER_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "/usr/bin/gcc-ar-11"}, {"name": "CMAKE_CXX_COMPILER_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"}], "type": "FILEPATH", "value": "/usr/bin/gcc-ranlib-11"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": "-DQT_QML_DEBUG"}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_CXX_FLAGS_INIT", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "-DQT_QML_DEBUG"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_CXX_OUTPUT_EXTENSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": ".o"}, {"name": "CMAKE_C_COMPILER", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "/usr/bin/gcc"}, {"name": "CMAKE_C_OUTPUT_EXTENSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": ""}, {"name": "CMAKE_DLLTOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_DLLTOOL-NOTFOUND"}, {"name": "CMAKE_EDIT_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cache edit program executable."}], "type": "INTERNAL", "value": "/usr/bin/cmake-gui"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "ELF"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable/Disable output of compile commands during generation."}], "type": "BOOL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "Ninja"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_LIBC_PTHREAD", "properties": [{"name": "HELPSTRING", "value": "Test CMAKE_HAVE_LIBC_PTHREAD"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_HAVE_PTHREAD_H", "properties": [{"name": "HELPSTRING", "value": "Have include pthread.h"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing"}, {"name": "CMAKE_INSTALL_BINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "User executables (bin)"}], "type": "PATH", "value": "bin"}, {"name": "CMAKE_INSTALL_DATADIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data (DATAROOTDIR)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_DATAROOTDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data root (share)"}], "type": "PATH", "value": "share"}, {"name": "CMAKE_INSTALL_DOCDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Documentation root (DATAROOTDIR/doc/PROJECT_NAME)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_INCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files (include)"}], "type": "PATH", "value": "include"}, {"name": "CMAKE_INSTALL_INFODIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Info documentation (DATAROOTDIR/info)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LIBDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Object code libraries (lib)"}], "type": "PATH", "value": "lib"}, {"name": "CMAKE_INSTALL_LIBEXECDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program executables (libexec)"}], "type": "PATH", "value": "libexec"}, {"name": "CMAKE_INSTALL_LOCALEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Locale-dependent data (DATAROOTDIR/locale)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LOCALSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable single-machine data (var)"}], "type": "PATH", "value": "var"}, {"name": "CMAKE_INSTALL_MANDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Man documentation (DATAROOTDIR/man)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_OLDINCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files for non-gcc (/usr/include)"}], "type": "PATH", "value": "/usr/include"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "/usr/local"}, {"name": "CMAKE_INSTALL_RUNSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Run-time variable data (LOCALSTATEDIR/run)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_SBINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "System admin executables (sbin)"}], "type": "PATH", "value": "sbin"}, {"name": "CMAKE_INSTALL_SHAREDSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable architecture-independent data (com)"}], "type": "PATH", "value": "com"}, {"name": "CMAKE_INSTALL_SO_NO_EXE", "properties": [{"name": "HELPSTRING", "value": "Install .so files without execute permission."}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_INSTALL_SYSCONFDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only single-machine data (etc)"}], "type": "PATH", "value": "etc"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ld"}, {"name": "CMAKE_MAKE_PROGRAM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program used to build from build.ninja files."}], "type": "FILEPATH", "value": "/usr/bin/ninja"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_NM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/nm"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "13"}, {"name": "CMAKE_OBJCOPY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/objcopy"}, {"name": "CMAKE_OBJDUMP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/objdump"}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PREFIX_PATH", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64"}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_INCLUDE_BEFORE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/.qtc/package-manager/auto-setup.cmake"}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "av-routing-and-mixing"}, {"name": "CMAKE_PROJECT_VERSION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "0.1"}, {"name": "CMAKE_PROJECT_VERSION_MAJOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "0"}, {"name": "CMAKE_PROJECT_VERSION_MINOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "1"}, {"name": "CMAKE_PROJECT_VERSION_PATCH", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_VERSION_TWEAK", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ranlib"}, {"name": "CMAKE_READELF", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/readelf"}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "/usr/share/cmake-3.28"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STRIP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/strip"}, {"name": "CMAKE_TAPI", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_TAPI-NOTFOUND"}, {"name": "CMAKE_UNAME", "properties": [{"name": "HELPSTRING", "value": "uname command"}], "type": "INTERNAL", "value": "/usr/bin/uname"}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "DEVICES_ENABLE_STREAMDECK", "properties": [{"name": "HELPSTRING", "value": "Enable Stream Deck native support"}], "type": "BOOL", "value": "OFF"}, {"name": "DEVICES_ENABLE_XKEYS", "properties": [{"name": "HELPSTRING", "value": "Enable XKeys support"}], "type": "BOOL", "value": "ON"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_GTest", "properties": [{"name": "HELPSTRING", "value": "Details about finding <PERSON><PERSON>"}], "type": "INTERNAL", "value": "[/usr/lib/x86_64-linux-gnu/cmake/GTest/GTestConfig.cmake][c ][v1.14.0()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenCL", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenCL"}], "type": "INTERNAL", "value": "[/usr/lib/x86_64-linux-gnu/libOpenCL.so][/usr/include][v3.0()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_OpenGL", "properties": [{"name": "HELPSTRING", "value": "Details about finding OpenGL"}], "type": "INTERNAL", "value": "[/usr/lib/x86_64-linux-gnu/libOpenGL.so][/usr/lib/x86_64-linux-gnu/libGLX.so][/usr/include][c ][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig", "properties": [{"name": "HELPSTRING", "value": "Details about finding PkgConfig"}], "type": "INTERNAL", "value": "[/usr/bin/pkg-config][v1.8.1()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Threads", "properties": [{"name": "HELPSTRING", "value": "Details about finding Threads"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_WrapAtomic", "properties": [{"name": "HELPSTRING", "value": "Details about finding <PERSON><PERSON><PERSON><PERSON><PERSON>"}], "type": "INTERNAL", "value": "[1][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_WrapOpenGL", "properties": [{"name": "HELPSTRING", "value": "Details about finding WrapOpenGL"}], "type": "INTERNAL", "value": "[ON][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_WrapVulkanHeaders", "properties": [{"name": "HELPSTRING", "value": "Details about finding WrapVulkanHeaders"}], "type": "INTERNAL", "value": "[/usr/include][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_XKB", "properties": [{"name": "HELPSTRING", "value": "Details about finding XKB"}], "type": "INTERNAL", "value": "[/usr/lib/x86_64-linux-gnu/libxkbcommon.so][/usr/include][v1.4.0(0.5.0)]"}, {"name": "GOALSPORT_USE_CUDA", "properties": [{"name": "HELPSTRING", "value": "Use CUDA capabilities of Videoframe"}], "type": "BOOL", "value": "OFF"}, {"name": "GOALSPORT_USE_EXPERIMENTAL_DECODER", "properties": [{"name": "HELPSTRING", "value": "Use network-based experimental decoder"}], "type": "BOOL", "value": "OFF"}, {"name": "GSS_libgoal_avrouter_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-avrouter"}, {"name": "GSS_libgoal_avrouter_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "GSS_libgoal_avrouter_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;Qt6::Core;general;Qt6::Multimedia;general;GSS::libgoal::utils;general;GSS::libgoal::videoframe;general;GSS::libgoal::ndi;general;GSS::libgoal::sdi;general;GSS::libgoal::smemory-video;general;OpenCL::OpenCL;"}, {"name": "GSS_libgoal_avrouter_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter"}, {"name": "GSS_libgoal_devices_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-devices"}, {"name": "GSS_libgoal_devices_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "GSS_libgoal_devices_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;-l<PERSON><PERSON>d;general;Qt6::Network;"}, {"name": "GSS_libgoal_devices_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-devices"}, {"name": "GSS_libgoal_libav_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-libav"}, {"name": "GSS_libgoal_libav_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "GSS_libgoal_libav_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;Qt6::Widgets;general;GSS::libgoal::utils;general;GSS::libgoal::videoframe;"}, {"name": "GSS_libgoal_libav_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav"}, {"name": "GSS_libgoal_match_manager_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;GSS::libgoal::libav;general;GSS::libgoal::utils;general;Qt6::WebSockets;general;Qt6::Xml;"}, {"name": "GSS_libgoal_ndi-embedded_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal/libgoal-ndi-embedded"}, {"name": "GSS_libgoal_ndi-embedded_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "GSS_libgoal_ndi-embedded_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;GSS::libgoal::videoframe;general;GSS::libgoal::utils;general;GSS::libgoal::ndi;general;GSS::libgoal::libav;"}, {"name": "GSS_libgoal_ndi-embedded_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi-embedded"}, {"name": "GSS_libgoal_ndi_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-ndi"}, {"name": "GSS_libgoal_ndi_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "GSS_libgoal_ndi_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;-lndi;general;GSS::libgoal::videoframe;general;GSS::libgoal::utils;"}, {"name": "GSS_libgoal_ndi_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi"}, {"name": "GSS_libgoal_remote_var_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal/libgoal-remote-var"}, {"name": "GSS_libgoal_remote_var_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "GSS_libgoal_remote_var_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-remote-var"}, {"name": "GSS_libgoal_sdi_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-sdi"}, {"name": "GSS_libgoal_sdi_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "GSS_libgoal_sdi_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;GSS::libgoal::libav;"}, {"name": "GSS_libgoal_sdi_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi"}, {"name": "GSS_libgoal_smemory-video_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-smemory-video"}, {"name": "GSS_libgoal_smemory-video_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "GSS_libgoal_smemory-video_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;GSS::libgoal::utils;general;GSS::libgoal::videoframe;"}, {"name": "GSS_libgoal_smemory-video_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video"}, {"name": "GSS_libgoal_utils_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-utils"}, {"name": "GSS_libgoal_utils_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "GSS_libgoal_utils_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;Qt6::Concurrent;"}, {"name": "GSS_libgoal_utils_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils"}, {"name": "GSS_libgoal_videoframe_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-videoframe"}, {"name": "GSS_libgoal_videoframe_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "GSS_libgoal_videoframe_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe"}, {"name": "GSS_libgoal_widgets_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-widgets"}, {"name": "GSS_libgoal_widgets_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "GSS_libgoal_widgets_LIB_DEPENDS", "properties": [{"name": "HELPSTRING", "value": "Dependencies for the target"}], "type": "STATIC", "value": "general;Qt6::Widgets;"}, {"name": "GSS_libgoal_widgets_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-widgets"}, {"name": "GTest_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for GTest."}], "type": "PATH", "value": "/usr/lib/x86_64-linux-gnu/cmake/GTest"}, {"name": "HAVE_STDATOMIC", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_STDATOMIC"}], "type": "INTERNAL", "value": "1"}, {"name": "LIBGOAL_PACKAGES_BLACKLIST", "properties": [{"name": "HELPSTRING", "value": "Blacklist of packages to not include in the build"}], "type": "STRING", "value": ""}, {"name": "LIBGOAL_STANDALONE_BUILD", "properties": [{"name": "HELPSTRING", "value": "Build all libgoal libs - include them in the ALL target"}], "type": "BOOL", "value": "OFF"}, {"name": "LibGoal_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal"}, {"name": "LibGoal_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "LibGoal_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal"}, {"name": "LibavTests_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal/libgoal-libav/tests"}, {"name": "LibavTests_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "LibavTests_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/tests"}, {"name": "NDIStream_send_test_app_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-ndi/NDIStream_send_test_app"}, {"name": "NDIStream_send_test_app_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "NDIStream_send_test_app_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/NDIStream_send_test_app"}, {"name": "OPENCL_VERSION_2_2", "properties": [{"name": "HELPSTRING", "value": "Have symbol CL_VERSION_2_2"}], "type": "INTERNAL", "value": "1"}, {"name": "OPENCL_VERSION_3_0", "properties": [{"name": "HELPSTRING", "value": "Have symbol CL_VERSION_3_0"}], "type": "INTERNAL", "value": "1"}, {"name": "OPENGL_EGL_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "OPENGL_GLES2_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "OPENGL_GLES3_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "OPENGL_GLX_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "OPENGL_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "OPENGL_egl_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libEGL.so"}, {"name": "OPENGL_gles2_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libGLESv2.so"}, {"name": "OPENGL_gles3_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libGLESv2.so"}, {"name": "OPENGL_glu_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libGLU.so"}, {"name": "OPENGL_glx_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libGLX.so"}, {"name": "OPENGL_opengl_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOpenGL.so"}, {"name": "OPENGL_xmesa_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "OPENGL_xmesa_INCLUDE_DIR-NOTFOUND"}, {"name": "OpenCL_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "OpenCL_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libOpenCL.so"}, {"name": "PKG_CONFIG_ARGN", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Arguments to supply to pkg-config"}], "type": "STRING", "value": ""}, {"name": "PKG_CONFIG_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "pkg-config executable"}], "type": "FILEPATH", "value": "/usr/bin/pkg-config"}, {"name": "PKG_XKB_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "PKG_XKB_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include"}, {"name": "PKG_XKB_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lxkbcommon"}, {"name": "PKG_XKB_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "PKG_XKB_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "xkbcommon"}, {"name": "PKG_XKB_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "PKG_XKB_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "xkbcommon"}, {"name": "PKG_XKB_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "PKG_XKB_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lxkbcommon"}, {"name": "PKG_XKB_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "xkbcommon"}, {"name": "PKG_XKB_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "PKG_XKB_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1.4.0"}, {"name": "PKG_XKB_xkbcommon_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_xkbcommon_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_xkbcommon_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "PKG_XKB_xkbcommon_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "QT_ADDITIONAL_HOST_PACKAGES_PREFIX_PATH", "properties": [{"name": "HELPSTRING", "value": "Additional directories where find(Qt6 ...) host Qt components are searched"}], "type": "STRING", "value": ""}, {"name": "QT_ADDITIONAL_PACKAGES_PREFIX_PATH", "properties": [{"name": "HELPSTRING", "value": "Additional directories where find(Qt6 ...) components are searched"}], "type": "STRING", "value": ""}, {"name": "QT_CREATOR_SKIP_PACKAGE_MANAGER_SETUP", "properties": [{"name": "HELPSTRING", "value": "Skip Qt Creator's package manager auto-setup"}], "type": "BOOL", "value": "OFF"}, {"name": "QT_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for QT."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6"}, {"name": "QT_FEATURE_abstractbutton", "properties": [{"name": "HELPSTRING", "value": "Qt feature: abstractbutton (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_abstractslider", "properties": [{"name": "HELPSTRING", "value": "Qt feature: abstractslider (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_accessibility", "properties": [{"name": "HELPSTRING", "value": "Qt feature: accessibility (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_accessibility_atspi_bridge", "properties": [{"name": "HELPSTRING", "value": "Qt feature: accessibility_atspi_bridge (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_action", "properties": [{"name": "HELPSTRING", "value": "Qt feature: action (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_aesni", "properties": [{"name": "HELPSTRING", "value": "Qt feature: aesni (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_alloca", "properties": [{"name": "HELPSTRING", "value": "Qt feature: alloca (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_alloca_h", "properties": [{"name": "HELPSTRING", "value": "Qt feature: alloca_h (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_alloca_malloc_h", "properties": [{"name": "HELPSTRING", "value": "Qt feature: alloca_malloc_h (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_alsa", "properties": [{"name": "HELPSTRING", "value": "Qt feature: alsa (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_android_style_assets", "properties": [{"name": "HELPSTRING", "value": "Qt feature: android_style_assets (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_animation", "properties": [{"name": "HELPSTRING", "value": "Qt feature: animation (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_appstore_compliant", "properties": [{"name": "HELPSTRING", "value": "Qt feature: appstore_compliant (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_arm_crc32", "properties": [{"name": "HELPSTRING", "value": "Qt feature: arm_crc32 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_arm_crypto", "properties": [{"name": "HELPSTRING", "value": "Qt feature: arm_crypto (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avfoundation", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avfoundation (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avx", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512bw", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512bw (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512cd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512cd (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512dq", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512dq (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512er", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512er (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512f", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512f (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512ifma", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512ifma (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512pf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512pf (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512vbmi", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512vbmi (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512vbmi2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512vbmi2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512vl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512vl (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_backtrace", "properties": [{"name": "HELPSTRING", "value": "Qt feature: backtrace (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_batch_test_support", "properties": [{"name": "HELPSTRING", "value": "Qt feature: batch_test_support (from target Qt6::Test)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_brotli", "properties": [{"name": "HELPSTRING", "value": "Qt feature: brotli (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_buttongroup", "properties": [{"name": "HELPSTRING", "value": "Qt feature: buttongroup (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_c11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: c11 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_c99", "properties": [{"name": "HELPSTRING", "value": "Qt feature: c99 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_calendarwidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: calendarwidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cborstreamreader", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cborstreamreader (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cborstreamwriter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cborstreamwriter (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_checkbox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: checkbox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_clipboard", "properties": [{"name": "HELPSTRING", "value": "Qt feature: clipboard (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_clock_gettime", "properties": [{"name": "HELPSTRING", "value": "Qt feature: clock_gettime (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_clock_monotonic", "properties": [{"name": "HELPSTRING", "value": "Qt feature: clock_monotonic (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_colordialog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: colordialog (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_colornames", "properties": [{"name": "HELPSTRING", "value": "Qt feature: colornames (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_columnview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: columnview (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_combobox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: combobox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_commandlineparser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: commandlineparser (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_commandlinkbutton", "properties": [{"name": "HELPSTRING", "value": "Qt feature: commandlinkbutton (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_completer", "properties": [{"name": "HELPSTRING", "value": "Qt feature: completer (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_concatenatetablesproxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: concatenatetablesproxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_concurrent", "properties": [{"name": "HELPSTRING", "value": "Qt feature: concurrent (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_contextmenu", "properties": [{"name": "HELPSTRING", "value": "Qt feature: contextmenu (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_coreaudio", "properties": [{"name": "HELPSTRING", "value": "Qt feature: coreaudio (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cpp_winrt", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cpp_winrt (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cross_compile", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cross_compile (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cssparser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cssparser (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ctf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ctf (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cursor", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cursor (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx11 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx11_future", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx11_future (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx14", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx14 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx17", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx17 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx17_filesystem", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx17_filesystem (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx1z", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx1z (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx20", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx20 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cxx2a", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx2a (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cxx2b", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx2b (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_datawidgetmapper", "properties": [{"name": "HELPSTRING", "value": "Qt feature: datawidgetmapper (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_datestring", "properties": [{"name": "HELPSTRING", "value": "Qt feature: datestring (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_datetimeedit", "properties": [{"name": "HELPSTRING", "value": "Qt feature: datetimeedit (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_datetimeparser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: datetimeparser (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dbus", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dbus (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dbus_linked", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dbus_linked (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_debug", "properties": [{"name": "HELPSTRING", "value": "Qt feature: debug (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_debug_and_release", "properties": [{"name": "HELPSTRING", "value": "Qt feature: debug_and_release (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_desktopservices", "properties": [{"name": "HELPSTRING", "value": "Qt feature: desktopservices (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_developer_build", "properties": [{"name": "HELPSTRING", "value": "Qt feature: developer_build (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_dial", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dial (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dialog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dialog (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dialogbuttonbox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dialogbuttonbox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_direct2d", "properties": [{"name": "HELPSTRING", "value": "Qt feature: direct2d (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_direct2d1_1", "properties": [{"name": "HELPSTRING", "value": "Qt feature: direct2d1_1 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_directfb", "properties": [{"name": "HELPSTRING", "value": "Qt feature: directfb (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_directwrite", "properties": [{"name": "HELPSTRING", "value": "Qt feature: directwrite (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_directwrite3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: directwrite3 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_dladdr", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dladdr (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dlopen", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dlopen (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dnslookup", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dnslookup (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dockwidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dockwidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dom", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dom (from target Qt6::Xml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_doubleconversion", "properties": [{"name": "HELPSTRING", "value": "Qt feature: doubleconversion (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_draganddrop", "properties": [{"name": "HELPSTRING", "value": "Qt feature: draganddrop (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_drm_atomic", "properties": [{"name": "HELPSTRING", "value": "Qt feature: drm_atomic (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dtls", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dtls (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dynamicgl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dynamicgl (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_easingcurve", "properties": [{"name": "HELPSTRING", "value": "Qt feature: easingcurve (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_effects", "properties": [{"name": "HELPSTRING", "value": "Qt feature: effects (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_egl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: egl (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_egl_x11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: egl_x11 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_eglfs", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_eglfs_brcm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_brcm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_egldevice", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_egldevice (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_eglfs_gbm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_gbm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_mali", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_mali (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_openwfd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_openwfd (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_rcar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_rcar (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_viv", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_viv (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_viv_wl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_viv_wl (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_vsp2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_vsp2 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_x11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_x11 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_enable_new_dtags", "properties": [{"name": "HELPSTRING", "value": "Qt feature: enable_new_dtags (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_errormessage", "properties": [{"name": "HELPSTRING", "value": "Qt feature: errormessage (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_etw", "properties": [{"name": "HELPSTRING", "value": "Qt feature: etw (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_evdev", "properties": [{"name": "HELPSTRING", "value": "Qt feature: evdev (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_eventfd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eventfd (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_evr", "properties": [{"name": "HELPSTRING", "value": "Qt feature: evr (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_f16c", "properties": [{"name": "HELPSTRING", "value": "Qt feature: f16c (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ffmpeg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ffmpeg (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_filedialog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: filedialog (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_filesystemiterator", "properties": [{"name": "HELPSTRING", "value": "Qt feature: filesystemiterator (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_filesystemmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: filesystemmodel (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_filesystemwatcher", "properties": [{"name": "HELPSTRING", "value": "Qt feature: filesystemwatcher (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_fontcombobox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: fontcombobox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_fontconfig", "properties": [{"name": "HELPSTRING", "value": "Qt feature: fontconfig (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_fontdialog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: fontdialog (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_force_asserts", "properties": [{"name": "HELPSTRING", "value": "Qt feature: force_asserts (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_force_debug_info", "properties": [{"name": "HELPSTRING", "value": "Qt feature: force_debug_info (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_forkfd_pidfd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: forkfd_pidfd (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_formlayout", "properties": [{"name": "HELPSTRING", "value": "Qt feature: formlayout (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_framework", "properties": [{"name": "HELPSTRING", "value": "Qt feature: framework (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_freetype", "properties": [{"name": "HELPSTRING", "value": "Qt feature: freetype (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_fscompleter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: fscompleter (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_futimens", "properties": [{"name": "HELPSTRING", "value": "Qt feature: futimens (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_future", "properties": [{"name": "HELPSTRING", "value": "Qt feature: future (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gc_binaries", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gc_binaries (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gestures", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gestures (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_getauxval", "properties": [{"name": "HELPSTRING", "value": "Qt feature: getauxval (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_getentropy", "properties": [{"name": "HELPSTRING", "value": "Qt feature: getentropy (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_getifaddrs", "properties": [{"name": "HELPSTRING", "value": "Qt feature: getifaddrs (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gif", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gif (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_glib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: glib (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_glibc", "properties": [{"name": "HELPSTRING", "value": "Qt feature: glibc (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gpu_vivante", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gpu_vivante (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_graphicseffect", "properties": [{"name": "HELPSTRING", "value": "Qt feature: graphicseffect (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_graphicsview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: graphicsview (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_groupbox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: groupbox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gssapi", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gssapi (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gstreamer", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gstreamer (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gstreamer_1_0", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gstreamer_1_0 (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gstreamer_app", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gstreamer_app (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gstreamer_gl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gstreamer_gl (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gstreamer_photography", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gstreamer_photography (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gtk3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gtk3 (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gui", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gui (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_harfbuzz", "properties": [{"name": "HELPSTRING", "value": "Qt feature: harfbuzz (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_headersclean", "properties": [{"name": "HELPSTRING", "value": "Qt feature: headersclean (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_highdpiscaling", "properties": [{"name": "HELPSTRING", "value": "Qt feature: highdpiscaling (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_hij<PERSON>endar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: hij<PERSON><PERSON>ar (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_http", "properties": [{"name": "HELPSTRING", "value": "Qt feature: http (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ico", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ico (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_icu", "properties": [{"name": "HELPSTRING", "value": "Qt feature: icu (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_identityproxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: identityproxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ifr_index", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ifr_index (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_im", "properties": [{"name": "HELPSTRING", "value": "Qt feature: im (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_image_heuristic_mask", "properties": [{"name": "HELPSTRING", "value": "Qt feature: image_heuristic_mask (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_image_text", "properties": [{"name": "HELPSTRING", "value": "Qt feature: image_text (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_bmp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_bmp (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_jpeg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_jpeg (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_png", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_png (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_ppm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_ppm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_xbm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_xbm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_xpm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_xpm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformatplugin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformatplugin (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageio_text_loading", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageio_text_loading (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_inotify", "properties": [{"name": "HELPSTRING", "value": "Qt feature: inotify (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_inputdialog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: inputdialog (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_integrityfb", "properties": [{"name": "HELPSTRING", "value": "Qt feature: integrityfb (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_integrityhid", "properties": [{"name": "HELPSTRING", "value": "Qt feature: <PERSON>hid (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_intelcet", "properties": [{"name": "HELPSTRING", "value": "Qt feature: intelcet (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_ipv6ifname", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ipv6ifname (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_islamiccivilcalendar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: islamiccivilcalendar (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_itemmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: itemmodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_itemmodeltester", "properties": [{"name": "HELPSTRING", "value": "Qt feature: itemmodeltester (from target Qt6::Test)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_itemviews", "properties": [{"name": "HELPSTRING", "value": "Qt feature: itemviews (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_jalalicalendar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: jala<PERSON><PERSON>dar (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_journald", "properties": [{"name": "HELPSTRING", "value": "Qt feature: journald (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_jpeg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: jpeg (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_keysequenceedit", "properties": [{"name": "HELPSTRING", "value": "Qt feature: keysequenceedit (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_kms", "properties": [{"name": "HELPSTRING", "value": "Qt feature: kms (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_label", "properties": [{"name": "HELPSTRING", "value": "Qt feature: label (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_largefile", "properties": [{"name": "HELPSTRING", "value": "Qt feature: largefile (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_lcdnumber", "properties": [{"name": "HELPSTRING", "value": "Qt feature: lcdnumber (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_libinput", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libinput (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_libinput_axis_api", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libinput_axis_api (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_libinput_hires_wheel_support", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libinput_hires_wheel_support (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_libproxy", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libproxy (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_library", "properties": [{"name": "HELPSTRING", "value": "Qt feature: library (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_libudev", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libudev (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_lineedit", "properties": [{"name": "HELPSTRING", "value": "Qt feature: lineedit (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_linkat", "properties": [{"name": "HELPSTRING", "value": "Qt feature: linkat (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_linux_dmabuf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: linux_dmabuf (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_linux_netlink", "properties": [{"name": "HELPSTRING", "value": "Qt feature: linux_netlink (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_linux_v4l", "properties": [{"name": "HELPSTRING", "value": "Qt feature: linux_v4l (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_linuxfb", "properties": [{"name": "HELPSTRING", "value": "Qt feature: linuxfb (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_listview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: listview (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_listwidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: listwidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_localserver", "properties": [{"name": "HELPSTRING", "value": "Qt feature: localserver (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_lttng", "properties": [{"name": "HELPSTRING", "value": "Qt feature: lttng (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_mainwindow", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mainwindow (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_mdiarea", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mdiarea (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_menu", "properties": [{"name": "HELPSTRING", "value": "Qt feature: menu (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_menubar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: menubar (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_messagebox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: messagebox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_mimetype", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mimetype (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_mimetype_database", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mimetype_database (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_mips_dsp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mips_dsp (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_mips_dspr2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mips_dspr2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_mmrenderer", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mmrenderer (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_movie", "properties": [{"name": "HELPSTRING", "value": "Qt feature: movie (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_mtdev", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mtdev (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_multiprocess", "properties": [{"name": "HELPSTRING", "value": "Qt feature: multiprocess (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_neon", "properties": [{"name": "HELPSTRING", "value": "Qt feature: neon (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_network", "properties": [{"name": "HELPSTRING", "value": "Qt feature: network (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_networkdiskcache", "properties": [{"name": "HELPSTRING", "value": "Qt feature: networkdiskcache (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_networkinterface", "properties": [{"name": "HELPSTRING", "value": "Qt feature: networkinterface (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_networklistmanager", "properties": [{"name": "HELPSTRING", "value": "Qt feature: networklistmanager (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_networkproxy", "properties": [{"name": "HELPSTRING", "value": "Qt feature: networkproxy (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_no_direct_extern_access", "properties": [{"name": "HELPSTRING", "value": "Qt feature: no_direct_extern_access (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_ocsp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ocsp (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_opengl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengl (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_opengles2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengles2 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opengles3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengles3 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opengles31", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengles31 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opengles32", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengles32 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opensles", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opensles (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_openssl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: openssl (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_openssl_linked", "properties": [{"name": "HELPSTRING", "value": "Qt feature: openssl_linked (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opensslv11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opensslv11 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opensslv30", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opensslv30 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_openvg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: openvg (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_pcre2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pcre2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_pdf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pdf (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_permissions", "properties": [{"name": "HELPSTRING", "value": "Qt feature: permissions (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_picture", "properties": [{"name": "HELPSTRING", "value": "Qt feature: picture (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_pkg_config", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pkg_config (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_plugin_manifest", "properties": [{"name": "HELPSTRING", "value": "Qt feature: plugin_manifest (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_png", "properties": [{"name": "HELPSTRING", "value": "Qt feature: png (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_poll_exit_on_error", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_exit_on_error (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_poll_poll", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_poll (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_poll_pollts", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_pollts (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_poll_ppoll", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_ppoll (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_poll_select", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_select (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_posix_fallocate", "properties": [{"name": "HELPSTRING", "value": "Qt feature: posix_fallocate (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_precompile_header", "properties": [{"name": "HELPSTRING", "value": "Qt feature: precompile_header (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_printsupport", "properties": [{"name": "HELPSTRING", "value": "Qt feature: printsupport (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_private_tests", "properties": [{"name": "HELPSTRING", "value": "Qt feature: private_tests (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_process", "properties": [{"name": "HELPSTRING", "value": "Qt feature: process (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_processenvironment", "properties": [{"name": "HELPSTRING", "value": "Qt feature: processenvironment (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_progressbar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: progressbar (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_progressdialog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: progressdialog (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_proxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: proxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_publicsuffix_qt", "properties": [{"name": "HELPSTRING", "value": "Qt feature: publicsuffix_qt (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_publicsuffix_system", "properties": [{"name": "HELPSTRING", "value": "Qt feature: publicsuffix_system (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_pulseaudio", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pulseaudio (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_pushbutton", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pushbutton (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_animation", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_animation (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_debug", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_debug (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_delegate_model", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_delegate_model (from target Qt6::QmlModels)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_itemmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_itemmodel (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_jit", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_jit (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_list_model", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_list_model (from target Qt6::QmlModels)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_locale", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_locale (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_network", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_network (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_object_model", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_object_model (from target Qt6::QmlModels)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_preview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_preview (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_profiler", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_profiler (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_python", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_python (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_table_model", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_table_model (from target Qt6::QmlModels)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_worker_script", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_worker_script (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_xml_http_request", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_xml_http_request (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_xmllistmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_xmllistmodel (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qqnx_imf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qqnx_imf (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_qqnx_pps", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qqnx_pps (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_quick_animatedimage", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_animatedimage (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_canvas", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_canvas (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_designer", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_designer (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_draganddrop", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_draganddrop (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_flipable", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_flipable (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_gridview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_gridview (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_itemview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_itemview (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_listview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_listview (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_particles", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_particles (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_path", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_path (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_pathview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_pathview (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_positioners", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_positioners (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_repeater", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_repeater (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_shadereffect", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_shadereffect (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_sprite", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_sprite (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_tableview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_tableview (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_treeview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_treeview (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_viewtransitions", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_viewtransitions (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_radiobutton", "properties": [{"name": "HELPSTRING", "value": "Qt feature: radiobutton (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_raster_64bit", "properties": [{"name": "HELPSTRING", "value": "Qt feature: raster_64bit (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_raster_fp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: raster_fp (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_rdrnd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: rdrnd (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_rdseed", "properties": [{"name": "HELPSTRING", "value": "Qt feature: rdseed (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_reduce_exports", "properties": [{"name": "HELPSTRING", "value": "Qt feature: reduce_exports (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_reduce_relocations", "properties": [{"name": "HELPSTRING", "value": "Qt feature: reduce_relocations (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_regularexpression", "properties": [{"name": "HELPSTRING", "value": "Qt feature: regularexpression (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_relocatable", "properties": [{"name": "HELPSTRING", "value": "Qt feature: relocatable (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_renameat2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: renameat2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_resizehandler", "properties": [{"name": "HELPSTRING", "value": "Qt feature: resizehandler (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_rpath", "properties": [{"name": "HELPSTRING", "value": "Qt feature: rpath (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_rubberband", "properties": [{"name": "HELPSTRING", "value": "Qt feature: rubberband (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_schannel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: schannel (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_scrollarea", "properties": [{"name": "HELPSTRING", "value": "Qt feature: scrollarea (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_scrollbar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: scrollbar (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_scroller", "properties": [{"name": "HELPSTRING", "value": "Qt feature: scroller (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sctp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sctp (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_securetransport", "properties": [{"name": "HELPSTRING", "value": "Qt feature: securetransport (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_separate_debug_info", "properties": [{"name": "HELPSTRING", "value": "Qt feature: separate_debug_info (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sessionmanager", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sessionmanager (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_settings", "properties": [{"name": "HELPSTRING", "value": "Qt feature: settings (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sha3_fast", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sha3_fast (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_shani", "properties": [{"name": "HELPSTRING", "value": "Qt feature: shani (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_shared", "properties": [{"name": "HELPSTRING", "value": "Qt feature: shared (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sharedmemory", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sharedmemory (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_shortcut", "properties": [{"name": "HELPSTRING", "value": "Qt feature: shortcut (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_signaling_nan", "properties": [{"name": "HELPSTRING", "value": "Qt feature: signaling_nan (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_simulator_and_device", "properties": [{"name": "HELPSTRING", "value": "Qt feature: simulator_and_device (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_sizegrip", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sizegrip (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_slider", "properties": [{"name": "HELPSTRING", "value": "Qt feature: slider (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_slog2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: slog2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_socks5", "properties": [{"name": "HELPSTRING", "value": "Qt feature: socks5 (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sortfilterproxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sortfilterproxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_spatialaudio", "properties": [{"name": "HELPSTRING", "value": "Qt feature: spatialaudio (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_spatialaudio_quick3d", "properties": [{"name": "HELPSTRING", "value": "Qt feature: spatialaudio_quick3d (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_spinbox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: spinbox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_splashscreen", "properties": [{"name": "HELPSTRING", "value": "Qt feature: splashscreen (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_splitter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: splitter (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sql", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sql (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sse2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sse2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sse3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sse3 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sse4_1", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sse4_1 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sse4_2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sse4_2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ssl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ssl (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sspi", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sspi (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_ssse3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ssse3 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_stack_protector_strong", "properties": [{"name": "HELPSTRING", "value": "Qt feature: stack_protector_strong (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_stackedwidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: stackedwidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_standarditemmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: standarditemmodel (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_static", "properties": [{"name": "HELPSTRING", "value": "Qt feature: static (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_statusbar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: statusbar (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_statustip", "properties": [{"name": "HELPSTRING", "value": "Qt feature: statustip (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_statx", "properties": [{"name": "HELPSTRING", "value": "Qt feature: statx (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_std_atomic64", "properties": [{"name": "HELPSTRING", "value": "Qt feature: std_atomic64 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_stdlib_libcpp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: stdlib_libcpp (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_stringlistmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: stringlistmodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_style_android", "properties": [{"name": "HELPSTRING", "value": "Qt feature: style_android (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_style_fusion", "properties": [{"name": "HELPSTRING", "value": "Qt feature: style_fusion (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_style_mac", "properties": [{"name": "HELPSTRING", "value": "Qt feature: style_mac (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_style_stylesheet", "properties": [{"name": "HELPSTRING", "value": "Qt feature: style_stylesheet (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_style_windows", "properties": [{"name": "HELPSTRING", "value": "Qt feature: style_windows (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_style_windowsvista", "properties": [{"name": "HELPSTRING", "value": "Qt feature: style_windowsvista (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_syntaxhighlighter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: syntaxhighlighter (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_syslog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: syslog (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_doubleconversion", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_doubleconversion (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_freetype", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_freetype (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_system_harfbuzz", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_harfbuzz (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_jpeg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_jpeg (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_libb2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_libb2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_pcre2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_pcre2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_png", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_png (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_proxies", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_proxies (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_system_textmarkdownreader", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_textmarkdownreader (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_xcb_xinput", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_xcb_xinput (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_zlib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_zlib (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_systemsemaphore", "properties": [{"name": "HELPSTRING", "value": "Qt feature: systemsemaphore (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_systemtrayicon", "properties": [{"name": "HELPSTRING", "value": "Qt feature: systemtrayicon (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tabbar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tabbar (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tabletevent", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tabletevent (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tableview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tableview (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tablewidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tablewidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tabwidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tabwidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_temporaryfile", "properties": [{"name": "HELPSTRING", "value": "Qt feature: temporaryfile (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_testlib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: testlib (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_testlib_selfcover", "properties": [{"name": "HELPSTRING", "value": "Qt feature: testlib_selfcover (from target Qt6::Test)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_textbrowser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textbrowser (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textdate", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textdate (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textedit", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textedit (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_texthtmlparser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: texthtmlparser (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textmarkdownreader", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textmarkdownreader (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textmarkdownwriter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textmarkdownwriter (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textodfwriter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textodfwriter (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_thread", "properties": [{"name": "HELPSTRING", "value": "Qt feature: thread (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_threadsafe_cloexec", "properties": [{"name": "HELPSTRING", "value": "Qt feature: threadsafe_cloexec (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_timezone", "properties": [{"name": "HELPSTRING", "value": "Qt feature: timezone (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_toolbar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: toolbar (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_toolbox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: toolbox (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_toolbutton", "properties": [{"name": "HELPSTRING", "value": "Qt feature: toolbutton (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tooltip", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tooltip (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_topleveldomain", "properties": [{"name": "HELPSTRING", "value": "Qt feature: topleveldomain (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_translation", "properties": [{"name": "HELPSTRING", "value": "Qt feature: translation (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_transposeproxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: transposeproxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_treeview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: treeview (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_treewidget", "properties": [{"name": "HELPSTRING", "value": "Qt feature: treewidget (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_tslib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tslib (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_tuiotouch", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tui<PERSON>uch (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_udpsocket", "properties": [{"name": "HELPSTRING", "value": "Qt feature: udpsocket (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_undocommand", "properties": [{"name": "HELPSTRING", "value": "Qt feature: undocommand (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_undogroup", "properties": [{"name": "HELPSTRING", "value": "Qt feature: undogroup (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_undostack", "properties": [{"name": "HELPSTRING", "value": "Qt feature: undostack (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_undoview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: undoview (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_use_bfd_linker", "properties": [{"name": "HELPSTRING", "value": "Qt feature: use_bfd_linker (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_use_gold_linker", "properties": [{"name": "HELPSTRING", "value": "Qt feature: use_gold_linker (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_use_lld_linker", "properties": [{"name": "HELPSTRING", "value": "Qt feature: use_lld_linker (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_use_mold_linker", "properties": [{"name": "HELPSTRING", "value": "Qt feature: use_mold_linker (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_vaapi", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vaapi (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_vaes", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vaes (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_valgrind", "properties": [{"name": "HELPSTRING", "value": "Qt feature: valgrind (from target Qt6::Test)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_validator", "properties": [{"name": "HELPSTRING", "value": "Qt feature: validator (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_videotoolbox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: videotoolbox (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_vkgen", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vkgen (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_vkkhrdisplay", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vk<PERSON>rdisplay (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_vnc", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vnc (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_vsp2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vsp2 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_vulkan", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vulkan (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_wasm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wasm (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_wasm_exceptions", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wasm_exceptions (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_wasm_simd128", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wasm_simd128 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_whatsthis", "properties": [{"name": "HELPSTRING", "value": "Qt feature: whatsthis (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_wheelevent", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wheelevent (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_widgets", "properties": [{"name": "HELPSTRING", "value": "Qt feature: widgets (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_widgettextcontrol", "properties": [{"name": "HELPSTRING", "value": "Qt feature: widgettextcontrol (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_wizard", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wizard (from target Qt6::Widgets)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_wmf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wmf (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_wmsdk", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wmsdk (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_x86intrin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: x86intrin (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xcb", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xcb_egl_plugin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_egl_plugin (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xcb_glx", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_glx (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xcb_glx_plugin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_glx_plugin (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xcb_native_painting", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_native_painting (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_sm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_sm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_xlib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_xlib (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xkbcommon", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xkbcommon (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xkbcommon_x11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xkbcommon_x11 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xlib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xlib (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xml", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xml (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xmlstream", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xmlstream (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xmlstreamreader", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xmlstreamreader (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xmlstreamwriter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xmlstreamwriter (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xrender", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xrender (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_zstd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: zstd (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_QMAKE_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "/opt/Qt/6.5.3/gcc_64/bin/qmake"}, {"name": "Qt6Concurrent_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Concurrent."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Concurrent"}, {"name": "Qt6CoreTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6CoreTools."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6CoreTools"}, {"name": "Qt6Core_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Core."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Core"}, {"name": "Qt6DBusTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6DBusTools."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6DBusTools"}, {"name": "Qt6DBus_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6DBus."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6DBus"}, {"name": "Qt6GuiTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6GuiTools."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6GuiTools"}, {"name": "Qt6Gui_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Gui."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Gui"}, {"name": "Qt6Multimedia_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Multimedia."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Multimedia"}, {"name": "Qt6Network_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Network."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Network"}, {"name": "Qt6OpenGL_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6OpenGL."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6OpenGL"}, {"name": "Qt6QmlCompilerPlusPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlCompilerPlusPrivate."}], "type": "PATH", "value": "Qt6QmlCompilerPlusPrivate_DIR-NOTFOUND"}, {"name": "Qt6QmlIntegration_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlIntegration."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6QmlIntegration"}, {"name": "Qt6QmlModels_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlModels."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6QmlModels"}, {"name": "Qt6QmlTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlTools."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6QmlTools"}, {"name": "Qt6Qml_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Qml."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Qml"}, {"name": "Qt6Quick_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Quick."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Quick"}, {"name": "Qt6Test_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Test."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Test"}, {"name": "Qt6WebSockets_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6WebSockets."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6WebSockets"}, {"name": "Qt6WidgetsTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6WidgetsTools."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6WidgetsTools"}, {"name": "Qt6Widgets_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Widgets."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Widgets"}, {"name": "Qt6Xml_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Xml."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Xml"}, {"name": "Qt6_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6."}], "type": "PATH", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6"}, {"name": "Vulkan_GLSLANG_VALIDATOR_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "Vulkan_GLSLANG_VALIDATOR_EXECUTABLE-NOTFOUND"}, {"name": "Vulkan_GLSLC_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "Vulkan_GLSLC_EXECUTABLE-NOTFOUND"}, {"name": "Vulkan_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "Vulkan_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libvulkan.so"}, {"name": "XKB_INCLUDE_DIR", "properties": [{"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/include"}, {"name": "XKB_LIBRARY", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libxkbcommon.so"}, {"name": "_CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED", "properties": [{"name": "HELPSTRING", "value": "linker supports push/pop state"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "CMAKE_INSTALL_PREFIX during last run"}], "type": "INTERNAL", "value": "/usr/local"}, {"name": "__pkg_config_arguments_PKG_XKB", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "QUIET;xkbcommon"}, {"name": "__pkg_config_arguments_libavcodec", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "REQUIRED;IMPORTED_TARGET;libavcodec"}, {"name": "__pkg_config_arguments_libavfilter", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "REQUIRED;IMPORTED_TARGET;libavfilter"}, {"name": "__pkg_config_arguments_libavformat", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "REQUIRED;IMPORTED_TARGET;libavformat"}, {"name": "__pkg_config_arguments_libavutil", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "REQUIRED;IMPORTED_TARGET;libavutil"}, {"name": "__pkg_config_arguments_libswresample", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "REQUIRED;IMPORTED_TARGET;libswresample"}, {"name": "__pkg_config_arguments_libswscale", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "REQUIRED;IMPORTED_TARGET;libswscale"}, {"name": "__pkg_config_checked_PKG_XKB", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_libavcodec", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_libavfilter", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_libavformat", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_libavutil", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_libswresample", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_libswscale", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__qt_qml_macros_module_base_dir", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/opt/Qt/6.5.3/gcc_64/lib/cmake/Qt6Qml"}, {"name": "av-routing-and-mixing_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug"}, {"name": "av-routing-and-mixing_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "av-routing-and-mixing_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing"}, {"name": "libavcodec_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "libavcodec_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "libavcodec_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libavcodec_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libavcodec_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lavcodec"}, {"name": "libavcodec_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libavcodec_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "avcodec"}, {"name": "libavcodec_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libavcodec_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libavcodec"}, {"name": "libavcodec_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "libavcodec_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "libavcodec_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libavcodec_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lavcodec;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lwebpmux;-lwebp;-lm;-llzma;-ldav1d;-lrsvg-2;-lm;-lgio-2.0;-lgdk_pixbuf-2.0;-lgobject-2.0;-lglib-2.0;-lcairo;-lzvbi;-lm;-lpthread;-lm;-lpng;-lz;-lsnappy;-lstdc++;-lz;-laom;-lcodec2;-lgsm;-lmp3lame;-lm;-lopenjp2;-lopus;-lm;-lshine;-lspeex;-ltheoraenc;-ltheoradec;-logg;-ltwolame;-lvorbis;-lvorbisenc;-lwebp;-lx264;-lx265;-lxvidcore;-pthread;-lva;-lmfx;-lstdc++;-ldl;-lswresample;-lm;-lsoxr;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-lmfx;-lstdc++;-ldl;-lOpenCL;-lva;-lXv;-lX11;-lXext"}, {"name": "libavcodec_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-pthread"}, {"name": "libavcodec_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "avcodec;vpx;m;vpx;m;vpx;m;vpx;m;webpmux;webp;m;lzma;dav1d;rsvg-2;m;gio-2.0;gdk_pixbuf-2.0;gobject-2.0;glib-2.0;cairo;zvbi;m;pthread;m;png;z;snappy;stdc++;z;aom;codec2;gsm;mp3lame;m;openjp2;opus;m;shine;speex;theoraenc;theoradec;ogg;twolame;vorbis;vorbisenc;webp;x264;x265;xvidcore;va;mfx;stdc++;dl;swresample;m;soxr;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;mfx;stdc++;dl;OpenCL;va;Xv;X11;Xext"}, {"name": "libavcodec_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libavcodec_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "58.134.100"}, {"name": "libavcodec_libavcodec_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_libavcodec_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_libavcodec_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavcodec_libavcodec_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "libavfilter_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "libavfilter_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libavfilter_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libavfilter_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lavfilter"}, {"name": "libavfilter_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libavfilter_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "avfilter"}, {"name": "libavfilter_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libavfilter_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libavfilter"}, {"name": "libavfilter_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "libavfilter_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "libavfilter_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libavfilter_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lavfilter;-pthread;-lm;-lpocketsphinx;-lsphinxbase;-lsphinxad;-lbs2b;-llilv-0;-ldl;-lsratom-0;-lsord-0;-lserd-0;-lrubberband;-lsamplerate;-lstdc++;-lmysofa;-lflite_cmu_time_awb;-lflite_cmu_us_awb;-lflite_cmu_us_kal;-lflite_cmu_us_kal16;-lflite_cmu_us_rms;-lflite_cmu_us_slt;-lflite_usenglish;-lflite_cmulex;-lflite;-lfribidi;-lass;-lva;-lvidstab;-lm;-lgomp;-lzmq;-lzimg;-lOpenCL;-lfontconfig;-lfreetype;-lmfx;-lstdc++;-ldl;-lswscale;-lm;-lpostproc;-lm;-lavformat;-lm;-lxml2;-lbz2;-lgme;-lopenmpt;-lstdc++;-lchromaprint;-lbluray;-lz;-lgnutls;-lrabbitmq;-lsrt-gnutls;-lssh;-lzmq;-lavcodec;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lwebpmux;-lwebp;-lm;-llzma;-ldav1d;-lrsvg-2;-lm;-lgio-2.0;-lgdk_pixbuf-2.0;-lgobject-2.0;-lglib-2.0;-lcairo;-lzvbi;-lm;-lpthread;-lm;-lpng;-lz;-lsnappy;-lstdc++;-lz;-laom;-lcodec2;-lgsm;-lmp3lame;-lm;-lopenjp2;-lopus;-lm;-lshine;-lspeex;-ltheoraenc;-ltheoradec;-logg;-ltwolame;-lvorbis;-lvorbisenc;-lwebp;-lx264;-lx265;-lxvidcore;-pthread;-lva;-lmfx;-lstdc++;-ldl;-lswresample;-lm;-lsoxr;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-lmfx;-lstdc++;-ldl;-lOpenCL;-lva;-lXv;-lX11;-lXext"}, {"name": "libavfilter_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-pthread"}, {"name": "libavfilter_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "avfilter;m;pocketsphinx;sphinxbase;sphinxad;bs2b;lilv-0;dl;sratom-0;sord-0;serd-0;rubberband;samplerate;stdc++;mysofa;flite_cmu_time_awb;flite_cmu_us_awb;flite_cmu_us_kal;flite_cmu_us_kal16;flite_cmu_us_rms;flite_cmu_us_slt;flite_usenglish;flite_cmulex;flite;fribidi;ass;va;vidstab;m;gomp;zmq;zimg;OpenCL;fontconfig;freetype;mfx;stdc++;dl;swscale;m;postproc;m;avformat;m;xml2;bz2;gme;openmpt;stdc++;chromaprint;bluray;z;gnutls;rabbitmq;srt-gnutls;ssh;zmq;avcodec;vpx;m;vpx;m;vpx;m;vpx;m;webpmux;webp;m;lzma;dav1d;rsvg-2;m;gio-2.0;gdk_pixbuf-2.0;gobject-2.0;glib-2.0;cairo;zvbi;m;pthread;m;png;z;snappy;stdc++;z;aom;codec2;gsm;mp3lame;m;openjp2;opus;m;shine;speex;theoraenc;theoradec;ogg;twolame;vorbis;vorbisenc;webp;x264;x265;xvidcore;va;mfx;stdc++;dl;swresample;m;soxr;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;mfx;stdc++;dl;OpenCL;va;Xv;X11;Xext"}, {"name": "libavfilter_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libavfilter_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "7.110.100"}, {"name": "libavfilter_libavfilter_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_libavfilter_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_libavfilter_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavfilter_libavfilter_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "libavformat_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "libavformat_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libavformat_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libavformat_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lavformat"}, {"name": "libavformat_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libavformat_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "avformat"}, {"name": "libavformat_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libavformat_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libavformat"}, {"name": "libavformat_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "libavformat_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "libavformat_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libavformat_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lavformat;-lm;-lxml2;-lbz2;-lgme;-lopenmpt;-lstdc++;-lchromaprint;-lbluray;-lz;-lgnutls;-lrabbitmq;-lsrt-gnutls;-lssh;-lzmq;-lavcodec;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lwebpmux;-lwebp;-lm;-llzma;-ldav1d;-lrsvg-2;-lm;-lgio-2.0;-lgdk_pixbuf-2.0;-lgobject-2.0;-lglib-2.0;-lcairo;-lzvbi;-lm;-lpthread;-lm;-lpng;-lz;-lsnappy;-lstdc++;-lz;-laom;-lcodec2;-lgsm;-lmp3lame;-lm;-lopenjp2;-lopus;-lm;-lshine;-lspeex;-ltheoraenc;-ltheoradec;-logg;-ltwolame;-lvorbis;-lvorbisenc;-lwebp;-lx264;-lx265;-lxvidcore;-pthread;-lva;-lmfx;-lstdc++;-ldl;-lswresample;-lm;-lsoxr;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-lmfx;-lstdc++;-ldl;-lOpenCL;-lva;-lXv;-lX11;-lXext"}, {"name": "libavformat_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-pthread"}, {"name": "libavformat_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "avformat;m;xml2;bz2;gme;openmpt;stdc++;chromaprint;bluray;z;gnutls;rabbitmq;srt-gnutls;ssh;zmq;avcodec;vpx;m;vpx;m;vpx;m;vpx;m;webpmux;webp;m;lzma;dav1d;rsvg-2;m;gio-2.0;gdk_pixbuf-2.0;gobject-2.0;glib-2.0;cairo;zvbi;m;pthread;m;png;z;snappy;stdc++;z;aom;codec2;gsm;mp3lame;m;openjp2;opus;m;shine;speex;theoraenc;theoradec;ogg;twolame;vorbis;vorbisenc;webp;x264;x265;xvidcore;va;mfx;stdc++;dl;swresample;m;soxr;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;mfx;stdc++;dl;OpenCL;va;Xv;X11;Xext"}, {"name": "libavformat_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libavformat_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "58.76.100"}, {"name": "libavformat_libavformat_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_libavformat_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_libavformat_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavformat_libavformat_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "libavutil_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "libavutil_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libavutil_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libavutil_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lavutil"}, {"name": "libavutil_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libavutil_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "avu<PERSON>"}, {"name": "libavutil_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libavutil_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "liba<PERSON><PERSON>"}, {"name": "libavutil_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "libavutil_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "libavutil_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libavutil_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lavu<PERSON>;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-lmfx;-lstdc++;-ldl;-lOpenCL;-lva;-lXv;-lX11;-lXext"}, {"name": "libavutil_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-pthread"}, {"name": "libavutil_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;mfx;stdc++;dl;OpenCL;va;Xv;X11;Xext"}, {"name": "libavutil_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libavutil_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "56.70.100"}, {"name": "libavutil_libavutil_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_libavutil_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_libavutil_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libavutil_libavutil_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "libswresample_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "libswresample_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libswresample_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libswresample_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lswresample"}, {"name": "libswresample_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libswresample_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "swresample"}, {"name": "libswresample_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libswresample_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libswresample"}, {"name": "libswresample_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "libswresample_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "libswresample_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libswresample_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lswresample;-lm;-lsoxr;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-lmfx;-lstdc++;-ldl;-lOpenCL;-lva;-lXv;-lX11;-lXext"}, {"name": "libswresample_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-pthread"}, {"name": "libswresample_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "swresample;m;soxr;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;mfx;stdc++;dl;OpenCL;va;Xv;X11;Xext"}, {"name": "libswresample_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libswresample_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "3.9.100"}, {"name": "libswresample_libswresample_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_libswresample_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_libswresample_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswresample_libswresample_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "libswscale_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "libswscale_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libswscale_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libswscale_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lswscale"}, {"name": "libswscale_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libswscale_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "swscale"}, {"name": "libswscale_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libswscale_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "libswscale"}, {"name": "libswscale_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr"}, {"name": "libswscale_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/include/x86_64-linux-gnu"}, {"name": "libswscale_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/include/x86_64-linux-gnu"}, {"name": "libswscale_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/lib/x86_64-linux-gnu;-lswscale;-lm;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-lmfx;-lstdc++;-ldl;-lOpenCL;-lva;-lXv;-lX11;-lXext"}, {"name": "libswscale_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-pthread"}, {"name": "libswscale_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "swscale;m;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;mfx;stdc++;dl;OpenCL;va;Xv;X11;Xext"}, {"name": "libswscale_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "libswscale_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "5.9.100"}, {"name": "libswscale_libswscale_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_libswscale_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_libswscale_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "libswscale_libswscale_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "pkgcfg_lib_PKG_XKB_xkbcommon", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libxkbcommon.so"}, {"name": "pkgcfg_lib_libavcodec_avcodec", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libavcodec.so"}, {"name": "pkgcfg_lib_libavfilter_avfilter", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libavfilter.so"}, {"name": "pkgcfg_lib_libavformat_avformat", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libavformat.so"}, {"name": "pkgcfg_lib_libavutil_avutil", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libavutil.so"}, {"name": "pkgcfg_lib_libswresample_swresample", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libswresample.so"}, {"name": "pkgcfg_lib_libswscale_swscale", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/lib/x86_64-linux-gnu/libswscale.so"}, {"name": "prefix_result", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/lib/x86_64-linux-gnu"}, {"name": "recv_test_app_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_5_3_GCC_64bit-Debug/libgoal-ndi/recv_test_app"}, {"name": "recv_test_app_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "OFF"}, {"name": "recv_test_app_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/recv_test_app"}], "kind": "cache", "version": {"major": 2, "minor": 0}}