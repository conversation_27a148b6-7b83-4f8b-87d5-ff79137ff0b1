#!/bin/bash

set -e

if [ -z "$1" ] || [ -z "$2" ]
  then
    exit 1
fi

fileDir=$1
fileName=$2
filePath=$fileDir/$fileName

mkdir -p $fileDir

fileSize=$(wc -c "$filePath" | awk '{print $1}')

if [ $fileSize -gt 1000 ]
then
    echo "$fileName already downloaded"
    exit 0
fi

if [[ -z "$CI_JOB_TOKEN" ]]; then 
    if [[ -z "$3" ]]; then
        echo "! ! ! !"
        echo "No private token provided - create personal access token with read_api scope"
        echo "https://docs.gitlab.com/ee/user/profile/personal_access_tokens.html"
        echo "and pass the token as the second argument to this script"
        echo "! ! ! !"
        echo " "
        exit 2
    fi

    curl -f --header "PRIVATE-TOKEN: $3" https://gitlab.goalsport.software/api/v4/projects/7/packages/generic/test-videos/0.1/$fileName --output $filePath
else
    curl -f --header "JOB-TOKEN: $CI_JOB_TOKEN" https://gitlab.goalsport.software/api/v4/projects/7/packages/generic/test-videos/0.1/$fileName --output $filePath
fi

fileSize=$(wc -c "$filePath" | awk '{print $1}')

# Have to check file size because GitLab API returns 200 OK even if file doesn't exist
if [ $fileSize -lt 1000 ]
then
    exit 1
fi

exit 0
