{"BUILD_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "CMAKE_EXECUTABLE": "/usr/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/CMakeLists.txt", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6Config.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtInstallPaths.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6Targets.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtFeature.cmake", "/usr/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake", "/usr/share/cmake-3.28/Modules/FindOpenCL.cmake", "/usr/share/cmake-3.28/Modules/CheckSymbolExists.cmake", "/usr/share/cmake-3.28/Modules/CMakePushCheckState.cmake", "/usr/share/cmake-3.28/Modules/FindPackageHandleStandardArgs.cmake", "/usr/share/cmake-3.28/Modules/FindPackageMessage.cmake", "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/.qtc/package-manager/auto-setup.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6Config.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtInstallPaths.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6Targets.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtFeature.cmake", "/usr/share/cmake-3.28/Modules/CheckCXXCompilerFlag.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6Core/Qt6CoreConfigureFileTemplate.in", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6Core/Qt6CoreResourceInit.in.cpp"], "CMAKE_SOURCE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing", "DEP_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/deps", "DEP_FILE_RULE_NAME": "GSS_libgoal_avrouter_autogen/timestamp", "HEADERS": [["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avr_helper.h", "MU", "AWBG43YATH/moc_avr_helper.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avrouter.h", "MU", "AWBG43YATH/moc_avrouter.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/fpssync.h", "MU", "AWBG43YATH/moc_fpssync.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveinputmanager.h", "MU", "AWBG43YATH/moc_liveinputmanager.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveoutputmanager.h", "MU", "AWBG43YATH/moc_liveoutputmanager.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/openclprocessor.h", "MU", "AWBG43YATH/moc_openclprocessor.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videoformatter.h", "MU", "AWBG43YATH/moc_videoformatter.cpp", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videooutputprocess.h", "MU", "AWBG43YATH/moc_videooutputprocess.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CONCURRENT_LIB", "QT_CORE_LIB", "QT_GUI_LIB", "QT_MULTIMEDIA_LIB", "QT_NETWORK_LIB", "QT_WIDGETS_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video", "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include", "/opt/Qt/6.8.3/gcc_64/include/QtCore", "/opt/Qt/6.8.3/gcc_64/include", "/opt/Qt/6.8.3/gcc_64/mkspecs/linux-g++", "/opt/Qt/6.8.3/gcc_64/include/QtMultimedia", "/opt/Qt/6.8.3/gcc_64/include/QtGui", "/opt/Qt/6.8.3/gcc_64/include/QtNetwork", "/opt/Qt/6.8.3/gcc_64/include/QtConcurrent", "/opt/Qt/6.8.3/gcc_64/include/QtCore/6.8.3", "/opt/Qt/6.8.3/gcc_64/include/QtCore/6.8.3/QtCore", "/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/6.8.3", "/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/6.8.3/QtMultimedia", "/opt/Qt/6.8.3/gcc_64/include/QtGui/6.8.3", "/opt/Qt/6.8.3/gcc_64/include/QtGui/6.8.3/QtGui", "/opt/Qt/6.8.3/gcc_64/include/QtWidgets", "/usr/include", "/usr/include/x86_64-linux-gnu", "/usr/include/c++/13", "/usr/include/x86_64-linux-gnu/c++/13", "/usr/include/c++/13/backward", "/usr/lib/llvm-18/lib/clang/18/include", "/usr/local/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/clang++", "-std=gnu++17", "-dM", "-E", "-c", "/usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": ["/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp", "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp"], "MULTI_CONFIG": false, "PARALLEL": 16, "PARSE_CACHE_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/opt/Qt/6.8.3/gcc_64/libexec/moc", "QT_UIC_EXECUTABLE": "/opt/Qt/6.8.3/gcc_64/libexec/uic", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 8, "SETTINGS_FILE": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avrouter.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/fpssync.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveinputmanager.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveoutputmanager.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/openclprocessor.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videoformatter.cpp", "MU", null], ["/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videooutputprocess.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": ["/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter.cpp", "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp"], "UIC_UI_FILES": [], "VERBOSITY": 0}