# Additional clean files
cmake_minimum_required(VERSION 3.16)

if("${CONFIG}" STREQUAL "" OR "${CONFIG}" STREQUAL "Debug")
  file(REMOVE_RECURSE
  "CMakeFiles/av-routing-and-mixing_autogen.dir/AutogenUsed.txt"
  "CMakeFiles/av-routing-and-mixing_autogen.dir/ParseCache.txt"
  "av-routing-and-mixing_autogen"
  "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen.dir/AutogenUsed.txt"
  "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen.dir/ParseCache.txt"
  "libgoal-avrouter/GSS_libgoal_avrouter_autogen"
  "libgoal-devices/CMakeFiles/GSS_libgoal_devices_autogen.dir/AutogenUsed.txt"
  "libgoal-devices/CMakeFiles/GSS_libgoal_devices_autogen.dir/ParseCache.txt"
  "libgoal-devices/GSS_libgoal_devices_autogen"
  "libgoal-libav/CMakeFiles/GSS_libgoal_libav_autogen.dir/AutogenUsed.txt"
  "libgoal-libav/CMakeFiles/GSS_libgoal_libav_autogen.dir/ParseCache.txt"
  "libgoal-libav/GSS_libgoal_libav_autogen"
  "libgoal-ndi/CMakeFiles/GSS_libgoal_ndi_autogen.dir/AutogenUsed.txt"
  "libgoal-ndi/CMakeFiles/GSS_libgoal_ndi_autogen.dir/ParseCache.txt"
  "libgoal-ndi/GSS_libgoal_ndi_autogen"
  "libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app_autogen.dir/AutogenUsed.txt"
  "libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app_autogen.dir/ParseCache.txt"
  "libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen"
  "libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app_autogen.dir/AutogenUsed.txt"
  "libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app_autogen.dir/ParseCache.txt"
  "libgoal-ndi/recv_test_app/recv_test_app_autogen"
  "libgoal-sdi/CMakeFiles/GSS_libgoal_sdi_autogen.dir/AutogenUsed.txt"
  "libgoal-sdi/CMakeFiles/GSS_libgoal_sdi_autogen.dir/ParseCache.txt"
  "libgoal-sdi/GSS_libgoal_sdi_autogen"
  "libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video_autogen.dir/AutogenUsed.txt"
  "libgoal-smemory-video/CMakeFiles/GSS_libgoal_smemory-video_autogen.dir/ParseCache.txt"
  "libgoal-smemory-video/GSS_libgoal_smemory-video_autogen"
  "libgoal-utils/CMakeFiles/GSS_libgoal_utils_autogen.dir/AutogenUsed.txt"
  "libgoal-utils/CMakeFiles/GSS_libgoal_utils_autogen.dir/ParseCache.txt"
  "libgoal-utils/GSS_libgoal_utils_autogen"
  "libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe_autogen.dir/AutogenUsed.txt"
  "libgoal-videoframe/CMakeFiles/GSS_libgoal_videoframe_autogen.dir/ParseCache.txt"
  "libgoal-videoframe/GSS_libgoal_videoframe_autogen"
  "libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen.dir/AutogenUsed.txt"
  "libgoal-widgets/CMakeFiles/GSS_libgoal_widgets_autogen.dir/ParseCache.txt"
  "libgoal-widgets/GSS_libgoal_widgets_autogen"
  )
endif()
