# Generated by CMake. Changes will be overwritten.
/home/<USER>/PROJECTS/av-routing-and-mixing/mainwindow.cpp
 uic:./ui_mainwindow.h
/home/<USER>/PROJECTS/av-routing-and-mixing/main.cpp
/home/<USER>/PROJECTS/av-routing-and-mixing/mainwindow.h
 mmc:Q_OBJECT
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/av-routing-and-mixing_autogen/moc_predefs.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avr_helper.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/avrouter.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/fpssync.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveinputmanager.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/liveoutputmanager.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/openclprocessor.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include/avrouter/videooutputprocess.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/imageloader.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavbuffer.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavdecoder.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavfilter.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavhelpers.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavplayerdirector.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libavprotocolcommons.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include/libav/libswshelper.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndifinder.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndireceiver.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include/ndi/ndisender.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPI.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIConfiguration.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDeckControl.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIDiscovery.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPIModes.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/DeckLinkAPITypes.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/DeckLinkAPI/LinuxCOM.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/capture.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/decklinknotificationcallback.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include/outputsdi.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideoplayer.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include/smemory-video/sharedmemoryvideosender.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/elapsedtimereference.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/freqsync.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsharedmemory.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/goalsystemsemaphore.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/memorypool.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/qthreadedobject.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/threadsafequeue.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/timedqueue.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include/universalplayer.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/colorcorrection.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/framemetadata.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/qvideoframehelper.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include/videoframe.h
 mdp:/home/<USER>/PROJECTS/av-routing-and-mixing/mainwindow.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/QtConcurrent
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/QtConcurrentDepends
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtaskbuilder.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrent_global.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentcompilertest.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentexports.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentfilter.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentfilterkernel.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentfunctionwrappers.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentiteratekernel.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentmap.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentmapkernel.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentmedian.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentreducekernel.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentrun.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentrunbase.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentstoredfunctioncall.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrenttask.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentthreadengine.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtConcurrent/qtconcurrentversion.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QBuffer
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QByteArray
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QCryptographicHash
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QDataStream
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QDateTime
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QDeadlineTimer
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QDebug
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QDir
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QElapsedTimer
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QEvent
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QFileInfo
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QHash
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QIODevice
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QJsonArray
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QJsonDocument
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QJsonObject
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QJsonValue
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QList
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QMap
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QMargins
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QMutex
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QMutexLocker
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QObject
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QPair
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QQueue
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QReadWriteLock
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QRect
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QRectF
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QSemaphore
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QSettings
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QSharedMemory
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QSize
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QSizeF
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QString
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QStringList
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QThread
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QTimer
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QUuid
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QVariant
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QVector
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QtCore
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QtCoreDepends
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QtDebug
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QtGlobal
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/QtMath
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/q20algorithm.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/q20chrono.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/q20functional.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/q20iterator.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/q20map.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/q20memory.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/q20type_traits.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/q20utility.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/q20vector.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/q23functional.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/q23utility.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/q26numeric.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qabstractanimation.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qabstracteventdispatcher.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qabstractitemmodel.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qabstractnativeeventfilter.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qabstractproxymodel.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qalgorithms.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qanimationgroup.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qanystringview.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qapplicationstatic.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qarraydata.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qarraydataops.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qarraydatapointer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qassert.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qassociativeiterable.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qatomic.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qatomic_cxx11.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qatomicscopedvaluerollback.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qbasicatomic.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qbasictimer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qbindingstorage.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qbitarray.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qbuffer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qbytearray.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qbytearrayalgorithms.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qbytearraylist.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qbytearraymatcher.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qbytearrayview.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcache.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcalendar.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcborarray.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcborcommon.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcbormap.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcborstream.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcborstreamreader.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcborstreamwriter.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcborvalue.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qchar.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qchronotimer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcollator.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcommandlineoption.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcommandlineparser.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcompare.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcompare_impl.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcomparehelpers.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcompilerdetection.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qconcatenatetablesproxymodel.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qconfig.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qconstructormacros.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcontainerfwd.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcontainerinfo.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcontainertools_impl.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcontiguouscache.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcoreapplication.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcoreapplication_platform.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcoreevent.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qcryptographichash.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qdarwinhelpers.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qdatastream.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qdatetime.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qdeadlinetimer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qdebug.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qdir.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qdiriterator.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qdirlisting.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qeasingcurve.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qelapsedtimer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qendian.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qeventloop.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qexception.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qexceptionhandling.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfactoryinterface.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfile.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfiledevice.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfileinfo.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfileselector.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfilesystemwatcher.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qflags.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfloat16.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qforeach.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfunctionaltools_impl.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfunctionpointer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfuture.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfuture_impl.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfutureinterface.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfuturesynchronizer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qfuturewatcher.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qgenericatomic.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qglobal.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qglobalstatic.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qhash.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qhashfunctions.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qidentityproxymodel.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qiodevice.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qiodevicebase.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qitemselectionmodel.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qiterable.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qiterator.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qjsonarray.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qjsondocument.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qjsonobject.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qjsonvalue.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qlatin1stringmatcher.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qlatin1stringview.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qlibrary.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qlibraryinfo.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qline.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qlist.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qlocale.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qlockfile.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qlogging.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qloggingcategory.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qmalloc.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qmap.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qmargins.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qmath.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qmessageauthenticationcode.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qmetacontainer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qmetaobject.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qmetatype.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qmimedata.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qmimedatabase.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qmimetype.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qminmax.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qmutex.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qnamespace.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qnativeinterface.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qnumeric.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qobject.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qobject_impl.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qobjectcleanuphandler.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qobjectdefs.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qobjectdefs_impl.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qoperatingsystemversion.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qoverload.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qpair.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qparallelanimationgroup.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qpauseanimation.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qpermissions.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qplugin.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qpluginloader.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qpoint.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qpointer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qprocess.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qprocessordetection.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qpromise.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qproperty.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qpropertyanimation.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qpropertyprivate.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qqueue.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qrandom.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qreadwritelock.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qrect.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qrefcount.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qregularexpression.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qresource.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qresultstore.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qrunnable.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsavefile.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qscopedpointer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qscopedvaluerollback.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qscopeguard.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsemaphore.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsequentialanimationgroup.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsequentialiterable.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qset.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsettings.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qshareddata.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qshareddata_impl.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsharedmemory.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsharedpointer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsharedpointer_impl.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsignalmapper.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsimd.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsize.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsocketnotifier.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsortfilterproxymodel.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qspan.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstack.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstandardpaths.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstaticlatin1stringmatcher.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstdlibdetection.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstorageinfo.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstring.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstringalgorithms.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstringbuilder.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstringconverter.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstringconverter_base.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstringfwd.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstringlist.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstringlistmodel.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstringliteral.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstringmatcher.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstringtokenizer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qstringview.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qswap.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsysinfo.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsystemdetection.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qsystemsemaphore.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtaggedpointer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtclasshelpermacros.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtconfiginclude.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtconfigmacros.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtcore-config.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtcoreexports.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtcoreversion.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtdeprecationmarkers.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtemporarydir.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtemporaryfile.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtenvironmentvariables.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtextboundaryfinder.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtextstream.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qthread.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qthreadpool.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qthreadstorage.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtimeline.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtimer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtimezone.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtipccommon.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtmetamacros.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtnoop.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtpreprocessorsupport.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtranslator.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtransposeproxymodel.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtresource.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtsan_impl.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtsymbolmacros.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qttranslation.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qttypetraits.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtversion.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtversionchecks.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtypeinfo.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtyperevision.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qtypes.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qurl.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qurlquery.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qutf8stringview.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/quuid.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qvariant.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qvariantanimation.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qvarianthash.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qvariantlist.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qvariantmap.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qvarlengtharray.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qvector.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qversionnumber.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qversiontagging.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qwaitcondition.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qwineventnotifier.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qxmlstream.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qxpfunctional.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qxptype_traits.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtCore/qyieldcpu.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/QImage
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/QMatrix4x4
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/QTransform
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qaccessible.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qaccessible_base.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qaction.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qbitmap.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qbrush.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qcolor.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qcursor.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qevent.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qeventpoint.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qfont.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qfontinfo.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qfontmetrics.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qgenericmatrix.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qguiapplication.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qguiapplication_platform.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qicon.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qimage.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qinputdevice.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qinputmethod.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qkeysequence.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qmatrix4x4.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qpaintdevice.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qpalette.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qpixelformat.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qpixmap.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qpointingdevice.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qpolygon.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qquaternion.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qregion.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qrgb.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qrgba64.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qscreen.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qscreen_platform.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qsurface.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qsurfaceformat.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qtgui-config.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qtguiexports.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qtguiglobal.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qtransform.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qvector2d.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qvector3d.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qvector4d.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qvectornd.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qwindow.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtGui/qwindowdefs.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/QAbstractVideoBuffer
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/QAudioBuffer
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/QAudioFormat
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/QVideoFrame
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/QVideoSink
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/qabstractvideobuffer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/qaudio.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/qaudiobuffer.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/qaudioformat.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/qtaudio.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/qtmultimedia-config.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/qtmultimediaexports.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/qtmultimediaglobal.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/qtvideo.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/qvideoframe.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/qvideoframeformat.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/qvideosink.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtNetwork/qtnetwork-config.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtNetwork/qtnetworkexports.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtNetwork/qtnetworkglobal.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/QQmlContext
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qjsnumbercoercion.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qjsprimitivevalue.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qjsvalue.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qqml.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qqmlcomponent.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qqmlcontext.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qqmldebug.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qqmlerror.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qqmlinfo.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qqmllist.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qqmlparserstatus.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qqmlprivate.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qqmlpropertyvaluesource.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qqmlregistration.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qtqml-config.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qtqmlexports.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQml/qtqmlglobal.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQmlIntegration/qqmlintegration.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQuick/QQuickItem
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQuick/QQuickView
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQuick/qquickitem.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQuick/qquickview.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQuick/qquickwindow.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQuick/qsggeometry.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQuick/qsgnode.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQuick/qsgrendererinterface.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQuick/qtquick-config.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQuick/qtquickexports.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtQuick/qtquickglobal.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtWidgets/QMainWindow
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtWidgets/qmainwindow.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtWidgets/qsizepolicy.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtWidgets/qtabwidget.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtWidgets/qtwidgets-config.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtWidgets/qtwidgetsexports.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:/opt/Qt/6.8.3/gcc_64/include/QtWidgets/qwidget.h
 mdp:/opt/Qt/6.8.3/gcc_64/mkspecs/common/posix/qplatformdefs.h
 mdp:/opt/Qt/6.8.3/gcc_64/mkspecs/linux-g++/qplatformdefs.h
 mdp:/usr/include/CL/cl.h
 mdp:/usr/include/CL/cl_ext.h
 mdp:/usr/include/CL/cl_gl.h
 mdp:/usr/include/CL/cl_platform.h
 mdp:/usr/include/CL/cl_version.h
 mdp:/usr/include/CL/opencl.h
 mdp:/usr/include/CL/opencl.hpp
 mdp:/usr/include/alloca.h
 mdp:/usr/include/asm-generic/bitsperlong.h
 mdp:/usr/include/asm-generic/errno-base.h
 mdp:/usr/include/asm-generic/errno.h
 mdp:/usr/include/asm-generic/ioctl.h
 mdp:/usr/include/asm-generic/ioctls.h
 mdp:/usr/include/asm-generic/posix_types.h
 mdp:/usr/include/asm-generic/socket.h
 mdp:/usr/include/asm-generic/sockios.h
 mdp:/usr/include/assert.h
 mdp:/usr/include/c++/13/algorithm
 mdp:/usr/include/c++/13/array
 mdp:/usr/include/c++/13/atomic
 mdp:/usr/include/c++/13/backward/auto_ptr.h
 mdp:/usr/include/c++/13/backward/binders.h
 mdp:/usr/include/c++/13/bit
 mdp:/usr/include/c++/13/bits/algorithmfwd.h
 mdp:/usr/include/c++/13/bits/align.h
 mdp:/usr/include/c++/13/bits/alloc_traits.h
 mdp:/usr/include/c++/13/bits/allocated_ptr.h
 mdp:/usr/include/c++/13/bits/allocator.h
 mdp:/usr/include/c++/13/bits/atomic_base.h
 mdp:/usr/include/c++/13/bits/atomic_futex.h
 mdp:/usr/include/c++/13/bits/atomic_lockfree_defines.h
 mdp:/usr/include/c++/13/bits/basic_ios.h
 mdp:/usr/include/c++/13/bits/basic_ios.tcc
 mdp:/usr/include/c++/13/bits/basic_string.h
 mdp:/usr/include/c++/13/bits/basic_string.tcc
 mdp:/usr/include/c++/13/bits/char_traits.h
 mdp:/usr/include/c++/13/bits/charconv.h
 mdp:/usr/include/c++/13/bits/chrono.h
 mdp:/usr/include/c++/13/bits/codecvt.h
 mdp:/usr/include/c++/13/bits/concept_check.h
 mdp:/usr/include/c++/13/bits/cpp_type_traits.h
 mdp:/usr/include/c++/13/bits/cxxabi_forced.h
 mdp:/usr/include/c++/13/bits/cxxabi_init_exception.h
 mdp:/usr/include/c++/13/bits/enable_special_members.h
 mdp:/usr/include/c++/13/bits/erase_if.h
 mdp:/usr/include/c++/13/bits/exception.h
 mdp:/usr/include/c++/13/bits/exception_defines.h
 mdp:/usr/include/c++/13/bits/exception_ptr.h
 mdp:/usr/include/c++/13/bits/fs_dir.h
 mdp:/usr/include/c++/13/bits/fs_fwd.h
 mdp:/usr/include/c++/13/bits/fs_ops.h
 mdp:/usr/include/c++/13/bits/fs_path.h
 mdp:/usr/include/c++/13/bits/functexcept.h
 mdp:/usr/include/c++/13/bits/functional_hash.h
 mdp:/usr/include/c++/13/bits/hash_bytes.h
 mdp:/usr/include/c++/13/bits/hashtable.h
 mdp:/usr/include/c++/13/bits/hashtable_policy.h
 mdp:/usr/include/c++/13/bits/invoke.h
 mdp:/usr/include/c++/13/bits/ios_base.h
 mdp:/usr/include/c++/13/bits/istream.tcc
 mdp:/usr/include/c++/13/bits/list.tcc
 mdp:/usr/include/c++/13/bits/locale_classes.h
 mdp:/usr/include/c++/13/bits/locale_classes.tcc
 mdp:/usr/include/c++/13/bits/locale_conv.h
 mdp:/usr/include/c++/13/bits/locale_facets.h
 mdp:/usr/include/c++/13/bits/locale_facets.tcc
 mdp:/usr/include/c++/13/bits/locale_facets_nonio.h
 mdp:/usr/include/c++/13/bits/locale_facets_nonio.tcc
 mdp:/usr/include/c++/13/bits/localefwd.h
 mdp:/usr/include/c++/13/bits/memory_resource.h
 mdp:/usr/include/c++/13/bits/memoryfwd.h
 mdp:/usr/include/c++/13/bits/move.h
 mdp:/usr/include/c++/13/bits/nested_exception.h
 mdp:/usr/include/c++/13/bits/new_allocator.h
 mdp:/usr/include/c++/13/bits/node_handle.h
 mdp:/usr/include/c++/13/bits/ostream.tcc
 mdp:/usr/include/c++/13/bits/ostream_insert.h
 mdp:/usr/include/c++/13/bits/parse_numbers.h
 mdp:/usr/include/c++/13/bits/postypes.h
 mdp:/usr/include/c++/13/bits/predefined_ops.h
 mdp:/usr/include/c++/13/bits/ptr_traits.h
 mdp:/usr/include/c++/13/bits/quoted_string.h
 mdp:/usr/include/c++/13/bits/random.h
 mdp:/usr/include/c++/13/bits/random.tcc
 mdp:/usr/include/c++/13/bits/range_access.h
 mdp:/usr/include/c++/13/bits/refwrap.h
 mdp:/usr/include/c++/13/bits/requires_hosted.h
 mdp:/usr/include/c++/13/bits/shared_ptr.h
 mdp:/usr/include/c++/13/bits/shared_ptr_atomic.h
 mdp:/usr/include/c++/13/bits/shared_ptr_base.h
 mdp:/usr/include/c++/13/bits/specfun.h
 mdp:/usr/include/c++/13/bits/sstream.tcc
 mdp:/usr/include/c++/13/bits/std_abs.h
 mdp:/usr/include/c++/13/bits/std_function.h
 mdp:/usr/include/c++/13/bits/std_mutex.h
 mdp:/usr/include/c++/13/bits/std_thread.h
 mdp:/usr/include/c++/13/bits/stl_algo.h
 mdp:/usr/include/c++/13/bits/stl_algobase.h
 mdp:/usr/include/c++/13/bits/stl_bvector.h
 mdp:/usr/include/c++/13/bits/stl_construct.h
 mdp:/usr/include/c++/13/bits/stl_function.h
 mdp:/usr/include/c++/13/bits/stl_heap.h
 mdp:/usr/include/c++/13/bits/stl_iterator.h
 mdp:/usr/include/c++/13/bits/stl_iterator_base_funcs.h
 mdp:/usr/include/c++/13/bits/stl_iterator_base_types.h
 mdp:/usr/include/c++/13/bits/stl_list.h
 mdp:/usr/include/c++/13/bits/stl_map.h
 mdp:/usr/include/c++/13/bits/stl_multimap.h
 mdp:/usr/include/c++/13/bits/stl_numeric.h
 mdp:/usr/include/c++/13/bits/stl_pair.h
 mdp:/usr/include/c++/13/bits/stl_raw_storage_iter.h
 mdp:/usr/include/c++/13/bits/stl_relops.h
 mdp:/usr/include/c++/13/bits/stl_tempbuf.h
 mdp:/usr/include/c++/13/bits/stl_tree.h
 mdp:/usr/include/c++/13/bits/stl_uninitialized.h
 mdp:/usr/include/c++/13/bits/stl_vector.h
 mdp:/usr/include/c++/13/bits/stream_iterator.h
 mdp:/usr/include/c++/13/bits/streambuf.tcc
 mdp:/usr/include/c++/13/bits/streambuf_iterator.h
 mdp:/usr/include/c++/13/bits/string_view.tcc
 mdp:/usr/include/c++/13/bits/stringfwd.h
 mdp:/usr/include/c++/13/bits/this_thread_sleep.h
 mdp:/usr/include/c++/13/bits/uniform_int_dist.h
 mdp:/usr/include/c++/13/bits/unique_lock.h
 mdp:/usr/include/c++/13/bits/unique_ptr.h
 mdp:/usr/include/c++/13/bits/unordered_map.h
 mdp:/usr/include/c++/13/bits/uses_allocator.h
 mdp:/usr/include/c++/13/bits/uses_allocator_args.h
 mdp:/usr/include/c++/13/bits/utility.h
 mdp:/usr/include/c++/13/bits/vector.tcc
 mdp:/usr/include/c++/13/cassert
 mdp:/usr/include/c++/13/cctype
 mdp:/usr/include/c++/13/cerrno
 mdp:/usr/include/c++/13/chrono
 mdp:/usr/include/c++/13/climits
 mdp:/usr/include/c++/13/clocale
 mdp:/usr/include/c++/13/cmath
 mdp:/usr/include/c++/13/codecvt
 mdp:/usr/include/c++/13/compare
 mdp:/usr/include/c++/13/condition_variable
 mdp:/usr/include/c++/13/cstddef
 mdp:/usr/include/c++/13/cstdint
 mdp:/usr/include/c++/13/cstdio
 mdp:/usr/include/c++/13/cstdlib
 mdp:/usr/include/c++/13/cstring
 mdp:/usr/include/c++/13/ctime
 mdp:/usr/include/c++/13/cwchar
 mdp:/usr/include/c++/13/cwctype
 mdp:/usr/include/c++/13/debug/assertions.h
 mdp:/usr/include/c++/13/debug/debug.h
 mdp:/usr/include/c++/13/exception
 mdp:/usr/include/c++/13/ext/aligned_buffer.h
 mdp:/usr/include/c++/13/ext/alloc_traits.h
 mdp:/usr/include/c++/13/ext/atomicity.h
 mdp:/usr/include/c++/13/ext/concurrence.h
 mdp:/usr/include/c++/13/ext/numeric_traits.h
 mdp:/usr/include/c++/13/ext/string_conversions.h
 mdp:/usr/include/c++/13/ext/type_traits.h
 mdp:/usr/include/c++/13/filesystem
 mdp:/usr/include/c++/13/functional
 mdp:/usr/include/c++/13/future
 mdp:/usr/include/c++/13/initializer_list
 mdp:/usr/include/c++/13/iomanip
 mdp:/usr/include/c++/13/ios
 mdp:/usr/include/c++/13/iosfwd
 mdp:/usr/include/c++/13/iostream
 mdp:/usr/include/c++/13/istream
 mdp:/usr/include/c++/13/iterator
 mdp:/usr/include/c++/13/limits
 mdp:/usr/include/c++/13/list
 mdp:/usr/include/c++/13/locale
 mdp:/usr/include/c++/13/map
 mdp:/usr/include/c++/13/memory
 mdp:/usr/include/c++/13/mutex
 mdp:/usr/include/c++/13/new
 mdp:/usr/include/c++/13/numeric
 mdp:/usr/include/c++/13/optional
 mdp:/usr/include/c++/13/ostream
 mdp:/usr/include/c++/13/pstl/execution_defs.h
 mdp:/usr/include/c++/13/pstl/glue_algorithm_defs.h
 mdp:/usr/include/c++/13/pstl/glue_memory_defs.h
 mdp:/usr/include/c++/13/pstl/glue_numeric_defs.h
 mdp:/usr/include/c++/13/random
 mdp:/usr/include/c++/13/ratio
 mdp:/usr/include/c++/13/sstream
 mdp:/usr/include/c++/13/stdexcept
 mdp:/usr/include/c++/13/streambuf
 mdp:/usr/include/c++/13/string
 mdp:/usr/include/c++/13/string_view
 mdp:/usr/include/c++/13/system_error
 mdp:/usr/include/c++/13/thread
 mdp:/usr/include/c++/13/tr1/bessel_function.tcc
 mdp:/usr/include/c++/13/tr1/beta_function.tcc
 mdp:/usr/include/c++/13/tr1/ell_integral.tcc
 mdp:/usr/include/c++/13/tr1/exp_integral.tcc
 mdp:/usr/include/c++/13/tr1/gamma.tcc
 mdp:/usr/include/c++/13/tr1/hypergeometric.tcc
 mdp:/usr/include/c++/13/tr1/legendre_function.tcc
 mdp:/usr/include/c++/13/tr1/modified_bessel_func.tcc
 mdp:/usr/include/c++/13/tr1/poly_hermite.tcc
 mdp:/usr/include/c++/13/tr1/poly_laguerre.tcc
 mdp:/usr/include/c++/13/tr1/riemann_zeta.tcc
 mdp:/usr/include/c++/13/tr1/special_function_util.h
 mdp:/usr/include/c++/13/tuple
 mdp:/usr/include/c++/13/type_traits
 mdp:/usr/include/c++/13/typeinfo
 mdp:/usr/include/c++/13/unordered_map
 mdp:/usr/include/c++/13/utility
 mdp:/usr/include/c++/13/variant
 mdp:/usr/include/c++/13/vector
 mdp:/usr/include/ctype.h
 mdp:/usr/include/dirent.h
 mdp:/usr/include/endian.h
 mdp:/usr/include/errno.h
 mdp:/usr/include/fcntl.h
 mdp:/usr/include/features-time64.h
 mdp:/usr/include/features.h
 mdp:/usr/include/grp.h
 mdp:/usr/include/inttypes.h
 mdp:/usr/include/libintl.h
 mdp:/usr/include/limits.h
 mdp:/usr/include/linux/errno.h
 mdp:/usr/include/linux/falloc.h
 mdp:/usr/include/linux/ioctl.h
 mdp:/usr/include/linux/limits.h
 mdp:/usr/include/linux/posix_types.h
 mdp:/usr/include/linux/stddef.h
 mdp:/usr/include/locale.h
 mdp:/usr/include/math.h
 mdp:/usr/include/netinet/in.h
 mdp:/usr/include/pthread.h
 mdp:/usr/include/pwd.h
 mdp:/usr/include/sched.h
 mdp:/usr/include/signal.h
 mdp:/usr/include/stdc-predef.h
 mdp:/usr/include/stdint.h
 mdp:/usr/include/stdio.h
 mdp:/usr/include/stdlib.h
 mdp:/usr/include/string.h
 mdp:/usr/include/strings.h
 mdp:/usr/include/time.h
 mdp:/usr/include/unistd.h
 mdp:/usr/include/wchar.h
 mdp:/usr/include/wctype.h
 mdp:/usr/include/x86_64-linux-gnu/asm/bitsperlong.h
 mdp:/usr/include/x86_64-linux-gnu/asm/errno.h
 mdp:/usr/include/x86_64-linux-gnu/asm/ioctl.h
 mdp:/usr/include/x86_64-linux-gnu/asm/ioctls.h
 mdp:/usr/include/x86_64-linux-gnu/asm/posix_types.h
 mdp:/usr/include/x86_64-linux-gnu/asm/posix_types_64.h
 mdp:/usr/include/x86_64-linux-gnu/asm/socket.h
 mdp:/usr/include/x86_64-linux-gnu/asm/sockios.h
 mdp:/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 mdp:/usr/include/x86_64-linux-gnu/bits/byteswap.h
 mdp:/usr/include/x86_64-linux-gnu/bits/confname.h
 mdp:/usr/include/x86_64-linux-gnu/bits/cpu-set.h
 mdp:/usr/include/x86_64-linux-gnu/bits/dirent.h
 mdp:/usr/include/x86_64-linux-gnu/bits/dirent_ext.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endian.h
 mdp:/usr/include/x86_64-linux-gnu/bits/endianness.h
 mdp:/usr/include/x86_64-linux-gnu/bits/environments.h
 mdp:/usr/include/x86_64-linux-gnu/bits/errno.h
 mdp:/usr/include/x86_64-linux-gnu/bits/fcntl-linux.h
 mdp:/usr/include/x86_64-linux-gnu/bits/fcntl.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn-common.h
 mdp:/usr/include/x86_64-linux-gnu/bits/floatn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/flt-eval-method.h
 mdp:/usr/include/x86_64-linux-gnu/bits/fp-fast.h
 mdp:/usr/include/x86_64-linux-gnu/bits/fp-logb.h
 mdp:/usr/include/x86_64-linux-gnu/bits/getopt_core.h
 mdp:/usr/include/x86_64-linux-gnu/bits/getopt_posix.h
 mdp:/usr/include/x86_64-linux-gnu/bits/in.h
 mdp:/usr/include/x86_64-linux-gnu/bits/ioctl-types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/ioctls.h
 mdp:/usr/include/x86_64-linux-gnu/bits/ipc-perm.h
 mdp:/usr/include/x86_64-linux-gnu/bits/ipc.h
 mdp:/usr/include/x86_64-linux-gnu/bits/ipctypes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/iscanonical.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 mdp:/usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h
 mdp:/usr/include/x86_64-linux-gnu/bits/local_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/locale.h
 mdp:/usr/include/x86_64-linux-gnu/bits/long-double.h
 mdp:/usr/include/x86_64-linux-gnu/bits/math-vector.h
 mdp:/usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h
 mdp:/usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h
 mdp:/usr/include/x86_64-linux-gnu/bits/mathcalls.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix1_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix2_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/posix_opt.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sched.h
 mdp:/usr/include/x86_64-linux-gnu/bits/select.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sem.h
 mdp:/usr/include/x86_64-linux-gnu/bits/setjmp.h
 mdp:/usr/include/x86_64-linux-gnu/bits/shm.h
 mdp:/usr/include/x86_64-linux-gnu/bits/shmlba.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigaction.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigcontext.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigevent-consts.h
 mdp:/usr/include/x86_64-linux-gnu/bits/siginfo-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/siginfo-consts-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/siginfo-consts.h
 mdp:/usr/include/x86_64-linux-gnu/bits/signal_ext.h
 mdp:/usr/include/x86_64-linux-gnu/bits/signum-arch.h
 mdp:/usr/include/x86_64-linux-gnu/bits/signum-generic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigstack.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigstksz.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sigthread.h
 mdp:/usr/include/x86_64-linux-gnu/bits/sockaddr.h
 mdp:/usr/include/x86_64-linux-gnu/bits/socket.h
 mdp:/usr/include/x86_64-linux-gnu/bits/socket_type.h
 mdp:/usr/include/x86_64-linux-gnu/bits/ss_flags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stat.h
 mdp:/usr/include/x86_64-linux-gnu/bits/statx-generic.h
 mdp:/usr/include/x86_64-linux-gnu/bits/statx.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-least.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 mdp:/usr/include/x86_64-linux-gnu/bits/struct_stat.h
 mdp:/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time.h
 mdp:/usr/include/x86_64-linux-gnu/bits/time64.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timesize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/timex.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/__sigval_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/error_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/idtype_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sig_atomic_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigevent_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/siginfo_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/sigval_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/stack_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_iovec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_osockaddr.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_semid64_ds.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_semid_ds.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_shmid64_ds.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_shmid_ds.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_sigstack.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_statx.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_statx_timestamp.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/time_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 mdp:/usr/include/x86_64-linux-gnu/bits/typesizes.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 mdp:/usr/include/x86_64-linux-gnu/bits/uio_lim.h
 mdp:/usr/include/x86_64-linux-gnu/bits/unistd_ext.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitflags.h
 mdp:/usr/include/x86_64-linux-gnu/bits/waitstatus.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wctype-wchar.h
 mdp:/usr/include/x86_64-linux-gnu/bits/wordsize.h
 mdp:/usr/include/x86_64-linux-gnu/bits/xopen_lim.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/atomic_word.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/c++allocator.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/c++config.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/c++locale.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/cpu_defines.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/ctype_base.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/ctype_inline.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/error_constants.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/gthr-default.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/gthr.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/messages_members.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/opt_random.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/os_defines.h
 mdp:/usr/include/x86_64-linux-gnu/c++/13/bits/time_members.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 mdp:/usr/include/x86_64-linux-gnu/gnu/stubs.h
 mdp:/usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
 mdp:/usr/include/x86_64-linux-gnu/libavcodec/codec.h
 mdp:/usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
 mdp:/usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
 mdp:/usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
 mdp:/usr/include/x86_64-linux-gnu/libavcodec/defs.h
 mdp:/usr/include/x86_64-linux-gnu/libavcodec/packet.h
 mdp:/usr/include/x86_64-linux-gnu/libavcodec/version.h
 mdp:/usr/include/x86_64-linux-gnu/libavcodec/version_major.h
 mdp:/usr/include/x86_64-linux-gnu/libavfilter/avfilter.h
 mdp:/usr/include/x86_64-linux-gnu/libavfilter/buffersink.h
 mdp:/usr/include/x86_64-linux-gnu/libavfilter/buffersrc.h
 mdp:/usr/include/x86_64-linux-gnu/libavfilter/version.h
 mdp:/usr/include/x86_64-linux-gnu/libavfilter/version_major.h
 mdp:/usr/include/x86_64-linux-gnu/libavformat/avformat.h
 mdp:/usr/include/x86_64-linux-gnu/libavformat/avio.h
 mdp:/usr/include/x86_64-linux-gnu/libavformat/version.h
 mdp:/usr/include/x86_64-linux-gnu/libavformat/version_major.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/attributes.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/avassert.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/avconfig.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/avutil.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/bswap.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/buffer.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/common.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/dict.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/error.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/frame.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/imgutils.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/intfloat.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/intreadwrite.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/log.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/macros.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/mathematics.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/mem.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/opt.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/pixdesc.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/rational.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
 mdp:/usr/include/x86_64-linux-gnu/libavutil/version.h
 mdp:/usr/include/x86_64-linux-gnu/libswresample/swresample.h
 mdp:/usr/include/x86_64-linux-gnu/libswresample/version.h
 mdp:/usr/include/x86_64-linux-gnu/libswresample/version_major.h
 mdp:/usr/include/x86_64-linux-gnu/libswscale/swscale.h
 mdp:/usr/include/x86_64-linux-gnu/libswscale/version.h
 mdp:/usr/include/x86_64-linux-gnu/libswscale/version_major.h
 mdp:/usr/include/x86_64-linux-gnu/sys/cdefs.h
 mdp:/usr/include/x86_64-linux-gnu/sys/ioctl.h
 mdp:/usr/include/x86_64-linux-gnu/sys/ipc.h
 mdp:/usr/include/x86_64-linux-gnu/sys/select.h
 mdp:/usr/include/x86_64-linux-gnu/sys/sem.h
 mdp:/usr/include/x86_64-linux-gnu/sys/shm.h
 mdp:/usr/include/x86_64-linux-gnu/sys/socket.h
 mdp:/usr/include/x86_64-linux-gnu/sys/stat.h
 mdp:/usr/include/x86_64-linux-gnu/sys/time.h
 mdp:/usr/include/x86_64-linux-gnu/sys/ttydefaults.h
 mdp:/usr/include/x86_64-linux-gnu/sys/types.h
 mdp:/usr/include/x86_64-linux-gnu/sys/ucontext.h
 mdp:/usr/include/x86_64-linux-gnu/sys/wait.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/__stdarg___gnuc_va_list.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/__stddef_max_align_t.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/__stddef_null.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/__stddef_nullptr_t.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/__stddef_offsetof.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/__stddef_ptrdiff_t.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/__stddef_size_t.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/__stddef_wchar_t.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/__wmmintrin_aes.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/__wmmintrin_pclmul.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/adcintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/adxintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/amxcomplexintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/amxfp16intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/amxintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx2intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512bf16intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512bitalgintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512bwintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512cdintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512dqintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512erintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512fintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512fp16intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512ifmaintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512ifmavlintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512pfintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vbmi2intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vbmiintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vbmivlintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vlbf16intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vlbitalgintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vlbwintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vlcdintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vldqintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vlfp16intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vlintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vlvbmi2intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vlvnniintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vlvp2intersectintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vnniintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vp2intersectintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vpopcntdqintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avx512vpopcntdqvlintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avxifmaintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avxintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avxneconvertintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avxvnniint16intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avxvnniint8intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/avxvnniintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/bmi2intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/bmiintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/cetintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/cldemoteintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/clflushoptintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/clwbintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/cmpccxaddintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/crc32intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/emmintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/enqcmdintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/f16cintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/float.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/fmaintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/fxsrintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/gfniintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/hresetintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/immintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/invpcidintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/keylockerintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/lzcntintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/mm_malloc.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/mmintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/movdirintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/pconfigintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/pkuintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/pmmintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/popcntintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/prfchiintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/ptwriteintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/raointintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/rdseedintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/rtmintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/serializeintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/sgxintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/sha512intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/shaintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/sm3intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/sm4intrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/smmintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/stdarg.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/stdbool.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/stddef.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/tmmintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/tsxldtrkintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/uintrintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/usermsrintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/vaesintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/vpclmulqdqintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/waitpkgintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/wbnoinvdintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/wmmintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/x86gprintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/xmmintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/xsavecintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/xsaveintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/xsaveoptintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/xsavesintrin.h
 mdp:/usr/lib/llvm-18/lib/clang/18/include/xtestintrin.h
 mdp:/usr/local/include/ndi/Processing.NDI.DynamicLoad.h
 mdp:/usr/local/include/ndi/Processing.NDI.Find.h
 mdp:/usr/local/include/ndi/Processing.NDI.FrameSync.h
 mdp:/usr/local/include/ndi/Processing.NDI.Lib.cplusplus.h
 mdp:/usr/local/include/ndi/Processing.NDI.Lib.h
 mdp:/usr/local/include/ndi/Processing.NDI.Recv.ex.h
 mdp:/usr/local/include/ndi/Processing.NDI.Recv.h
 mdp:/usr/local/include/ndi/Processing.NDI.Routing.h
 mdp:/usr/local/include/ndi/Processing.NDI.Send.h
 mdp:/usr/local/include/ndi/Processing.NDI.compat.h
 mdp:/usr/local/include/ndi/Processing.NDI.deprecated.h
 mdp:/usr/local/include/ndi/Processing.NDI.structs.h
 mdp:/usr/local/include/ndi/Processing.NDI.utilities.h
