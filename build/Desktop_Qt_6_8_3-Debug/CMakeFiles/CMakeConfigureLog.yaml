
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake:233 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Linux - 6.8.0-51-generic - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/clang++ 
      Build flags: -DQT_QML_DEBUG
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is Clang, found in:
        /home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/3.28.3/CompilerIdCXX/a.out
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-A8Z5Fk"
      binary: "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-A8Z5Fk"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-NOTFOUND"
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-A8Z5Fk'
        
        Run Build Command(s): /usr/bin/ninja -v cmTC_9bfd0
        [1/2] /usr/bin/clang++   -DQT_QML_DEBUG    -v -MD -MT CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp
        Ubuntu clang version 18.1.8 (9ubuntu1~24.04)
        Target: x86_64-pc-linux-gnu
        Thread model: posix
        InstalledDir: /usr/bin
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/11
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/12
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/9
        Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13
        Candidate multilib: .;@m64
        Selected multilib: .;@m64
        Found CUDA installation: /usr/local/cuda, version 12.3
         (in-process)
         "/usr/lib/llvm-18/bin/clang" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -fmath-errno -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu x86-64 -tune-cpu generic -debugger-tuning=gdb -fdebug-compilation-dir=/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-A8Z5Fk -v -fcoverage-compilation-dir=/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-A8Z5Fk -resource-dir /usr/lib/llvm-18/lib/clang/18 -dependency-file CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D QT_QML_DEBUG -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13 -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13 -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward -internal-isystem /usr/lib/llvm-18/lib/clang/18/include -internal-isystem /usr/local/include -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include -internal-externc-isystem /usr/include/x86_64-linux-gnu -internal-externc-isystem /include -internal-externc-isystem /usr/include -fdeprecated-macro -ferror-limit 19 -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fcxx-exceptions -fexceptions -dwarf-debug-flags "/usr/lib/llvm-18/bin/clang --driver-mode=g++ -D QT_QML_DEBUG -v -MD -MT CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp" -faddrsig -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -x c++ /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp
        clang -cc1 version 18.1.8 based upon LLVM 18.1.8 default target x86_64-pc-linux-gnu
        ignoring nonexistent directory "/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include"
        ignoring nonexistent directory "/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13
         /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13
         /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward
         /usr/lib/llvm-18/lib/clang/18/include
         /usr/local/include
         /usr/include/x86_64-linux-gnu
         /usr/include
        End of search list.
        [2/2] : && /usr/bin/clang++ -DQT_QML_DEBUG -v CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_9bfd0   && :
        Ubuntu clang version 18.1.8 (9ubuntu1~24.04)
        Target: x86_64-pc-linux-gnu
        Thread model: posix
        InstalledDir: /usr/bin
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/11
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/12
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13
        Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/9
        Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13
        Candidate multilib: .;@m64
        Selected multilib: .;@m64
        Found CUDA installation: /usr/local/cuda, version 12.3
         "/usr/bin/ld" --hash-style=gnu --build-id --eh-frame-hdr -m elf_x86_64 -pie -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_9bfd0 /lib/x86_64-linux-gnu/Scrt1.o /lib/x86_64-linux-gnu/crti.o /usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o -L/usr/bin/../lib/gcc/x86_64-linux-gnu/13 -L/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/lib -L/usr/lib CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o /lib/x86_64-linux-gnu/crtn.o
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13]
          add: [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13]
          add: [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward]
          add: [/usr/lib/llvm-18/lib/clang/18/include]
          add: [/usr/local/include]
          add: [/usr/include/x86_64-linux-gnu]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13] ==> [/usr/include/c++/13]
        collapse include dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13] ==> [/usr/include/x86_64-linux-gnu/c++/13]
        collapse include dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward] ==> [/usr/include/c++/13/backward]
        collapse include dir [/usr/lib/llvm-18/lib/clang/18/include] ==> [/usr/lib/llvm-18/lib/clang/18/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/include/c++/13;/usr/include/x86_64-linux-gnu/c++/13;/usr/include/c++/13/backward;/usr/lib/llvm-18/lib/clang/18/include;/usr/local/include;/usr/include/x86_64-linux-gnu;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:159 (message)"
      - "/usr/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-A8Z5Fk']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/bin/ninja -v cmTC_9bfd0]
        ignore line: [[1/2] /usr/bin/clang++   -DQT_QML_DEBUG    -v -MD -MT CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Ubuntu clang version 18.1.8 (9ubuntu1~24.04)]
        ignore line: [Target: x86_64-pc-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /usr/bin]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/11]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/12]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/9]
        ignore line: [Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13]
        ignore line: [Candidate multilib: .]
        ignore line: [@m64]
        ignore line: [Selected multilib: .]
        ignore line: [@m64]
        ignore line: [Found CUDA installation: /usr/local/cuda  version 12.3]
        ignore line: [ (in-process)]
        ignore line: [ "/usr/lib/llvm-18/bin/clang" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -fmath-errno -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu x86-64 -tune-cpu generic -debugger-tuning=gdb -fdebug-compilation-dir=/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-A8Z5Fk -v -fcoverage-compilation-dir=/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-A8Z5Fk -resource-dir /usr/lib/llvm-18/lib/clang/18 -dependency-file CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D QT_QML_DEBUG -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13 -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13 -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward -internal-isystem /usr/lib/llvm-18/lib/clang/18/include -internal-isystem /usr/local/include -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include -internal-externc-isystem /usr/include/x86_64-linux-gnu -internal-externc-isystem /include -internal-externc-isystem /usr/include -fdeprecated-macro -ferror-limit 19 -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fcxx-exceptions -fexceptions -dwarf-debug-flags "/usr/lib/llvm-18/bin/clang --driver-mode=g++ -D QT_QML_DEBUG -v -MD -MT CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp" -faddrsig -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -x c++ /usr/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang -cc1 version 18.1.8 based upon LLVM 18.1.8 default target x86_64-pc-linux-gnu]
        ignore line: [ignoring nonexistent directory "/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../x86_64-linux-gnu/include"]
        ignore line: [ignoring nonexistent directory "/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13]
        ignore line: [ /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/x86_64-linux-gnu/c++/13]
        ignore line: [ /usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../include/c++/13/backward]
        ignore line: [ /usr/lib/llvm-18/lib/clang/18/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include/x86_64-linux-gnu]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [[2/2] : && /usr/bin/clang++ -DQT_QML_DEBUG -v CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_9bfd0   && :]
        ignore line: [Ubuntu clang version 18.1.8 (9ubuntu1~24.04)]
        ignore line: [Target: x86_64-pc-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /usr/bin]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/11]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/12]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13]
        ignore line: [Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/9]
        ignore line: [Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/13]
        ignore line: [Candidate multilib: .]
        ignore line: [@m64]
        ignore line: [Selected multilib: .]
        ignore line: [@m64]
        ignore line: [Found CUDA installation: /usr/local/cuda  version 12.3]
        link line: [ "/usr/bin/ld" --hash-style=gnu --build-id --eh-frame-hdr -m elf_x86_64 -pie -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_9bfd0 /lib/x86_64-linux-gnu/Scrt1.o /lib/x86_64-linux-gnu/crti.o /usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o -L/usr/bin/../lib/gcc/x86_64-linux-gnu/13 -L/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/lib -L/usr/lib CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o /lib/x86_64-linux-gnu/crtn.o]
          arg [/usr/bin/ld] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [-pie] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_9bfd0] ==> ignore
          arg [/lib/x86_64-linux-gnu/Scrt1.o] ==> obj [/lib/x86_64-linux-gnu/Scrt1.o]
          arg [/lib/x86_64-linux-gnu/crti.o] ==> obj [/lib/x86_64-linux-gnu/crti.o]
          arg [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o] ==> obj [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o]
          arg [-L/usr/bin/../lib/gcc/x86_64-linux-gnu/13] ==> dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13]
          arg [-L/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64] ==> dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64]
          arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
          arg [-L/lib/../lib64] ==> dir [/lib/../lib64]
          arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
          arg [-L/usr/lib/../lib64] ==> dir [/usr/lib/../lib64]
          arg [-L/lib] ==> dir [/lib]
          arg [-L/usr/lib] ==> dir [/usr/lib]
          arg [CMakeFiles/cmTC_9bfd0.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o] ==> obj [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o]
          arg [/lib/x86_64-linux-gnu/crtn.o] ==> obj [/lib/x86_64-linux-gnu/crtn.o]
        collapse obj [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtbeginS.o] ==> [/usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o]
        collapse obj [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/crtendS.o] ==> [/usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o]
        collapse library dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13] ==> [/usr/lib/gcc/x86_64-linux-gnu/13]
        collapse library dir [/usr/bin/../lib/gcc/x86_64-linux-gnu/13/../../../../lib64] ==> [/usr/lib64]
        collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
        collapse library dir [/lib/../lib64] ==> [/lib64]
        collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/../lib64] ==> [/usr/lib64]
        collapse library dir [/lib] ==> [/lib]
        collapse library dir [/usr/lib] ==> [/usr/lib]
        implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
        implicit objs: [/lib/x86_64-linux-gnu/Scrt1.o;/lib/x86_64-linux-gnu/crti.o;/usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o;/usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o;/lib/x86_64-linux-gnu/crtn.o]
        implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/13;/usr/lib64;/lib/x86_64-linux-gnu;/lib64;/usr/lib/x86_64-linux-gnu;/lib;/usr/lib]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/usr/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "/usr/share/cmake-3.28/Modules/FindThreads.cmake:99 (CHECK_CXX_SOURCE_COMPILES)"
      - "/usr/share/cmake-3.28/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6Dependencies.cmake:34 (_qt_internal_find_third_party_dependencies)"
      - "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6Config.cmake:146 (include)"
      - "CMakeLists.txt:14 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-TnSNgD"
      binary: "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-TnSNgD"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-NOTFOUND"
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-TnSNgD'
        
        Run Build Command(s): /usr/bin/ninja -v cmTC_3f4a1
        [1/2] /usr/bin/clang++ -DCMAKE_HAVE_LIBC_PTHREAD  -DQT_QML_DEBUG  -std=gnu++17 -MD -MT CMakeFiles/cmTC_3f4a1.dir/src.cxx.o -MF CMakeFiles/cmTC_3f4a1.dir/src.cxx.o.d -o CMakeFiles/cmTC_3f4a1.dir/src.cxx.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-TnSNgD/src.cxx
        [2/2] : && /usr/bin/clang++ -DQT_QML_DEBUG  CMakeFiles/cmTC_3f4a1.dir/src.cxx.o -o cmTC_3f4a1   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/usr/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:45 (include)"
      - "/usr/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:145 (find_dependency)"
      - "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake:45 (_qt_internal_find_qt_dependencies)"
      - "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake:43 (include)"
      - "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/Qt6Config.cmake:196 (find_package)"
      - "CMakeLists.txt:15 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-wfI5zX"
      binary: "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-wfI5zX"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-NOTFOUND"
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/3rdparty/kwin;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-wfI5zX'
        
        Run Build Command(s): /usr/bin/ninja -v cmTC_099fd
        [1/2] /usr/bin/clang++ -DHAVE_STDATOMIC  -DQT_QML_DEBUG  -std=gnu++17 -MD -MT CMakeFiles/cmTC_099fd.dir/src.cxx.o -MF CMakeFiles/cmTC_099fd.dir/src.cxx.o.d -o CMakeFiles/cmTC_099fd.dir/src.cxx.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-wfI5zX/src.cxx
        [2/2] : && /usr/bin/clang++ -DQT_QML_DEBUG  CMakeFiles/cmTC_099fd.dir/src.cxx.o -o cmTC_099fd   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/share/cmake-3.28/Modules/CheckSymbolExists.cmake:140 (try_compile)"
      - "/usr/share/cmake-3.28/Modules/CheckSymbolExists.cmake:70 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "/usr/share/cmake-3.28/Modules/FindOpenCL.cmake:59 (CHECK_SYMBOL_EXISTS)"
      - "/usr/share/cmake-3.28/Modules/FindOpenCL.cmake:100 (_FIND_OPENCL_VERSION)"
      - "libgoal/libgoal-avrouter/CMakeLists.txt:6 (find_package)"
    checks:
      - "Looking for CL_VERSION_3_0"
    directories:
      source: "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-53G7mk"
      binary: "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-53G7mk"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-NOTFOUND"
      CMAKE_CXX_FLAGS: "-DQT_QML_DEBUG"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/3rdparty/kwin;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/3rdparty/kwin;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "OPENCL_VERSION_3_0"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-53G7mk'
        
        Run Build Command(s): /usr/bin/ninja -v cmTC_d45cc
        [1/2] /usr/bin/clang++   -DQT_QML_DEBUG  -std=gnu++17 -MD -MT CMakeFiles/cmTC_d45cc.dir/CheckSymbolExists.cxx.o -MF CMakeFiles/cmTC_d45cc.dir/CheckSymbolExists.cxx.o.d -o CMakeFiles/cmTC_d45cc.dir/CheckSymbolExists.cxx.o -c /home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/CMakeFiles/CMakeScratch/TryCompile-53G7mk/CheckSymbolExists.cxx
        [2/2] : && /usr/bin/clang++ -DQT_QML_DEBUG  CMakeFiles/cmTC_d45cc.dir/CheckSymbolExists.cxx.o -o cmTC_d45cc   && :
        
      exitCode: 0
...
