set(qml_import_scanner_imports_count 28)
set(qml_import_scanner_import_0 "CLASSNAME;QtQuick2Plugin;LINKTARGET;Qt6::qtquick2plugin;NAME;QtQuick;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick;PLUGIN;qtquick2plugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/;RELATIVEPATH;QtQuick;TYPE;module;")
set(qml_import_scanner_import_1 "CLASSNAME;QtQmlPlugin;LINKTARGET;Qt6::qmlplugin;NAME;QtQml;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQml;PLUGIN;qmlplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQml/;RELATIVEPATH;QtQml;TYPE;module;")
set(qml_import_scanner_import_2 "NAME;QML;PATH;/opt/Qt/6.8.3/gcc_64/qml/QML;PREFER;:/qt-project.org/imports/QML/;RELATIVEPATH;QML;TYPE;module;")
set(qml_import_scanner_import_3 "CLASSNAME;QtQmlModelsPlugin;LINKTARGET;Qt6::modelsplugin;NAME;QtQml.Models;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQml/Models;PLUGIN;modelsplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQml/Models/;RELATIVEPATH;QtQml/Models;TYPE;module;")
set(qml_import_scanner_import_4 "CLASSNAME;QtQmlWorkerScriptPlugin;LINKTARGET;Qt6::workerscriptplugin;NAME;QtQml.WorkerScript;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQml/WorkerScript;PLUGIN;workerscriptplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQml/WorkerScript/;RELATIVEPATH;QtQml/WorkerScript;TYPE;module;")
set(qml_import_scanner_import_5 "CLASSNAME;QtQuick_WindowPlugin;LINKTARGET;Qt6::quickwindow;NAME;QtQuick.Window;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Window;PLUGIN;quickwindowplugin;PREFER;:/qt-project.org/imports/QtQuick/Window/;RELATIVEPATH;QtQuick/Window;TYPE;module;")
set(qml_import_scanner_import_6 "CLASSNAME;QtQuickControls2Plugin;LINKTARGET;Qt6::qtquickcontrols2plugin;NAME;QtQuick.Controls;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls;PLUGIN;qtquickcontrols2plugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/;RELATIVEPATH;QtQuick/Controls;TYPE;module;")
set(qml_import_scanner_import_7 "CLASSNAME;QtQuickControls2FusionStylePlugin;COMPONENTS;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/ApplicationWindow.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/BusyIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/Button.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/CheckBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/CheckDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/ComboBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/DelayButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/Dial.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/Dialog.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/DialogButtonBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/Drawer.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/Frame.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/GroupBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/HorizontalHeaderView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/ItemDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/Label.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/Menu.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/MenuBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/MenuBarItem.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/MenuItem.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/MenuSeparator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/Page.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/PageIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/Pane.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/Popup.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/ProgressBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/RadioButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/RadioDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/RangeSlider.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/RoundButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/ScrollBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/ScrollIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/ScrollView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/SelectionRectangle.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/Slider.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/SpinBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/SplitView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/SwipeDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/Switch.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/SwitchDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/TabBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/TabButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/TextArea.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/TextField.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/ToolBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/ToolButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/ToolSeparator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/ToolTip.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/TreeViewDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/Tumbler.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2fusionstyleplugin;NAME;QtQuick.Controls.Fusion;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion;PLUGIN;qtquickcontrols2fusionstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Fusion/;RELATIVEPATH;QtQuick/Controls/Fusion;TYPE;module;")
set(qml_import_scanner_import_8 "CLASSNAME;QtQuickControls2MaterialStylePlugin;COMPONENTS;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/ApplicationWindow.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/BusyIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/Button.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/CheckBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/CheckDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/ComboBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/DelayButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/Dial.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/Dialog.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/DialogButtonBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/Drawer.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/Frame.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/GroupBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/HorizontalHeaderView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/ItemDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/Label.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/Menu.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/MenuBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/MenuBarItem.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/MenuItem.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/MenuSeparator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/Page.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/PageIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/Pane.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/Popup.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/ProgressBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/RadioButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/RadioDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/RangeSlider.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/RoundButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/ScrollBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/ScrollIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/ScrollView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/SelectionRectangle.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/Slider.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/SpinBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/SplitView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/StackView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/SwipeDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/SwipeView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/Switch.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/SwitchDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/TabBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/TabButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/TextArea.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/TextField.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/ToolBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/ToolButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/ToolSeparator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/ToolTip.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/TreeViewDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/Tumbler.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2materialstyleplugin;NAME;QtQuick.Controls.Material;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material;PLUGIN;qtquickcontrols2materialstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Material/;RELATIVEPATH;QtQuick/Controls/Material;TYPE;module;")
set(qml_import_scanner_import_9 "CLASSNAME;QtQuickControls2ImagineStylePlugin;COMPONENTS;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/ApplicationWindow.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/BusyIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/Button.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/CheckBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/CheckDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/ComboBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/DelayButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/Dial.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/Dialog.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/DialogButtonBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/Drawer.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/Frame.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/GroupBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/HorizontalHeaderView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/ItemDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/Label.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/Menu.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/MenuItem.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/MenuSeparator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/Page.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/PageIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/Pane.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/Popup.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/ProgressBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/RadioButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/RadioDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/RangeSlider.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/RoundButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/ScrollBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/ScrollIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/ScrollView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/SelectionRectangle.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/Slider.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/SpinBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/SplitView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/StackView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/SwipeDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/SwipeView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/Switch.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/SwitchDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/TabBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/TabButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/TextArea.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/TextField.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/ToolBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/ToolButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/ToolSeparator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/ToolTip.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/Tumbler.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2imaginestyleplugin;NAME;QtQuick.Controls.Imagine;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine;PLUGIN;qtquickcontrols2imaginestyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Imagine/;RELATIVEPATH;QtQuick/Controls/Imagine;TYPE;module;")
set(qml_import_scanner_import_10 "CLASSNAME;QtQuickControls2UniversalStylePlugin;COMPONENTS;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/ApplicationWindow.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/BusyIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/Button.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/CheckBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/CheckDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/ComboBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/DelayButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/Dial.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/Dialog.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/DialogButtonBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/Drawer.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/Frame.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/GroupBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/HorizontalHeaderView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/ItemDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/Label.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/Menu.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/MenuBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/MenuBarItem.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/MenuItem.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/MenuSeparator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/Page.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/PageIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/Pane.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/Popup.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/ProgressBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/RadioButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/RadioDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/RangeSlider.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/RoundButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/ScrollBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/ScrollIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/ScrollView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/SelectionRectangle.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/Slider.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/SpinBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/SplitView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/StackView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/SwipeDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/Switch.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/SwitchDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/TabBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/TabButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/TextArea.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/TextField.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/ToolBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/ToolButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/ToolSeparator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/ToolTip.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/Tumbler.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2universalstyleplugin;NAME;QtQuick.Controls.Universal;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal;PLUGIN;qtquickcontrols2universalstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Universal/;RELATIVEPATH;QtQuick/Controls/Universal;TYPE;module;")
set(qml_import_scanner_import_11 "CLASSNAME;QtQuickControls2FluentWinUI3StylePlugin;COMPONENTS;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/ApplicationWindow.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/BusyIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/Button.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/CheckBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/CheckDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/ComboBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/Config.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/DelayButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/Dialog.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/DialogButtonBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/FocusFrame.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/Frame.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/GroupBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/ItemDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/Menu.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/MenuBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/MenuBarItem.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/MenuItem.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/MenuSeparator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/PageIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/Popup.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/ProgressBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/RadioButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/RadioDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/RangeSlider.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/RoundButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/Slider.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/SpinBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/StyleImage.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/SwipeDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/Switch.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/SwitchDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/TabBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/TabButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/TextArea.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/TextField.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/ToolBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/ToolButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/ToolSeparator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/ToolTip.qml;LINKTARGET;Qt6::qtquickcontrols2fluentwinui3styleplugin;NAME;QtQuick.Controls.FluentWinUI3;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3;PLUGIN;qtquickcontrols2fluentwinui3styleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/FluentWinUI3/;RELATIVEPATH;QtQuick/Controls/FluentWinUI3;TYPE;module;")
set(qml_import_scanner_import_12 "NAME;QtQuick.Controls.Windows;TYPE;module;")
set(qml_import_scanner_import_13 "NAME;QtQuick.Controls.macOS;TYPE;module;")
set(qml_import_scanner_import_14 "NAME;QtQuick.Controls.iOS;TYPE;module;")
set(qml_import_scanner_import_15 "CLASSNAME;QtQuickControls2BasicStylePlugin;COMPONENTS;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/AbstractButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Action.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/ActionGroup.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/ApplicationWindow.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/BusyIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Button.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/ButtonGroup.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Calendar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/CalendarModel.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/CheckBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/CheckDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/ComboBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Container.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Control.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/DayOfWeekRow.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/DelayButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Dial.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Dialog.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/DialogButtonBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Drawer.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Frame.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/GroupBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/HorizontalHeaderView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/ItemDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Label.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Menu.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/MenuBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/MenuBarItem.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/MenuItem.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/MenuSeparator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/MonthGrid.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Page.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/PageIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Pane.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Popup.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/ProgressBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/RadioButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/RadioDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/RangeSlider.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/RoundButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/ScrollBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/ScrollIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/ScrollView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/SelectionRectangle.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Slider.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/SpinBox.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/SplitView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/StackView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/SwipeDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/SwipeView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Switch.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/SwitchDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/TabBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/TabButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/TextArea.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/TextField.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/ToolBar.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/ToolButton.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/ToolSeparator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/ToolTip.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/TreeViewDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/Tumbler.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/VerticalHeaderView.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/WeekNumberColumn.qml;LINKTARGET;Qt6::qtquickcontrols2basicstyleplugin;NAME;QtQuick.Controls.Basic;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic;PLUGIN;qtquickcontrols2basicstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Basic/;RELATIVEPATH;QtQuick/Controls/Basic;TYPE;module;")
set(qml_import_scanner_import_16 "CLASSNAME;QtQuickTemplates2Plugin;LINKTARGET;Qt6::qtquicktemplates2plugin;NAME;QtQuick.Templates;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Templates;PLUGIN;qtquicktemplates2plugin;PREFER;:/qt-project.org/imports/QtQuick/Templates/;RELATIVEPATH;QtQuick/Templates;TYPE;module;")
set(qml_import_scanner_import_17 "CLASSNAME;QtQuickControls2ImplPlugin;LINKTARGET;Qt6::qtquickcontrols2implplugin;NAME;QtQuick.Controls.impl;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/impl;PLUGIN;qtquickcontrols2implplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/impl/;RELATIVEPATH;QtQuick/Controls/impl;TYPE;module;")
set(qml_import_scanner_import_18 "CLASSNAME;QtQuickControls2FusionStyleImplPlugin;COMPONENTS;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/impl/ButtonPanel.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/impl/CheckIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/impl/RadioIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/impl/SliderGroove.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/impl/SliderHandle.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/impl/SwitchIndicator.qml;LINKTARGET;Qt6::qtquickcontrols2fusionstyleimplplugin;NAME;QtQuick.Controls.Fusion.impl;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Fusion/impl;PLUGIN;qtquickcontrols2fusionstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Fusion/impl/;RELATIVEPATH;QtQuick/Controls/Fusion/impl;TYPE;module;")
set(qml_import_scanner_import_19 "CLASSNAME;QtQuickControls2MaterialStyleImplPlugin;COMPONENTS;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/impl/BoxShadow.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/impl/CheckIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/impl/CursorDelegate.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/impl/ElevationEffect.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/impl/RadioIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/impl/RectangularGlow.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/impl/RoundedElevationEffect.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/impl/SliderHandle.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/impl/SwitchIndicator.qml;LINKTARGET;Qt6::qtquickcontrols2materialstyleimplplugin;NAME;QtQuick.Controls.Material.impl;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Material/impl;PLUGIN;qtquickcontrols2materialstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Material/impl/;RELATIVEPATH;QtQuick/Controls/Material/impl;TYPE;module;")
set(qml_import_scanner_import_20 "CLASSNAME;QtQuickControls2ImagineStyleImplPlugin;COMPONENTS;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/impl/OpacityMask.qml;LINKTARGET;Qt6::qtquickcontrols2imaginestyleimplplugin;NAME;QtQuick.Controls.Imagine.impl;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Imagine/impl;PLUGIN;qtquickcontrols2imaginestyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Imagine/impl/;RELATIVEPATH;QtQuick/Controls/Imagine/impl;TYPE;module;")
set(qml_import_scanner_import_21 "CLASSNAME;QtQuickControls2UniversalStyleImplPlugin;COMPONENTS;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/impl/CheckIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/impl/RadioIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/impl/SwitchIndicator.qml;LINKTARGET;Qt6::qtquickcontrols2universalstyleimplplugin;NAME;QtQuick.Controls.Universal.impl;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Universal/impl;PLUGIN;qtquickcontrols2universalstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Universal/impl/;RELATIVEPATH;QtQuick/Controls/Universal/impl;TYPE;module;")
set(qml_import_scanner_import_22 "CLASSNAME;QtQuickControls2FluentWinUI3StyleImplPlugin;COMPONENTS;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/impl/ButtonBackground.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/impl/CheckIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/impl/RadioIndicator.qml;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/impl/SwitchIndicator.qml;LINKTARGET;Qt6::qtquickcontrols2fluentwinui3styleimplplugin;NAME;QtQuick.Controls.FluentWinUI3.impl;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/FluentWinUI3/impl;PLUGIN;qtquickcontrols2fluentwinui3styleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/FluentWinUI3/impl/;RELATIVEPATH;QtQuick/Controls/FluentWinUI3/impl;TYPE;module;")
set(qml_import_scanner_import_23 "CLASSNAME;QtQuickEffectsPlugin;LINKTARGET;Qt6::effectsplugin;NAME;QtQuick.Effects;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Effects;PLUGIN;effectsplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Effects/;RELATIVEPATH;QtQuick/Effects;TYPE;module;")
set(qml_import_scanner_import_24 "CLASSNAME;QtQuickLayoutsPlugin;LINKTARGET;Qt6::qquicklayoutsplugin;NAME;QtQuick.Layouts;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Layouts;PLUGIN;qquicklayoutsplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Layouts/;RELATIVEPATH;QtQuick/Layouts;TYPE;module;")
set(qml_import_scanner_import_25 "CLASSNAME;QmlShapesPlugin;LINKTARGET;Qt6::qmlshapesplugin;NAME;QtQuick.Shapes;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Shapes;PLUGIN;qmlshapesplugin;PREFER;:/qt-project.org/imports/QtQuick/Shapes/;RELATIVEPATH;QtQuick/Shapes;TYPE;module;")
set(qml_import_scanner_import_26 "CLASSNAME;QtQuickControls2BasicStyleImplPlugin;LINKTARGET;Qt6::qtquickcontrols2basicstyleimplplugin;NAME;QtQuick.Controls.Basic.impl;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtQuick/Controls/Basic/impl;PLUGIN;qtquickcontrols2basicstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Basic/impl/;RELATIVEPATH;QtQuick/Controls/Basic/impl;TYPE;module;")
set(qml_import_scanner_import_27 "CLASSNAME;QMultimediaQuickModule;COMPONENTS;/opt/Qt/6.8.3/gcc_64/qml/QtMultimedia/Video.qml;LINKTARGET;Qt6::quickmultimedia;NAME;QtMultimedia;PATH;/opt/Qt/6.8.3/gcc_64/qml/QtMultimedia;PLUGIN;quickmultimediaplugin;PREFER;:/qt-project.org/imports/QtMultimedia/;RELATIVEPATH;QtMultimedia;TYPE;module;")

