{"artifacts": [{"path": "libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_resources_1.dir/./.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp.o"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "__qt_propagate_generated_resource", "_qt_internal_process_resource", "qt6_add_resources", "qt_add_resources", "set_property", "_qt_internal_copy_dependency_properties", "__qt_internal_propagate_object_library", "target_compile_definitions", "target_include_directories"], "files": ["/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "libgoal/libgoal-avrouter/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 4, "file": 1, "line": 36, "parent": 0}, {"command": 3, "file": 0, "line": 458, "parent": 1}, {"command": 2, "file": 0, "line": 406, "parent": 2}, {"command": 1, "file": 0, "line": 2547, "parent": 3}, {"command": 0, "file": 0, "line": 2036, "parent": 4}, {"command": 7, "file": 0, "line": 2080, "parent": 4}, {"command": 6, "file": 0, "line": 1978, "parent": 6}, {"command": 5, "file": 0, "line": 2906, "parent": 7}, {"command": 5, "file": 0, "line": 2906, "parent": 7}, {"command": 8, "file": 0, "line": 2049, "parent": 4}, {"command": 9, "file": 0, "line": 2052, "parent": 4}, {"command": 5, "file": 0, "line": 2906, "parent": 7}, {"command": 5, "file": 0, "line": 2906, "parent": 7}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fcolor-diagnostics"}, {"backtrace": 8, "fragment": "-fPIC"}], "defines": [{"backtrace": 9, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 10, "define": "QT_CORE_LIB"}, {"backtrace": 9, "define": "QT_GUI_LIB"}, {"backtrace": 9, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 9, "define": "QT_NETWORK_LIB"}, {"backtrace": 9, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter"}, {"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter"}, {"backtrace": 11, "path": "/opt/Qt/6.8.3/gcc_64/include/QtCore"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/include"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-avrouter/include"}, {"backtrace": 12, "path": "/opt/Qt/6.8.3/gcc_64/include/QtMultimedia"}, {"backtrace": 12, "path": "/opt/Qt/6.8.3/gcc_64/include/QtGui"}, {"backtrace": 12, "path": "/opt/Qt/6.8.3/gcc_64/include/QtNetwork"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include"}, {"backtrace": 12, "path": "/opt/Qt/6.8.3/gcc_64/include/QtConcurrent"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include"}, {"backtrace": 12, "path": "/opt/Qt/6.8.3/gcc_64/include/QtCore/6.8.3"}, {"backtrace": 12, "path": "/opt/Qt/6.8.3/gcc_64/include/QtCore/6.8.3/QtCore"}, {"backtrace": 12, "path": "/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/6.8.3"}, {"backtrace": 12, "path": "/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/6.8.3/QtMultimedia"}, {"backtrace": 12, "path": "/opt/Qt/6.8.3/gcc_64/include/QtGui/6.8.3"}, {"backtrace": 12, "path": "/opt/Qt/6.8.3/gcc_64/include/QtGui/6.8.3/QtGui"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include"}, {"backtrace": 12, "path": "/opt/Qt/6.8.3/gcc_64/include/QtWidgets"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-sdi/include"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video"}, {"backtrace": 12, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-smemory-video/include"}, {"backtrace": 11, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include"}, {"backtrace": 11, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/mkspecs/linux-g++"}], "language": "CXX", "languageStandard": {"backtraces": [13, 13], "standard": "17"}, "sourceIndexes": [0]}], "id": "GSS_libgoal_avrouter_resources_1::@610245c7e5910b70db46", "name": "GSS_libgoal_avrouter_resources_1", "paths": {"build": "libgoal-avrouter", "source": "libgoal/libgoal-avrouter"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "path": "build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/.qt/rcc/qrc_GSS_libgoal_avrouter_init.cpp", "sourceGroupIndex": 0}], "type": "OBJECT_LIBRARY"}