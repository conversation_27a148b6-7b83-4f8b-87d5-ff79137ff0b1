{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 9, 10, 11, 12], "hasInstallRule": true, "jsonFile": "directory-.-Debug-e2383e491dce564cb0c2.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]}, {"build": "libgoal", "jsonFile": "directory-libgoal-Debug-9d0ff3bccd0d8d1a246d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "libgoal"}, {"build": "libgoal-ndi", "childIndexes": [3, 4, 5, 8], "jsonFile": "directory-libgoal-ndi-Debug-34859441f47e3f43c708.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 2, "source": "libgoal/libgoal-ndi", "targetIndexes": [11, 12, 13]}, {"build": "libgoal-videoframe", "jsonFile": "directory-libgoal-videoframe-Debug-1309eb80b927c2445157.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 3, "source": "libgoal/libgoal-videoframe", "targetIndexes": [23, 24, 25]}, {"build": "libgoal-utils", "jsonFile": "directory-libgoal-utils-Debug-07b608341c359a3a3d67.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 4, "source": "libgoal/libgoal-utils", "targetIndexes": [20, 21, 22]}, {"build": "libgoal-ndi/NDIStream_send_test_app", "childIndexes": [6, 7], "jsonFile": "directory-libgoal-ndi.NDIStream_send_test_app-Debug-85e6ec79151aafa8b63a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 5, "source": "libgoal/libgoal-ndi/NDIStream_send_test_app", "targetIndexes": [29, 30, 31]}, {"build": "libgoal-libav", "jsonFile": "directory-libgoal-libav-Debug-52662cca0608a1863458.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 5, "projectIndex": 6, "source": "libgoal/libgoal-libav", "targetIndexes": [8, 9, 10]}, {"build": "libgoal-smemory-video", "jsonFile": "directory-libgoal-smemory-video-Debug-e223693788a3ecf2c7ac.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 5, "projectIndex": 7, "source": "libgoal/libgoal-smemory-video", "targetIndexes": [17, 18, 19]}, {"build": "libgoal-ndi/recv_test_app", "jsonFile": "directory-libgoal-ndi.recv_test_app-Debug-ad708c96f26ff926da08.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 8, "source": "libgoal/libgoal-ndi/recv_test_app", "targetIndexes": [45, 46, 47]}, {"build": "libgoal-sdi", "jsonFile": "directory-libgoal-sdi-Debug-5118cf0663713067f8ae.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 9, "source": "libgoal/libgoal-sdi", "targetIndexes": [14, 15, 16]}, {"build": "libgoal-devices", "jsonFile": "directory-libgoal-devices-Debug-014f5eff0ea2a37927db.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 10, "source": "libgoal/libgoal-devices", "targetIndexes": [5, 6, 7]}, {"build": "libgoal-avrouter", "jsonFile": "directory-libgoal-avrouter-Debug-2050b8a657a7bedb4072.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 11, "source": "libgoal/libgoal-avrouter", "targetIndexes": [0, 1, 2, 3, 4]}, {"build": "libgoal-widgets", "jsonFile": "directory-libgoal-widgets-Debug-3ff3cb590b0cfa5796e7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 12, "source": "libgoal/libgoal-widgets", "targetIndexes": [26, 27, 28]}], "name": "Debug", "projects": [{"childIndexes": [1, 2, 9, 10, 11, 12], "directoryIndexes": [0], "name": "av-routing-and-mixing", "targetIndexes": [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]}, {"directoryIndexes": [1], "name": "LibGoal", "parentIndex": 0}, {"childIndexes": [3, 4, 5, 8], "directoryIndexes": [2], "name": "GSS_libgoal_ndi", "parentIndex": 0, "targetIndexes": [11, 12, 13]}, {"directoryIndexes": [3], "name": "GSS_libgoal_videoframe", "parentIndex": 2, "targetIndexes": [23, 24, 25]}, {"directoryIndexes": [4], "name": "GSS_libgoal_utils", "parentIndex": 2, "targetIndexes": [20, 21, 22]}, {"childIndexes": [6, 7], "directoryIndexes": [5], "name": "NDIStream_send_test_app", "parentIndex": 2, "targetIndexes": [29, 30, 31]}, {"directoryIndexes": [6], "name": "GSS_libgoal_libav", "parentIndex": 5, "targetIndexes": [8, 9, 10]}, {"directoryIndexes": [7], "name": "GSS_libgoal_smemory-video", "parentIndex": 5, "targetIndexes": [17, 18, 19]}, {"directoryIndexes": [8], "name": "recv_test_app", "parentIndex": 2, "targetIndexes": [45, 46, 47]}, {"directoryIndexes": [9], "name": "GSS_libgoal_sdi", "parentIndex": 0, "targetIndexes": [14, 15, 16]}, {"directoryIndexes": [10], "name": "GSS_libgoal_devices", "parentIndex": 0, "targetIndexes": [5, 6, 7]}, {"directoryIndexes": [11], "name": "GSS_libgoal_avrouter", "parentIndex": 0, "targetIndexes": [0, 1, 2, 3, 4]}, {"directoryIndexes": [12], "name": "GSS_libgoal_widgets", "parentIndex": 0, "targetIndexes": [26, 27, 28]}], "targets": [{"directoryIndex": 11, "id": "GSS_libgoal_avrouter::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter-Debug-65e9e91c1567e72aa7ba.json", "name": "GSS_libgoal_avrouter", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_autogen::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_autogen-Debug-fc3499a23da853c02dd5.json", "name": "GSS_libgoal_avrouter_autogen", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_autogen_timestamp_deps::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_autogen_timestamp_deps-Debug-2b9754322b63f19b1184.json", "name": "GSS_libgoal_avrouter_autogen_timestamp_deps", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_other_files::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_other_files-Debug-7cf7f336f98423be2a53.json", "name": "GSS_libgoal_avrouter_other_files", "projectIndex": 11}, {"directoryIndex": 11, "id": "GSS_libgoal_avrouter_resources_1::@610245c7e5910b70db46", "jsonFile": "target-GSS_libgoal_avrouter_resources_1-Debug-af6f899f609d799cb7f5.json", "name": "GSS_libgoal_avrouter_resources_1", "projectIndex": 11}, {"directoryIndex": 10, "id": "GSS_libgoal_devices::@a4acf01ffd64b3c25b5e", "jsonFile": "target-GSS_libgoal_devices-Debug-5d1a0dd423ec7cbd8cdc.json", "name": "GSS_libgoal_devices", "projectIndex": 10}, {"directoryIndex": 10, "id": "GSS_libgoal_devices_autogen::@a4acf01ffd64b3c25b5e", "jsonFile": "target-GSS_libgoal_devices_autogen-Debug-3ff5811002bbb953a2f9.json", "name": "GSS_libgoal_devices_autogen", "projectIndex": 10}, {"directoryIndex": 10, "id": "GSS_libgoal_devices_autogen_timestamp_deps::@a4acf01ffd64b3c25b5e", "jsonFile": "target-GSS_libgoal_devices_autogen_timestamp_deps-Debug-b8c1562f29c2e59b0934.json", "name": "GSS_libgoal_devices_autogen_timestamp_deps", "projectIndex": 10}, {"directoryIndex": 6, "id": "GSS_libgoal_libav::@d4925a3eb7b45a3a7728", "jsonFile": "target-GSS_libgoal_libav-Debug-7c3c7f87e01dd7e7d1cb.json", "name": "GSS_libgoal_libav", "projectIndex": 6}, {"directoryIndex": 6, "id": "GSS_libgoal_libav_autogen::@d4925a3eb7b45a3a7728", "jsonFile": "target-GSS_libgoal_libav_autogen-Debug-d7cd9debcb138fc2f776.json", "name": "GSS_libgoal_libav_autogen", "projectIndex": 6}, {"directoryIndex": 6, "id": "GSS_libgoal_libav_autogen_timestamp_deps::@d4925a3eb7b45a3a7728", "jsonFile": "target-GSS_libgoal_libav_autogen_timestamp_deps-Debug-41b46e1e8f94e014d5d1.json", "name": "GSS_libgoal_libav_autogen_timestamp_deps", "projectIndex": 6}, {"directoryIndex": 2, "id": "GSS_libgoal_ndi::@e8405e6f8fa3070b957e", "jsonFile": "target-GSS_libgoal_ndi-Debug-aa5af9b2f89972387d7c.json", "name": "GSS_libgoal_ndi", "projectIndex": 2}, {"directoryIndex": 2, "id": "GSS_libgoal_ndi_autogen::@e8405e6f8fa3070b957e", "jsonFile": "target-GSS_libgoal_ndi_autogen-Debug-588d944148eee39a5543.json", "name": "GSS_libgoal_ndi_autogen", "projectIndex": 2}, {"directoryIndex": 2, "id": "GSS_libgoal_ndi_autogen_timestamp_deps::@e8405e6f8fa3070b957e", "jsonFile": "target-GSS_libgoal_ndi_autogen_timestamp_deps-Debug-280b783f28febc653394.json", "name": "GSS_libgoal_ndi_autogen_timestamp_deps", "projectIndex": 2}, {"directoryIndex": 9, "id": "GSS_libgoal_sdi::@83de4cecf005b7ef38bf", "jsonFile": "target-GSS_libgoal_sdi-Debug-61cf6f57d780f03add18.json", "name": "GSS_libgoal_sdi", "projectIndex": 9}, {"directoryIndex": 9, "id": "GSS_libgoal_sdi_autogen::@83de4cecf005b7ef38bf", "jsonFile": "target-GSS_libgoal_sdi_autogen-Debug-f953d0df5f0b339873f5.json", "name": "GSS_libgoal_sdi_autogen", "projectIndex": 9}, {"directoryIndex": 9, "id": "GSS_libgoal_sdi_autogen_timestamp_deps::@83de4cecf005b7ef38bf", "jsonFile": "target-GSS_libgoal_sdi_autogen_timestamp_deps-Debug-2b68adde3b593aadaa85.json", "name": "GSS_libgoal_sdi_autogen_timestamp_deps", "projectIndex": 9}, {"directoryIndex": 7, "id": "GSS_libgoal_smemory-video::@ce80f594daf5fb6bfe42", "jsonFile": "target-GSS_libgoal_smemory-video-Debug-bbb222d93b110559e9f4.json", "name": "GSS_libgoal_smemory-video", "projectIndex": 7}, {"directoryIndex": 7, "id": "GSS_libgoal_smemory-video_autogen::@ce80f594daf5fb6bfe42", "jsonFile": "target-GSS_libgoal_smemory-video_autogen-Debug-738fcb13c5aedb510534.json", "name": "GSS_libgoal_smemory-video_autogen", "projectIndex": 7}, {"directoryIndex": 7, "id": "GSS_libgoal_smemory-video_autogen_timestamp_deps::@ce80f594daf5fb6bfe42", "jsonFile": "target-GSS_libgoal_smemory-video_autogen_timestamp_deps-Debug-f959640c4aeaba0ffe9a.json", "name": "GSS_libgoal_smemory-video_autogen_timestamp_deps", "projectIndex": 7}, {"directoryIndex": 4, "id": "GSS_libgoal_utils::@6c8cb491c3014fb511f8", "jsonFile": "target-GSS_libgoal_utils-Debug-bf923915a11cbf9e563c.json", "name": "GSS_libgoal_utils", "projectIndex": 4}, {"directoryIndex": 4, "id": "GSS_libgoal_utils_autogen::@6c8cb491c3014fb511f8", "jsonFile": "target-GSS_libgoal_utils_autogen-Debug-ababe69236dffaafcf1d.json", "name": "GSS_libgoal_utils_autogen", "projectIndex": 4}, {"directoryIndex": 4, "id": "GSS_libgoal_utils_autogen_timestamp_deps::@6c8cb491c3014fb511f8", "jsonFile": "target-GSS_libgoal_utils_autogen_timestamp_deps-Debug-00629587e580cc69ee06.json", "name": "GSS_libgoal_utils_autogen_timestamp_deps", "projectIndex": 4}, {"directoryIndex": 3, "id": "GSS_libgoal_videoframe::@2bc16d3bd896b78e6cbd", "jsonFile": "target-GSS_libgoal_videoframe-Debug-6338e09b21976dd825b2.json", "name": "GSS_libgoal_videoframe", "projectIndex": 3}, {"directoryIndex": 3, "id": "GSS_libgoal_videoframe_autogen::@2bc16d3bd896b78e6cbd", "jsonFile": "target-GSS_libgoal_videoframe_autogen-Debug-34b05cee3a7f252b4aeb.json", "name": "GSS_libgoal_videoframe_autogen", "projectIndex": 3}, {"directoryIndex": 3, "id": "GSS_libgoal_videoframe_autogen_timestamp_deps::@2bc16d3bd896b78e6cbd", "jsonFile": "target-GSS_libgoal_videoframe_autogen_timestamp_deps-Debug-b57c1d6156d5489fb93a.json", "name": "GSS_libgoal_videoframe_autogen_timestamp_deps", "projectIndex": 3}, {"directoryIndex": 12, "id": "GSS_libgoal_widgets::@1a6ca22365c285a707f8", "jsonFile": "target-GSS_libgoal_widgets-Debug-4e3cb99958fdba6738b3.json", "name": "GSS_libgoal_widgets", "projectIndex": 12}, {"directoryIndex": 12, "id": "GSS_libgoal_widgets_autogen::@1a6ca22365c285a707f8", "jsonFile": "target-GSS_libgoal_widgets_autogen-Debug-4c3b746a680084dcf525.json", "name": "GSS_libgoal_widgets_autogen", "projectIndex": 12}, {"directoryIndex": 12, "id": "GSS_libgoal_widgets_autogen_timestamp_deps::@1a6ca22365c285a707f8", "jsonFile": "target-GSS_libgoal_widgets_autogen_timestamp_deps-Debug-81c70e87dcbfb4c6cd07.json", "name": "GSS_libgoal_widgets_autogen_timestamp_deps", "projectIndex": 12}, {"directoryIndex": 5, "id": "NDIStream_send_test_app::@6a6cc8600da1c3617a31", "jsonFile": "target-NDIStream_send_test_app-Debug-dc4d80be520c9a85aa91.json", "name": "NDIStream_send_test_app", "projectIndex": 5}, {"directoryIndex": 5, "id": "NDIStream_send_test_app_autogen::@6a6cc8600da1c3617a31", "jsonFile": "target-NDIStream_send_test_app_autogen-Debug-3253bd949999ea1f3951.json", "name": "NDIStream_send_test_app_autogen", "projectIndex": 5}, {"directoryIndex": 5, "id": "NDIStream_send_test_app_autogen_timestamp_deps::@6a6cc8600da1c3617a31", "jsonFile": "target-NDIStream_send_test_app_autogen_timestamp_deps-Debug-d70bc34b55d29358352b.json", "name": "NDIStream_send_test_app_autogen_timestamp_deps", "projectIndex": 5}, {"directoryIndex": 0, "id": "all_aotstats::@6890427a1f51a3e7e1df", "jsonFile": "target-all_aotstats-Debug-665711746567286daf26.json", "name": "all_aotstats", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmllint::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmllint-Debug-67af2d8ae8f08bef4f84.json", "name": "all_qmllint", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmllint_json::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmllint_json-Debug-50e9f49e87fb74d73780.json", "name": "all_qmllint_json", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmllint_module::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmllint_module-Debug-e4efb4000ed718751334.json", "name": "all_qmllint_module", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmltyperegistrations::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmltyperegistrations-Debug-fff56f35cf7e7ee48514.json", "name": "all_qmltyperegistrations", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing-Debug-38618076a3d775362ebd.json", "name": "av-routing-and-mixing", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_autogen-Debug-1e4eae1df05b419b8d4a.json", "name": "av-routing-and-mixing_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_autogen_timestamp_deps-Debug-e98c592a723ca3ebce0a.json", "name": "av-routing-and-mixing_autogen_timestamp_deps", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmlimportscan::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmlimportscan-Debug-51eaaa3728eb7122810e.json", "name": "av-routing-and-mixing_qmlimportscan", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmllint::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmllint-Debug-b5544413a8309a7a9a06.json", "name": "av-routing-and-mixing_qmllint", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmllint_json::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmllint_json-Debug-123daa55fc9796bd7189.json", "name": "av-routing-and-mixing_qmllint_json", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmllint_module::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmllint_module-Debug-058a598164e8d89e0e42.json", "name": "av-routing-and-mixing_qmllint_module", "projectIndex": 0}, {"directoryIndex": 0, "id": "av-routing-and-mixing_qmltyperegistration::@6890427a1f51a3e7e1df", "jsonFile": "target-av-routing-and-mixing_qmltyperegistration-Debug-5d6a5549742af1bd9dea.json", "name": "av-routing-and-mixing_qmltyperegistration", "projectIndex": 0}, {"directoryIndex": 8, "id": "recv_test_app::@899e4ac02e105eff13be", "jsonFile": "target-recv_test_app-Debug-1d0818f9d023dcf0b763.json", "name": "recv_test_app", "projectIndex": 8}, {"directoryIndex": 8, "id": "recv_test_app_autogen::@899e4ac02e105eff13be", "jsonFile": "target-recv_test_app_autogen-Debug-5ea024bc676e0b2d55cb.json", "name": "recv_test_app_autogen", "projectIndex": 8}, {"directoryIndex": 8, "id": "recv_test_app_autogen_timestamp_deps::@899e4ac02e105eff13be", "jsonFile": "target-recv_test_app_autogen_timestamp_deps-Debug-ab719bd37fe2f11290e8.json", "name": "recv_test_app_autogen_timestamp_deps", "projectIndex": 8}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug", "source": "/home/<USER>/PROJECTS/av-routing-and-mixing"}, "version": {"major": 2, "minor": 6}}