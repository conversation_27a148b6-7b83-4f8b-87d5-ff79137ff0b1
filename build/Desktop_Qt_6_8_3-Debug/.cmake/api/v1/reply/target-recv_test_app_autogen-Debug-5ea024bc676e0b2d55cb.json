{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["libgoal/libgoal-ndi/recv_test_app/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"id": "recv_test_app_autogen_timestamp_deps::@899e4ac02e105eff13be"}], "id": "recv_test_app_autogen::@899e4ac02e105eff13be", "isGeneratorProvided": true, "name": "recv_test_app_autogen", "paths": {"build": "libgoal-ndi/recv_test_app", "source": "libgoal/libgoal-ndi/recv_test_app"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/libgoal-ndi/recv_test_app/CMakeFiles/recv_test_app_autogen.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/libgoal-ndi/recv_test_app/recv_test_app_autogen/timestamp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}