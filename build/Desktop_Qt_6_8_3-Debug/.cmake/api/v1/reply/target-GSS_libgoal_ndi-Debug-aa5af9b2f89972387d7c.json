{"archive": {}, "artifacts": [{"path": "libgoal-ndi/libGSS_libgoal_ndi.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_include_directories"], "files": ["libgoal/libgoal-ndi/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 1, "file": 0, "line": 51, "parent": 0}, {"command": 2, "file": 0, "line": 46, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG -g -std=gnu++17 -fcolor-diagnostics"}, {"backtrace": 2, "fragment": "-fPIC"}], "defines": [{"backtrace": 2, "define": "QT_CONCURRENT_LIB"}, {"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_MULTIMEDIA_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}], "includes": [{"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-ndi"}, {"path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi"}, {"backtrace": 0, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/build/Desktop_Qt_6_8_3-Debug/libgoal-ndi/GSS_libgoal_ndi_autogen/include"}, {"backtrace": 3, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-ndi/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-videoframe/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-libav/include"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils"}, {"backtrace": 2, "path": "/home/<USER>/PROJECTS/av-routing-and-mixing/libgoal/libgoal-utils/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include/QtCore/6.8.3"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include/QtCore/6.8.3/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/mkspecs/linux-g++"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/6.8.3"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include/QtMultimedia/6.8.3/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include/QtGui/6.8.3"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include/QtGui/6.8.3/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include/QtMultimedia"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include/QtNetwork"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt/6.8.3/gcc_64/include/QtConcurrent"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0, 2, 4, 6]}], "dependencies": [{"backtrace": 2, "id": "GSS_libgoal_videoframe::@2bc16d3bd896b78e6cbd"}, {"backtrace": 2, "id": "GSS_libgoal_utils::@6c8cb491c3014fb511f8"}, {"id": "GSS_libgoal_ndi_autogen_timestamp_deps::@e8405e6f8fa3070b957e"}, {"backtrace": 0, "id": "GSS_libgoal_ndi_autogen::@e8405e6f8fa3070b957e"}], "id": "GSS_libgoal_ndi::@e8405e6f8fa3070b957e", "name": "GSS_libgoal_ndi", "nameOnDisk": "libGSS_libgoal_ndi.a", "paths": {"build": "libgoal-ndi", "source": "libgoal/libgoal-ndi"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4, 6]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 5]}, {"name": "", "sourceIndexes": [7]}, {"name": "CMake Rules", "sourceIndexes": [8]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/libgoal-ndi/GSS_libgoal_ndi_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-ndi/include/ndi/ndifinder.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-ndi/include/ndi/ndifinder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-ndi/include/ndi/ndireceiver.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-ndi/include/ndi/ndireceiver.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "libgoal/libgoal-ndi/include/ndi/ndisender.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "libgoal/libgoal-ndi/include/ndi/ndisender.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/libgoal-ndi/GSS_libgoal_ndi_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/libgoal-ndi/GSS_libgoal_ndi_autogen/timestamp.rule", "sourceGroupIndex": 3}], "type": "STATIC_LIBRARY"}