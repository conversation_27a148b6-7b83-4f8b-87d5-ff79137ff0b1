{"backtrace": 6, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_add_phony_target", "_qt_internal_add_all_qmllint_target", "_qt_internal_target_enable_qmllint", "qt6_target_qml_sources", "qt6_add_qml_module", "add_dependencies", "_qt_internal_add_phony_target_dependencies"], "files": ["/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "CMakeLists.txt"], "nodes": [{"file": 2}, {"command": 5, "file": 2, "line": 58, "parent": 0}, {"command": 4, "file": 1, "line": 916, "parent": 1}, {"command": 3, "file": 1, "line": 2927, "parent": 2}, {"command": 2, "file": 1, "line": 1559, "parent": 3}, {"command": 1, "file": 1, "line": 1620, "parent": 4}, {"command": 0, "file": 0, "line": 299, "parent": 5}, {"command": 7, "file": 1, "line": 1624, "parent": 4}, {"command": 6, "file": 0, "line": 328, "parent": 7}]}, "dependencies": [{"backtrace": 8, "id": "av-routing-and-mixing_qmllint_module::@6890427a1f51a3e7e1df"}], "folder": {"name": "/QmlLinter"}, "id": "all_qmllint_module::@6890427a1f51a3e7e1df", "name": "all_qmllint_module", "paths": {"build": ".", "source": "."}, "sources": [], "type": "UTILITY"}