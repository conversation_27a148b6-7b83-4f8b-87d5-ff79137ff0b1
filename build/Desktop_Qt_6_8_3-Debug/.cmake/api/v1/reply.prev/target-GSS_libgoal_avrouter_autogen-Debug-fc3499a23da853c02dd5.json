{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["libgoal/libgoal-avrouter/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"id": "GSS_libgoal_avrouter_autogen_timestamp_deps::@610245c7e5910b70db46"}], "id": "GSS_libgoal_avrouter_autogen::@610245c7e5910b70db46", "isGeneratorProvided": true, "name": "GSS_libgoal_avrouter_autogen", "paths": {"build": "libgoal-avrouter", "source": "libgoal/libgoal-avrouter"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/CMakeFiles/GSS_libgoal_avrouter_autogen.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/libgoal-avrouter/GSS_libgoal_avrouter_autogen/timestamp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}