{"backtrace": 3, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_deferred_aotstats_setup"], "files": ["/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake:1105:EVAL", "CMakeLists.txt"], "nodes": [{"file": 2}, {"file": 2, "line": -1, "parent": 0}, {"command": 1, "file": 1, "line": 1, "parent": 1}, {"command": 0, "file": 0, "line": 1212, "parent": 2}]}, "id": "all_aotstats::@6890427a1f51a3e7e1df", "name": "all_aotstats", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 3, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/CMakeFiles/all_aotstats", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/CMakeFiles/all_aotstats.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/.rcc/qmlcache/all_aotstats.aotstats.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}