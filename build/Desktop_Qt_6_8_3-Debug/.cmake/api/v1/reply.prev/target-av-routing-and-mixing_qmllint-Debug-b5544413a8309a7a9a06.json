{"backtrace": 4, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_target_enable_qmllint", "qt6_target_qml_sources", "qt6_add_qml_module"], "files": ["/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 58, "parent": 0}, {"command": 2, "file": 0, "line": 916, "parent": 1}, {"command": 1, "file": 0, "line": 2927, "parent": 2}, {"command": 0, "file": 0, "line": 1474, "parent": 3}]}, "dependencies": [{"id": "all_qmltyperegistrations::@6890427a1f51a3e7e1df"}], "folder": {"name": "/QmlLinter"}, "id": "av-routing-and-mixing_qmllint::@6890427a1f51a3e7e1df", "name": "av-routing-and-mixing_qmllint", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 4, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/CMakeFiles/av-routing-and-mixing_qmllint", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/CMakeFiles/av-routing-and-mixing_qmllint.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}