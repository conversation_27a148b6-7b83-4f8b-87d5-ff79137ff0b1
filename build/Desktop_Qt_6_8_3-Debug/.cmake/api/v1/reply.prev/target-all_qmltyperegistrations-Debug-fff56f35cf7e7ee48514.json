{"backtrace": 3, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_qml_type_registration", "qt6_add_qml_module", "add_dependencies"], "files": ["/opt/Qt/6.8.3/gcc_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 2, "file": 1, "line": 58, "parent": 0}, {"command": 1, "file": 0, "line": 796, "parent": 1}, {"command": 0, "file": 0, "line": 3832, "parent": 2}, {"command": 3, "file": 0, "line": 3835, "parent": 2}]}, "dependencies": [{"backtrace": 4, "id": "av-routing-and-mixing_qmltyperegistration::@6890427a1f51a3e7e1df"}], "id": "all_qmltyperegistrations::@6890427a1f51a3e7e1df", "name": "all_qmltyperegistrations", "paths": {"build": ".", "source": "."}, "sources": [], "type": "UTILITY"}