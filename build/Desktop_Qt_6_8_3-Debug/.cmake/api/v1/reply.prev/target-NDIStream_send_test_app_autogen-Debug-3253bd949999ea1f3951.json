{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["libgoal/libgoal-ndi/NDIStream_send_test_app/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"id": "NDIStream_send_test_app_autogen_timestamp_deps::@6a6cc8600da1c3617a31"}], "id": "NDIStream_send_test_app_autogen::@6a6cc8600da1c3617a31", "isGeneratorProvided": true, "name": "NDIStream_send_test_app_autogen", "paths": {"build": "libgoal-ndi/NDIStream_send_test_app", "source": "libgoal/libgoal-ndi/NDIStream_send_test_app"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/libgoal-ndi/NDIStream_send_test_app/CMakeFiles/NDIStream_send_test_app_autogen.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3-Debug/libgoal-ndi/NDIStream_send_test_app/NDIStream_send_test_app_autogen/timestamp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}