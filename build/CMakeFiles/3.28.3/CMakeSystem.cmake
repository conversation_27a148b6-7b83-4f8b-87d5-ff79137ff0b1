set(CMAKE_HOST_SYSTEM "Linux-6.8.0-51-generic")
set(CMAKE_HOST_SYSTEM_NAME "Linux")
set(CMAKE_HOST_SYSTEM_VERSION "6.8.0-51-generic")
set(CMAKE_HOST_SYSTEM_PROCESSOR "x86_64")

include("/opt/Qt/6.8.2/gcc_64/lib/cmake/Qt6/qt.toolchain.cmake")

set(CMAKE_SYSTEM "Linux-6.8.0-51-generic")
set(CMAKE_SYSTEM_NAME "Linux")
set(CMAKE_SYSTEM_VERSION "6.8.0-51-generic")
set(CMAKE_SYSTEM_PROCESSOR "x86_64")

set(CMAKE_CROSSCOMPILING "FALSE")

set(CMAKE_SYSTEM_LOADED 1)
